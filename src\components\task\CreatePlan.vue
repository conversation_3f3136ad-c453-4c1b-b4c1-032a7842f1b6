<template>
  <div class="create-plan-wrapper">
    <span class="header">创建任务</span>
    <div class="content" style="position: relative;">
      <a-form ref="valueRef"
              :layout="formState.layout"
              :hideRequiredMark="true"
              :rules="rules"
              :model="planBody"
              labelAlign="left"
      >
        <a-form-item label="任务名称" name="name" :labelCol="{ span: 23 }">
          <a-input style="background-color: #232323;"
                   placeholder="请输入计划名称"
                   v-model:value="planBody.name"
          />
        </a-form-item>

        <!-- 航线 -->
        <a-form-item label="执行航线" name="file_id">
          <a-input style="background-color: #232323;"
                   placeholder="请输入执行航线"
                   v-model:value="fileName"
                   @click="openDialog"
          />
          <!-- <a-select placeholder="选择航线"
                    v-model:value="planBody.file_id"
                    @change="handleSelectChange"
                    style="width: 100%;background: black;"
          >
            <a-select-option :value="wayline.id" v-for="(wayline, index) in waylinesData.data" :key="index">
              {{ wayline.name }}
            </a-select-option>
          </a-select> -->
        </a-form-item>

        <!-- 设备 -->
        <a-form-item label="选择设备" name="dock_sn">
          <a-select placeholder="选择设备"
                    @change="dockChange" v-model:value="planBody.dock_sn"
                    style="width: 100%;background: black;"
          >
            <a-select-option :value="dock.device_sn" v-for="dock in docksData.data">
              {{ dock.nickname }}
            </a-select-option>
          </a-select>
        </a-form-item>

        <!-- 任务类型 -->
        <a-form-item label="计划时间" class="plan-timer-form-item">
          <div style="white-space: nowrap;">
            <a-radio-group v-model:value="planBody.task_type" button-style="solid">
              <a-radio-button v-for="item in TaskTypeOptions" :value="item.value" :key="item.value">
                {{ item.label }}
              </a-radio-button>
            </a-radio-group>
          </div>
        </a-form-item>

        <!-- 执行日期 -->
        <a-form-item
            label="执行日期"
            v-if="planBody.task_type === TaskType.Timed || planBody.task_type === TaskType.Condition"
            name="select_execute_date"
            :labelCol="{ span: 23 }"
        >
          <a-range-picker
              v-model:value="planBody.select_execute_date"
              :disabledDate="(current: Moment) => current < moment().subtract(1, 'days')"
              format="YYYY-MM-DD"
              :placeholder="['开始时间', '结束时间']"
              style="width: 100%;"
          />
        </a-form-item>

        <!-- 执行时间 -->
        <a-form-item v-if="planBody.task_type === TaskType.Timed || planBody.task_type === TaskType.Condition"
                     label="执行时间"
                     name="select_execute_time"
                     ref="select_execute_time"
                     :labelCol="{ span: 23 }"
                     :autoLink="false"
        >
          <div class="mb10 flex-row flex-align-center flex-justify-around"
               v-for="n in planBody.select_time_number" :key="n"
          >
            <a-time-picker v-model:value="planBody.select_time[n - 1][0]"
                           format="HH:mm:ss"
                           show-time
                           placeholder="开始时间"
                           :style="planBody.task_type === TaskType.Condition ? 'width: 40%' : 'width: 82%'"
                           @change="() => $refs.select_execute_time.onFieldChange()"
            />
            <template v-if="planBody.task_type === TaskType.Condition">
              <div>
                <span style="color: white;">-</span>
              </div>
              <a-time-picker v-model:value="planBody.select_time[n - 1][1]"
                             format="HH:mm:ss"
                             show-time
                             placeholder="结束时间"
                             style="width: 40%;"
              />
            </template>
            <div class="ml5" style="font-size: 18px;">
              <PlusCircleOutlined class="mr5" style="color: #1890ff;" @click="addTime"/>
              <MinusCircleOutlined @click="removeTime"
                                   :style="planBody.select_time_number === 1 ? 'color: gray' : 'color: red;'"
              />
            </div>
          </div>
        </a-form-item>

        <template v-if="planBody.task_type === TaskType.Condition">
          <!-- battery capacity -->
          <a-form-item
              label="电池电量达到时启动任务"
              :labelCol="{ span: 23 }"
              name="min_battery_capacity"
          >
            <a-input-number
                class="width-100"
                v-model:value="planBody.min_battery_capacity"
                :min="50"
                :max="100"
                :formatter="(value: number) => `${value}%`"
                :parser="(value: string) => value.replace('%', '')"
            >
            </a-input-number>
          </a-form-item>
          <!-- storage capacity -->
          <a-form-item
              label="当存储级别达到（MB）时启动任务"
              :labelCol="{ span: 23 }"
              name="storage_capacity"
          >
            <a-select v-model:value="planBody.min_storage_capacity" :dropdown-match-select-width="false" class="width-100">
              <a-select-option value="0">无</a-select-option>
              <a-select-option value="2.5">2.5</a-select-option>
              <a-select-option value="5">5</a-select-option>
              <a-select-option value="7.5">7.5</a-select-option>
            </a-select>
          </a-form-item>
        </template>
        <!-- RTH Altitude Relative to Dock -->
        <a-form-item label="相对机场返航高度 (米)" :labelCol="{ span: 23 }" name="rth_altitude">
          <a-input-number v-model:value="planBody.rth_altitude"
                          :min="20" :max="1500" class="width-100" required
          >
          </a-input-number>
        </a-form-item>
        <!-- Lost Action -->
        <a-form-item label="失联动作" :labelCol="{ span: 23 }" name="out_of_control_action">
          <div style="white-space: nowrap;">
            <a-radio-group v-model:value="planBody.out_of_control_action" button-style="solid">
              <a-radio-button v-for="action in OutOfControlActionOptions"
                              :value="action.value"
                              :key="action.value"
              >
                {{ action.label }}
              </a-radio-button>
            </a-radio-group>
          </div>
        </a-form-item>
        <a-form-item class="width-100">
          <div class="footer" style="position: absolute;bottom: 0;width: 100%;">
            <!--<a-button class="mr10" style="background: #3c3c3c; width: 45%" @click="closePlan">-->
            <!--  取消-->
            <!--</a-button>-->
            <a-button type="primary" style="width: 100%;" @click="onSubmit" :disabled="disabled" v-permission>
              确定
            </a-button>
          </div>
        </a-form-item>
      </a-form>
    </div>

    <el-dialog v-model="isAdd" title="航线" width="900" :close-on-click-modal="false" style="height: 600px;">
      <Airline @onSelectedRowChanged="handleSelectedRowChange"></Airline>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import {computed, reactive, ref} from 'vue';
import {PlusCircleOutlined, MinusCircleOutlined} from '@ant-design/icons-vue';
import {ERouterName} from '@/api/enum/index';
import store from '@/store';
import {WaylineFile} from '@/types/wayline';
import {Device, DEVICE_NAME} from '@/types/device';
import {createPlan, CreatePlan} from '@/api/wayline';
import {getRoot} from '@/root';
import {TaskType, OutOfControlActionOptions, OutOfControlAction, TaskTypeOptions} from '@/types/task';
import moment, {Moment} from 'moment';
import {RuleObject} from 'ant-design-vue/es/form/interface';
import {message} from 'ant-design-vue';
import router from '@/router';
import {getWaylineFiles} from '@/api/wayline';
import {getBindingDevices} from '@/api/manage';
import {EDeviceTypeName, ELocalStorageKey} from '@/types';
import EventBus from '@/event-bus/';
import Airline from '@/views/task/airline.vue';
import { getWorkspaceId } from '@/utils/storage'

const root = getRoot();
const wayline = computed<WaylineFile>(() => {
  return store.state.dock.waylineInfo;
});
const dock = computed<Device>(() => {
  return store.state.dock.dockInfo;
});
const disabled = ref(false);
const formState = reactive({
  layout: 'vertical',
});
const planBody = reactive({
  name: '',
  file_id: '',
  wayline_type: 0,
  // file_id: computed(() => store.state.dock?.waylineInfo.id),
  dock_sn: computed(() => store.state.dock?.dockInfo.device_sn),
  task_type: TaskType.Immediate,
  select_execute_date: [moment(), moment()] as Moment[],
  select_time_number: 1,
  select_time: [[]] as Moment[][],
  rth_altitude: '',
  out_of_control_action: OutOfControlAction.ReturnToHome,
  min_battery_capacity: 90 as number,
  min_storage_capacity: undefined as number | undefined,
});
const valueRef = ref();
const rules = {
  name: [
    {required: true, message: '请输入计划名称'},
    {max: 20, message: '长度应为1到20'},
  ],
  file_id: [{required: true, message: '请选择航线'}],
  dock_sn: [{required: true, message: '请选择设备'}],
  select_execute_time: [
    {
      validator: async (rule: RuleObject, value: Moment[]) => {
        validEndTime();
        validStartTime();
        if (planBody.select_time.length < planBody.select_time_number) {
          throw new Error('请选择时间');
        }
        validOverlapped();
      },
    },
  ],
  select_execute_date: [{required: true, message: '请选择执行日期'}],
  rth_altitude: [
    {
      validator: async (rule: RuleObject, value: string) => {
        if (!/^[0-9]{1,}$/.test(value)) {
          throw new Error('请设置相对机场返航高度');
        }
      },
    },
  ],
  min_battery_capacity: [
    {
      validator: async (rule: RuleObject, value: any) => {
        if (TaskType.Condition === planBody.task_type && !value) {
          throw new Error('请输入电池容量');
        }
      },
    },
  ],
  out_of_control_action: [{required: true, message: '请选择失联动作'}],
};
const pagination = {
  page: 1,
  total: -1,
  page_size: 10,
};
const waylinesData = reactive({
  data: [] as WaylineFile[],
});

//获取航线
function getWaylines() {
  getWaylineFiles(getWorkspaceId(), {
    page: pagination.page,
    page_size: pagination.page_size,
    order_by: 'update_time desc',
  }).then(res => {
    if (res.data.code !== 0) {
      return;
    }
    waylinesData.data = [...waylinesData.data, ...res.data.data.list];
    pagination.total = res.data.data.pagination.total;
    pagination.page = res.data.data.pagination.page;
  })
}

const body = {
  page: 1,
  total: -1,
  page_size: 10,
}

const docksData = reactive({
  data: [] as Device[]
})

//获取设备
async function getDocks() {
  await getBindingDevices(getWorkspaceId(), body, EDeviceTypeName.Dock).then(res => {
    if (res.code !== 0) {
      return
    }
    docksData.data = res.data.list
    body.page = res.data.pagination.page
    body.page_size = res.data.pagination.page_size
    body.total = res.data.pagination.total
  })
}

getWaylines();
getDocks();

function validStartTime() {
  for (let i = 0; i < planBody.select_time.length; i++) {
    if (!planBody.select_time[i][0]) {
      throw new Error('请选择开始时间');
    }
  }
}

function validEndTime(): Error | void {
  if (TaskType.Condition !== planBody.task_type) return;
  for (let i = 0; i < planBody.select_time.length; i++) {
    if (!planBody.select_time[i][1]) {
      throw new Error('请选择结束时间');
    }
    if (
        planBody.select_time[i][0] &&
        planBody.select_time[i][1].isSameOrBefore(planBody.select_time[i][0])
    ) {
      throw new Error('结束时间应晚于开始时间');
    }
  }
}

function validOverlapped(): Error | void {
  if (TaskType.Condition !== planBody.task_type) return;
  const arr = planBody.select_time.slice();
  arr.sort((a, b) => a[0].unix() - b[0].unix());
  arr.forEach((v, i, arr) => {
    if (i > 0 && v[0] < arr[i - 1][1]) {
      throw new Error('重叠时间段.');
    }
  });
}

function onSubmit() {
  valueRef.value.validate().then(() => {
    disabled.value = true;
    const createPlanBody = {...planBody} as unknown as CreatePlan;
    if (planBody.select_execute_date.length === 2) {
      const startDate = moment(planBody.select_execute_date[0]);
      const endDate = moment(planBody.select_execute_date[1]);
      createPlanBody.task_days = [];
      let currentDate = startDate;
      while (currentDate.isSameOrBefore(endDate)) {
        createPlanBody.task_days.push(currentDate.unix());
        currentDate.add(1, 'days');
      }
    }
    createPlanBody.task_periods = [];
    if (TaskType.Immediate !== planBody.task_type) {
      for (let i = 0; i < planBody.select_time.length; i++) {
        const result = [];
        result.push(planBody.select_time[i][0].unix());
        if (TaskType.Condition === planBody.task_type) {
          result.push(planBody.select_time[i][1].unix());
        }
        createPlanBody.task_periods.push(result);
      }
    }
    createPlanBody.rth_altitude = Number(createPlanBody.rth_altitude);
    // if (wayline.value && wayline.value.template_types && wayline.value.template_types.length > 0) {
    //   createPlanBody.wayline_type = wayline.value.template_types[0];
    // }
    createPlanBody.wayline_type = planBody.wayline_type;
    if (createPlanBody.min_storage_capacity == 0 || createPlanBody.min_storage_capacity == undefined) {
      delete createPlanBody.min_storage_capacity
    }
    createPlan(getWorkspaceId(), createPlanBody).then(res => {
      setNull();
      message.success(res.data);
    }).finally(() => {
      setNull();
    });
  }).catch((e: any) => {
    setNull();
  });
}
/**
 * 关闭任务
 */
function setNull() {
  disabled.value = false;
  fileName.value = ''
  planBody.name = ''
  planBody.rth_altitude = ''
  planBody.task_type = null
  planBody.file_id = null
  planBody.min_storage_capacity = null
  planBody.min_battery_capacity = undefined
  planBody.select_time = [[]]
  planBody.select_execute_date = [moment(), moment()]
  planBody.select_time_number = 1
  EventBus.emit('updateplan');
}

function closePlan() {
  router.push('/' + ERouterName.TASK);
}

function handleSelectChange(id) {
  store.state.dock.waylineInfo.id = id;
}

function dockChange(id) {
  store.state.dock.dockInfo.device_sn = id;
}

function addTime() {
  valueRef.value.validateFields(['select_execute_time']).then(() => {
    planBody.select_time_number++;
    planBody.select_time.push([]);
  });
}

function removeTime() {
  if (planBody.select_time_number === 1) return;
  planBody.select_time_number--;
  planBody.select_time.splice(planBody.select_time_number);
}

//接收值
const fileName = ref('');
const isAdd = ref(false);
const canOpenDialog = ref(false); // 控制是否可以打开弹窗
const openDialog = () => {
  if (!canOpenDialog.value) {
    isAdd.value = true;
    // canOpenDialog.value = false; // 标记已选择
  }
};
const handleSelectedRowChange = (id: string, name: string, template_types: number[]) => {
  console.log('template_types', template_types);
  planBody.file_id = id;
  fileName.value = name;
  planBody.wayline_type = template_types[0];
  isAdd.value = false;
  canOpenDialog.value = true; // 标记已选择
  setTimeout(function () {
    canOpenDialog.value = false
  }, 100);
};
</script>

<style lang="scss" scoped>
:deep(.ant-select:not(.ant-select-customize-input) .ant-select-selector) {
  background-color: #232323;
  color: #fff;
}
</style>

<style lang="scss">
.create-plan-wrapper {
  position: relative;
  // background-color: #232323;
  color: #fff;
  padding-bottom: 0;
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;

  .header {
    height: 52px;
    border-bottom: 1px solid #4f4f4f;
    font-weight: 700;
    font-size: 16px;
    padding-left: 10px;
    display: flex;
    align-items: center;
  }

  ::-webkit-scrollbar {
    display: none;
  }

  .content {
    height: calc(100% - 54px);
    overflow-y: auto;
    padding-bottom: 30px;

    form {
      margin: 10px;
    }

    form label,
    input,
    .ant-input,
    .ant-calendar-range-picker-separator,
    .ant-input:hover,
    .ant-time-picker .anticon,
    .ant-calendar-picker .anticon {
      background-color: #232323;
      color: #fff;
    }

    .ant-input-suffix {
      color: #fff;
    }

    .plan-timer-form-item {
      .ant-radio-button-wrapper {
        background-color: #232323;
        color: #fff;
        width: 33%;
        text-align: center;

        &.ant-radio-button-wrapper-checked {
          background-color: #1890ff;
        }
      }
    }
  }

  .footer {
    display: flex;
    padding: 10px 0;

    button {
      width: 45%;
      color: #fff;
      border: 0;
    }
  }
}

.wayline-panel {
  background: #1f2b38;
  margin-left: auto;
  margin-right: auto;
  margin-top: 10px;
  height: 90px;
  width: 95%;
  font-size: 13px;
  border-radius: 2px;
  cursor: pointer;

  .title {
    display: flex;
    color: white;
    flex-direction: row;
    align-items: center;
    height: 30px;
    font-weight: bold;
    margin: 0 10px 0 10px;
  }
}

.panel {
  background: #192a3b;
  margin-left: auto;
  margin-right: auto;
  margin-top: 10px;
  height: 70px;
  width: 95%;
  font-size: 13px;
  border-radius: 2px;
  cursor: pointer;

  .title {
    display: flex;
    color: white;
    flex-direction: row;
    align-items: center;
    height: 30px;
    font-weight: bold;
    margin: 0 10px 0 10px;
  }
}

.cor-096 {
  color: #096dd9;
}
</style>