<template>
  <div class="project-app-wrapper">
    <div class="left">
      <div class="main-content uranus-scrollbar dark" style="position: relative;">
        <CreatePlan />
      </div>
    </div>
    <div style="position: fixed;top: 40px;left: 325px;z-index: 2;">
      <TaskPanel v-if="taskRoute" :item-size="itemSize" @showPlanList="closeList" @AmpJobId="AmpJobId"
        @showAmp="showAmp" @AmpJobVideo="AmpJobVideo" :zoom="8" />
    </div>
    <div>
      <el-button type="primary" class="btn" @click="isShow">
        任务列表-{{ taskRoute ? '关闭' : '展示' }}
      </el-button>
    </div>
  </div>
  <div class="right">
    <div class="map-wrapper">
      <div class="g-map-wrapper">
        <!-- <Amap :wgs84Center="[120.241176, 30.299719]" :placemark="placemark" :take0ffPoint="take0ffPoint" :zoom="14.5"/> -->
        <div v-if="lineArr && lineArr.length > 0" class="leftManageTop">
          <el-row>
            <el-col :span="5">
              <el-button type="primary" class="btn" @click="startAnimation">开始动画</el-button>
            </el-col>
            <el-col :span="5">
              <el-button type="primary" class="btn" @click="pauseAnimation">暂停动画</el-button>
            </el-col>
            <el-col :span="5">
              <el-button type="primary" class="btn" @click="resumeAnimation">继续动画</el-button>
            </el-col>
            <el-col :span="4">
              <!--<el-button type="primary" class="btn" @click="stopAnimation">停止动画</el-button>-->
              <el-select v-model="speed" style="width: 100px;top: 5px;" placeholder="选择倍速"
                @change="handleSelect($event)">
                <el-option :value="'1倍速'">1倍速</el-option>
                <el-option :value="'2倍速'">2倍速</el-option>
                <el-option :value="'3倍速'">3倍速</el-option>
                <el-option :value="'4倍速'">4倍速</el-option>
              </el-select>
            </el-col>
            <el-col :span="5">
              <el-button type="primary" class="btn" @click="toggleFollow">
                {{ isFollowing ? '取消跟随' : '视角跟随' }}
              </el-button>
            </el-col>
            <!-- <el-col :span="4" v-if="getVideoShow">
              <el-button type="primary" class="btn" @click="getVideo">轨迹视频</el-button>
            </el-col> -->
          </el-row>
        </div>
        <div v-if="lineArr && lineArr.length > 0" class="leftManageLeft">
          <div style="margin: 20px;">
            <p>
              <span>经度：</span><span>{{ longitude }}</span>
            </p>
            <p>
              <span>纬度：</span><span>{{ latitude }}</span>
            </p>
            <p>
              <span>高度：</span><span>{{ height }}</span>
            </p>
            <p>
              <span>相对起飞点高度：</span><span>{{ elevation }}</span>
            </p>
            <p>
              <span>水平速度：</span><span>{{ horizontalSpeed }}</span>
            </p>
            <p>
              <span>距离Home点的距离：</span><span>{{ homeDistance }}</span>
            </p>
          </div>
        </div>
        <div v-if="timeData && timeData.distance" class="leftManage">
          <el-row>
            <el-col :span="1"></el-col>
            <el-col :span="7">
              <div style="text-align: center;font-size: 18px;">航线长度</div>
            </el-col>
            <el-col :span="8">
              <div style="text-align: center;font-size: 18px;">预计执行时间</div>
            </el-col>
            <el-col :span="7">
              <div style="text-align: center;font-size: 18px;">航点</div>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="1"></el-col>
            <el-col :span="7">
              <div style="text-align: center;font-size: 18px;">
                {{ timeData && timeData.distance ? timeData.distance + 'm' : '' }}
              </div>
            </el-col>
            <el-col :span="8">
              <div style="text-align: center;font-size: 18px;">
                {{ timeData && timeData.workTime ? timeData.workTime : '' }}
              </div>
            </el-col>
            <el-col :span="7">
              <div style="text-align: center;font-size: 18px;">
                {{ timeData && timeData.pointCount ? timeData.pointCount : '' }}
              </div>
            </el-col>
          </el-row>
        </div>
      </div>
    </div>
  </div>
  <!-- 背景遮罩 -->
  <div v-if="visible" class="modal-overlay" @click="closeModal"></div>
  <!-- 模拟模态框的 div -->
  <div v-if="visible" class="custom-modal">
    <videoAmp :VideoId="jobId" @closeExit="closeExit"></videoAmp>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, provide } from 'vue';
import TaskPanel from '@/components/task/TaskPanel.vue';
import CreatePlan from '@/components/task/CreatePlan.vue';
import Amap from '@/components/djimap/index.vue';
import { wgs84togcj02 } from '@/vendors/coordtransform';
import { getWayLineDetail, getHistoryTrajectory, getCheckFlightMp4 } from '@/api/wayline';
import { getRoot } from '@/root';
import M30 from '@/assets/m30.png';
import videoAmp from '@/views/task/videoAmp.vue';
import store from '@/store';
import M30glb from '@/assets/M30.glb';

let taskRoute = ref(true);
store.commit('MAP_TYPE', false);
store.commit('LAYER_MAP_TYPE', false);

const isShow = () => {
  taskRoute.value = !taskRoute.value
}

const itemSize = ref({ width: 1000, height: 800 });

const handleResize = () => {
  const viewportWidth = window.innerWidth;
  const baseWidth = 130; // 假设基础宽度为800px
  const scale = viewportWidth / baseWidth;

  itemSize.value.width = 100 * scale;
  itemSize.value.height = 100 * scale;
};

provide('itemSize', itemSize);

onMounted(() => {
  handleResize(); // 初始化时调用一次
  window.addEventListener('resize', handleResize);
});

onBeforeUnmount(() => {
  if (marker.value) {
    marker.value.setMap(null); // 移除当前标记
    longitude.value = '';
    latitude.value = '';
    height.value = '';
    elevation.value = '';
    horizontalSpeed.value = '';
    homeDistance.value = '';
  }
  if (polyline.value) {
    polyline.value.setMap(null); // 移除当前路径
  }
  if (passedPolyline.value) {
    passedPolyline.value.setMap(null); // 移除已通过的路径
  }
  marker.value = null; // 将marker声明为全局变量
  polyline.value = null; // 将polyline声明为全局变量
  passedPolyline.value = null; // 将passedPolyline声明为全局变量
  lineArr.value = []; // 初始化路径数组
  uavDetails.value = [];
  placemark.value = [];
  take0ffPoint.value = [];
  timeData.value = {};

  // 清理Cesium相关资源
  if (viewer.value) {
    // 移除事件监听器
    viewer.value.scene.preRender.removeEventListener(updateUAVDetails);

    // 停止动画
    if (clock.value) {
      clock.value.shouldAnimate = false;
    }

    // 取消实体跟随
    viewer.value.trackedEntity = undefined;

    // 移除实体
    if (animationEntity.value) {
      viewer.value.entities.remove(animationEntity.value);
      animationEntity.value = null;
    }

    if (flightPath.value) {
      viewer.value.entities.remove(flightPath.value);
      flightPath.value = null;
    }

    // 清理其他Cesium相关变量
    positionProperty.value = null;
    clock.value = null;
  }

  window.removeEventListener('resize', handleResize);
});

const closeList = (value) => {
  taskRoute.value = value
}

let placemark = ref([])
let take0ffPoint = ref([])
let timeData = ref({})
/* 不能在TaskPanel组件中写函数方法,影响到生命周期 */
const showAmp = (id) => {
  getWayLineDetail(id).then(res => {
    if (res.code !== 0) {
      return;
    } else {
      // 飞行航线转换经纬度
      const arr = res.data.waylineData.placemark.map(item => {
        return item.point.split(',').map(Number);
      });
      placemark.value = arr.map(item => {
        return wgs84togcj02(item[0], item[1]);
      });
      // 有数据-回显地图起飞点
      if (res.data.waylineData.take_off_ref_point && res.data.waylineData.take_off_ref_point.substring(0, 3) != 'NaN') {
        const array = res.data.waylineData.take_off_ref_point.split(',')
        // 起飞点数据处理-取字符串前两个值进行上图
        take0ffPoint.value = [parseFloat(array[1]), parseFloat(array[0])]
      }
      // 飞行航线数据统计
      timeData.value = res.data.wayLineCount;
    }
  })
}
let marker = ref(null); // 将marker声明为全局变量
let polyline = ref(null); // 将polyline声明为全局变量
let passedPolyline = ref(null); // 将passedPolyline声明为全局变量
let lineArr = ref([]); // 初始化路径数组
let uavDetails = ref([]); // 初始化路径数组详情数据
const viewer = ref(null);
const clock = ref(null); // 添加时钟引用
const animationEntity = ref(null); // 添加无人机实体引用
const positionProperty = ref(null); // 添加位置属性引用
const isFollowing = ref(true); // 是否开启视角跟随

//接收参数清除历史轨迹
const AmpJobId = async (id) => {
  speed.value = '1倍速';
  duration.value = 500;
  // 停止并清除上一个轨迹动画
  if (marker.value) {
    marker.value.stopMove(); // 停止当前标记的移动
    marker.value.setMap(null); // 移除当前标记
    longitude.value = '';
    latitude.value = '';
    height.value = '';
    elevation.value = '';
    horizontalSpeed.value = '';
    homeDistance.value = '';
  }
  if (polyline.value) {
    polyline.value.setMap(null); // 移除当前路径
  }
  if (passedPolyline.value) {
    passedPolyline.value.setMap(null); // 移除已通过的路径
  }
  
  // 清理Cesium相关资源
  if (viewer.value) {
    // 移除事件监听器
    viewer.value.scene.preRender.removeEventListener(updateUAVDetails);
    
    // 停止动画
    if (clock.value) {
      clock.value.shouldAnimate = false;
    }
    
    // 取消实体跟随
    viewer.value.trackedEntity = undefined;
    
    // 移除实体
    if (animationEntity.value) {
      viewer.value.entities.remove(animationEntity.value);
      animationEntity.value = null;
    }
    
    if (flightPath.value) {
      viewer.value.entities.remove(flightPath.value);
      flightPath.value = null;
    }
    
    // 清理其他Cesium相关变量
    positionProperty.value = null;
  }
  
  lineArr.value = []; // 清空之前的路径数组
  uavDetails.value = [];
  await getHistoryTrajectory({ jobId: id }).then(data => {
    if (data.data.data != '') {
      data.data.data.forEach(location => {
        lineArr.value.push([location.longitude, location.latitude]);
        uavDetails.value.push(location);
      })
    } else {
      return;
    }
  })

  // 飞行航线回显地图
  viewer.value = getRoot().$viewer;
  createCesiumEntities()
}
const flightPath = ref(null)
function createCesiumEntities() {
  if (!viewer.value || lineArr.value.length === 0) return;

  // 创建时间线数据
  createTimelineData();

  // 创建航线路径
  flightPath.value = viewer.value.entities.add({
    name: 'Flight Path',
    polyline: {
      positions: Cesium.Cartesian3.fromDegreesArrayHeights(
        lineArr.value.flatMap((point, index) => {
          const height = uavDetails.value[index]?.height || 100;
          return [point[0], point[1], height];
        })
      ),
      width: 4,
      material: Cesium.Color.fromCssColorString('#0aed8b')
    }
  });

  // 计算整个飞行路径的边界范围
  const boundingSphere = Cesium.BoundingSphere.fromPoints(
    Cesium.Cartesian3.fromDegreesArrayHeights(
      lineArr.value.flatMap((point, index) => {
        const height = uavDetails.value[index]?.height || 100;
        return [point[0], point[1], height];
      })
    )
  );

  // 使用flyToBoundingSphere方法自动计算最佳视角以查看整个路径
  viewer.value.camera.flyToBoundingSphere(boundingSphere, {
    offset: new Cesium.HeadingPitchRange(0, Cesium.Math.toRadians(-60), boundingSphere.radius * 3),
    duration: 1 // 快速过渡
  });

  // 创建无人机实体
  animationEntity.value = viewer.value.entities.add({
    name: 'UAV',
    position: positionProperty.value,
    orientation: new Cesium.VelocityOrientationProperty(positionProperty.value),
    billboard: {
      image: M30,
      width: 32,
      height: 32,
      scale: 1.0,
      verticalOrigin: Cesium.VerticalOrigin.BOTTOM
    },
    path: {
      resolution: 1,
      material: new Cesium.PolylineGlowMaterialProperty({
        glowPower: 0.2,
        color: Cesium.Color.GREEN
      }),
      width: 3
    }
  });
}

function createTimelineData() {
  const start = Cesium.JulianDate.fromDate(new Date());
  const stop = Cesium.JulianDate.addSeconds(start, lineArr.value.length * (duration.value / 1000), new Cesium.JulianDate());

  // 设置时钟
  viewer.value.clock.startTime = start.clone();
  viewer.value.clock.stopTime = stop.clone();
  viewer.value.clock.currentTime = start.clone();
  viewer.value.clock.clockRange = Cesium.ClockRange.CLAMPED;
  viewer.value.clock.multiplier = 1;

  // 保存时钟引用
  clock.value = viewer.value.clock;

  // 创建位置属性
  positionProperty.value = new Cesium.SampledPositionProperty();

  // 添加每个位置的采样点
  for (let i = 0; i < lineArr.value.length; i++) {
    const time = Cesium.JulianDate.addSeconds(start, i * (duration.value / 1000), new Cesium.JulianDate());
    const position = Cesium.Cartesian3.fromDegrees(
      lineArr.value[i][0],
      lineArr.value[i][1],
      uavDetails.value[i]?.height || 100
    );
    positionProperty.value.addSample(time, position);
  }
}


const speed = ref('1倍速');
const duration = ref(500);  // 轨迹回放时间
//开始
const longitude = ref('');
const latitude = ref('');
const height = ref('');
const elevation = ref('');
const horizontalSpeed = ref('');
const homeDistance = ref('');

function startAnimation() {
  if (!viewer.value || !clock.value) return;

  // 重置时钟到开始位置
  clock.value.currentTime = clock.value.startTime.clone();

  // 设置时钟为运行状态
  clock.value.shouldAnimate = true;

  // 确保无人机实体可见
  if (animationEntity.value) {
    animationEntity.value.show = true;
  }

  // 移除之前的事件监听器（如果有）以避免重复添加
  viewer.value.scene.preRender.removeEventListener(updateUAVDetails);

  // 添加新的事件监听器
  viewer.value.scene.preRender.addEventListener(updateUAVDetails);

  // 启用视角跟随
  if (isFollowing.value && animationEntity.value) {
    enableEntityFollow();
  }
}

function pauseAnimation() {
  if (!clock.value) return;

  // 暂停时钟
  clock.value.shouldAnimate = false;
}

function resumeAnimation() {
  if (!clock.value) return;

  // 恢复时钟运行
  clock.value.shouldAnimate = true;
}

// 重置动画函数 - 完全重置动画状态
function resetAnimation() {
  if (!viewer.value || !clock.value) return;

  // 停止动画
  clock.value.shouldAnimate = false;

  // 重置时钟到开始位置
  clock.value.currentTime = clock.value.startTime.clone();

  // 确保无人机实体可见
  if (animationEntity.value) {
    animationEntity.value.show = true;
  }

  // 重置位置到第一个点
  if (positionProperty.value && lineArr.value.length > 0) {
    const position = Cesium.Cartesian3.fromDegrees(
      lineArr.value[0][0],
      lineArr.value[0][1],
      uavDetails.value[0]?.height || 100
    );

    // 手动更新位置信息
    longitude.value = lineArr.value[0][0].toFixed(6);
    latitude.value = lineArr.value[0][1].toFixed(6);
    height.value = uavDetails.value[0]?.height || '';
    elevation.value = uavDetails.value[0]?.elevation || '';
    horizontalSpeed.value = uavDetails.value[0]?.horizontal_speed || '';
    homeDistance.value = uavDetails.value[0]?.home_distance || '';
  }
}

// 更新无人机详情信息的函数
function updateUAVDetails() {
  if (!positionProperty.value || !viewer.value || !clock.value || uavDetails.value.length === 0) return;

  // 获取当前时间
  const currentTime = clock.value.currentTime;

  // 检查是否到达动画结束时间
  if (Cesium.JulianDate.greaterThanOrEquals(currentTime, clock.value.stopTime)) {
    // 停止动画时确保图标仍然可见
    if (animationEntity.value) {
      animationEntity.value.show = true;
    }
    return;
  }

  // 计算当前索引
  const totalDuration = Cesium.JulianDate.secondsDifference(clock.value.stopTime, clock.value.startTime);
  const elapsedDuration = Cesium.JulianDate.secondsDifference(currentTime, clock.value.startTime);
  const progress = elapsedDuration / totalDuration;
  const index = Math.min(Math.floor(progress * uavDetails.value.length), uavDetails.value.length - 1);

  if (index >= 0 && index < uavDetails.value.length) {
    // 获取当前位置
    const position = positionProperty.value.getValue(currentTime);
    if (position) {
      const cartographic = Cesium.Cartographic.fromCartesian(position);
      longitude.value = Cesium.Math.toDegrees(cartographic.longitude).toFixed(6);
      latitude.value = Cesium.Math.toDegrees(cartographic.latitude).toFixed(6);

      // 更新详情信息
      height.value = uavDetails.value[index].height;
      elevation.value = uavDetails.value[index].elevation;
      horizontalSpeed.value = uavDetails.value[index].horizontal_speed;
      homeDistance.value = uavDetails.value[index].home_distance;
    }
  }
}

function handleSelect(e) {
  // 更新时钟的乘数来控制速度
  const speedMultiplier = parseInt(e.charAt(0));
  if (clock.value) {
    clock.value.multiplier = speedMultiplier;
  }
  // 更新持续时间以便于创建新的轨迹时使用
  duration.value = 500 / speedMultiplier;
}

const visible = ref(false);
const jobId = ref('');
const AmpJobVideo = (id) => {
  jobId.value = '';
  getCheckFlightMp4(id).then(res => {
    if (res.data.code === 0) {
      jobId.value = id;
      visible.value = true;
    } else {
      visible.value = false;
    }
  })
}
const closeExit = () => {
  visible.value = false;
}

// 启用实体跟随
function enableEntityFollow() {
  if (!viewer.value || !animationEntity.value) return;

  // 设置相机跟随无人机实体
  viewer.value.trackedEntity = animationEntity.value;

  // 也可以设置跟随视角的偏移和方向
  // viewer.value.scene.camera.lookAt(
  //   animationEntity.value.position.getValue(viewer.value.clock.currentTime),
  //   new Cesium.HeadingPitchRange(0, -Math.PI/4, 200) // 方位角、俯仰角、距离
  // );
}

// 禁用实体跟随
function disableEntityFollow() {
  if (!viewer.value) return;

  // 取消跟随
  viewer.value.trackedEntity = undefined;
}

// 切换跟随状态
function toggleFollow() {
  isFollowing.value = !isFollowing.value;

  if (isFollowing.value) {
    enableEntityFollow();
  } else {
    disableEntityFollow();
  }
}
</script>

<style lang="scss" scoped>
.route-icon {
  color: #fff;
  font-size: 16px;
}

.btn {
  position: fixed;
  z-index: 1;
  width: 100px;
  margin-top: 6px;
  margin-left: 6px;
}

.right {
  flex-grow: 1;
  position: relative;

  .map-wrapper {
    width: 100%;
    height: 100%;
  }
}

.g-map-wrapper {
  height: 100%;
  width: 100%;

  .g-action-panel {
    position: absolute;
    top: 16px;
    right: 16px;

    .g-action-item {
      width: 28px;
      height: 28px;
      background: white;
      color: rgb(81, 81, 81);
      border-radius: 2px;
      line-height: 28px;
      text-align: center;
      margin-bottom: 2px;
    }

    .g-action-item:hover {
      border: 1px solid rgb(81, 81, 81);
      border-radius: 2px;
    }
  }

  &:deep(.ant-btn) {
    &::after {
      display: none;
    }
  }
}

.leftManageTop {
  position: fixed;
  top: 10%;
  left: 42%;
  color: black;
  width: 600px;
  height: 60px;
  z-index: 1;
}

.leftManageLeft {
  background: #ffffff;
  position: fixed;
  bottom: 5%;
  left: 21%;
  color: black;
  width: 250px;
  z-index: 1;
}

.leftManage {
  position: fixed;
  bottom: 20px;
  left: 48%;
  color: black;
  width: 340px;
  height: 60px;
  z-index: 1;
}

:deep(.el-select__wrapper) {
  height: 35px;

  background-color: #409eff;
}

:deep(.el-select__placeholder) {
  color: #ffffff;
  font-size: 12px;
}

:deep(.el-select__caret) {
  color: #ffffff;
}

:deep(.ant-table-column-title) {
  color: #000000 !important;
}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1;
}

.custom-modal {
  position: fixed;
  top: 5%;
  left: 50%;
  transform: translateX(-50%);
  width: 1300px;
  height: 90%;
  background: white;
  z-index: 3;
  border-radius: 5px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  overflow: hidden;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  background: #f0f0f0;
  border-bottom: 1px solid #ddd;
}

.modal-title {
  font-size: 18px;
  font-weight: bold;
}

.modal-close {
  cursor: pointer;
  font-size: 20px;
}

.modal-body {
  padding: 20px;
}
</style>