{"asset": {"gltfUpAxis": "Z", "version": "0.0"}, "geometricError": 0.43640902638435364, "root": {"boundingVolume": {"box": [103.79068756103516, 185.34854125976562, -154.80462646484375, 64.50151824951172, 0.0, 0.0, 0.0, 64.50147247314453, 0.0, 0.0, 0.0, 24.27611541748047]}, "children": [{"boundingVolume": {"box": [71.5400390625, 153.09681701660156, -148.12020874023438, 32.25004196166992, 0.0, 0.0, 0.0, 32.25009536743164, 0.0, 0.0, 0.0, 17.60063934326172]}, "children": [{"boundingVolume": {"box": [57.28858184814453, 121.80720520019531, -163.7202606201172, 14.242141723632812, 0.0, 0.0, 0.0, 0.9537010192871094, 0.0, 0.0, 0.0, 0.953155517578125]}, "content": {"uri": "pointcloud_034440.pnts"}, "geometricError": 0.10910225659608841, "refine": "ADD"}, {"boundingVolume": {"box": [87.66680908203125, 136.57763671875, -164.230712890625, 16.114887237548828, 0.0, 0.0, 0.0, 15.72292709350586, 0.0, 0.0, 0.0, 1.473114013671875]}, "content": {"uri": "pointcloud_034441.pnts"}, "geometricError": 0.10910225659608841, "refine": "ADD"}, {"boundingVolume": {"box": [45.34228515625, 171.8106689453125, -163.8408966064453, 6.039794921875, 0.0, 0.0, 0.0, 13.524467468261719, 0.0, 0.0, 0.0, 1.08148193359375]}, "content": {"uri": "pointcloud_034442.pnts"}, "geometricError": 0.10910225659608841, "refine": "ADD"}, {"boundingVolume": {"box": [55.416168212890625, 136.96717834472656, -158.08779907226562, 16.120229721069336, 0.0, 0.0, 0.0, 16.118179321289062, 0.0, 0.0, 0.0, 4.514778137207031]}, "content": {"uri": "pointcloud_034444.pnts"}, "geometricError": 0.10910225659608841, "refine": "ADD"}, {"boundingVolume": {"box": [87.66272735595703, 136.97312927246094, -146.63104248046875, 16.116485595703125, 0.0, 0.0, 0.0, 16.114147186279297, 0.0, 0.0, 0.0, 16.121788024902344]}, "content": {"uri": "pointcloud_034445.pnts"}, "geometricError": 0.10910225659608841, "refine": "ADD"}, {"boundingVolume": {"box": [55.440330505371094, 169.22213745117188, -157.51992797851562, 16.08772087097168, 0.0, 0.0, 0.0, 16.11920166015625, 0.0, 0.0, 0.0, 5.230010986328125]}, "content": {"uri": "pointcloud_034446.pnts"}, "geometricError": 0.10910225659608841, "refine": "ADD"}, {"boundingVolume": {"box": [87.66450500488281, 169.22442626953125, -153.90589904785156, 16.12234115600586, 0.0, 0.0, 0.0, 16.12348175048828, 0.0, 0.0, 0.0, 7.2971038818359375]}, "content": {"uri": "pointcloud_034447.pnts"}, "geometricError": 0.10910225659608841, "refine": "ADD"}], "content": {"uri": "pointcloud_03444.pnts"}, "geometricError": 0.21820451319217682, "refine": "ADD"}, {"boundingVolume": {"box": [136.04188537597656, 153.096923828125, -156.82901000976562, 32.250606536865234, 0.0, 0.0, 0.0, 32.250144958496094, 0.0, 0.0, 0.0, 8.976875305175781]}, "children": [{"boundingVolume": {"box": [119.93216705322266, 133.6402130126953, -164.12451171875, 16.107200622558594, 0.0, 0.0, 0.0, 12.79348373413086, 0.0, 0.0, 0.0, 1.3694992065429688]}, "content": {"uri": "pointcloud_034450.pnts"}, "geometricError": 0.10910225659608841, "refine": "ADD"}, {"boundingVolume": {"box": [152.17938232421875, 136.96878051757812, -163.76248168945312, 16.086402893066406, 0.0, 0.0, 0.0, 16.11181640625, 0.0, 0.0, 0.0, 0.9963150024414062]}, "content": {"uri": "pointcloud_034451.pnts"}, "geometricError": 0.10910225659608841, "refine": "ADD"}, {"boundingVolume": {"box": [132.41952514648438, 170.51718139648438, -164.01376342773438, 3.4029006958007812, 0.0, 0.0, 0.0, 14.384071350097656, 0.0, 0.0, 0.0, 1.2589263916015625]}, "content": {"uri": "pointcloud_034452.pnts"}, "geometricError": 0.10910225659608841, "refine": "ADD"}, {"boundingVolume": {"box": [160.9756317138672, 163.42556762695312, -164.2653045654297, 7.313323974609375, 0.0, 0.0, 0.0, 10.32720947265625, 0.0, 0.0, 0.0, 1.507781982421875]}, "content": {"uri": "pointcloud_034453.pnts"}, "geometricError": 0.10910225659608841, "refine": "ADD"}, {"boundingVolume": {"box": [119.91654968261719, 136.96554565429688, -155.74050903320312, 16.124065399169922, 0.0, 0.0, 0.0, 16.117286682128906, 0.0, 0.0, 0.0, 7.0137481689453125]}, "content": {"uri": "pointcloud_034454.pnts"}, "geometricError": 0.10910225659608841, "refine": "ADD"}, {"boundingVolume": {"box": [152.16714477539062, 139.00125122070312, -155.32577514648438, 16.125228881835938, 0.0, 0.0, 0.0, 14.094192504882812, 0.0, 0.0, 0.0, 7.4284515380859375]}, "content": {"uri": "pointcloud_034455.pnts"}, "geometricError": 0.10910225659608841, "refine": "ADD"}, {"boundingVolume": {"box": [119.91487884521484, 169.21820068359375, -158.2333984375, 16.12151336669922, 0.0, 0.0, 0.0, 16.11699676513672, 0.0, 0.0, 0.0, 4.521080017089844]}, "content": {"uri": "pointcloud_034456.pnts"}, "geometricError": 0.10910225659608841, "refine": "ADD"}, {"boundingVolume": {"box": [152.15634155273438, 169.22476196289062, -158.53640747070312, 16.111610412597656, 0.0, 0.0, 0.0, 16.12220001220703, 0.0, 0.0, 0.0, 4.215972900390625]}, "content": {"uri": "pointcloud_034457.pnts"}, "geometricError": 0.10910225659608841, "refine": "ADD"}], "content": {"uri": "pointcloud_03445.pnts"}, "geometricError": 0.21820451319217682, "refine": "ADD"}, {"boundingVolume": {"box": [71.53958129882812, 217.60028076171875, -156.0439453125, 32.25068283081055, 0.0, 0.0, 0.0, 32.249305725097656, 0.0, 0.0, 0.0, 11.214653015136719]}, "children": [{"boundingVolume": {"box": [55.41801452636719, 201.48565673828125, -164.0133819580078, 16.11498260498047, 0.0, 0.0, 0.0, 16.09912872314453, 0.0, 0.0, 0.0, 1.25811767578125]}, "content": {"uri": "pointcloud_034460.pnts"}, "geometricError": 0.10910225659608841, "refine": "ADD"}, {"boundingVolume": {"box": [87.661376953125, 205.0366973876953, -164.04730224609375, 16.118595123291016, 0.0, 0.0, 0.0, 12.555419921875, 0.0, 0.0, 0.0, 1.2892837524414062]}, "content": {"uri": "pointcloud_034461.pnts"}, "geometricError": 0.10910225659608841, "refine": "ADD"}, {"boundingVolume": {"box": [55.425071716308594, 233.72341918945312, -164.0731201171875, 16.11351203918457, 0.0, 0.0, 0.0, 16.12413787841797, 0.0, 0.0, 0.0, 1.2955856323242188]}, "content": {"uri": "pointcloud_034462.pnts"}, "geometricError": 0.10910225659608841, "refine": "ADD"}, {"boundingVolume": {"box": [87.66207885742188, 233.69203186035156, -164.1290283203125, 16.12197494506836, 0.0, 0.0, 0.0, 16.09246826171875, 0.0, 0.0, 0.0, 1.3723373413085938]}, "content": {"uri": "pointcloud_034463.pnts"}, "geometricError": 0.10910225659608841, "refine": "ADD"}, {"boundingVolume": {"box": [55.434852600097656, 201.4916229248047, -159.22784423828125, 16.093385696411133, 0.0, 0.0, 0.0, 16.095748901367188, 0.0, 0.0, 0.0, 3.5137710571289062]}, "content": {"uri": "pointcloud_034464.pnts"}, "geometricError": 0.10910225659608841, "refine": "ADD"}, {"boundingVolume": {"box": [87.6774673461914, 200.12147521972656, -153.8734130859375, 16.09947967529297, 0.0, 0.0, 0.0, 14.768295288085938, 0.0, 0.0, 0.0, 8.868240356445312]}, "content": {"uri": "pointcloud_034465.pnts"}, "geometricError": 0.10910225659608841, "refine": "ADD"}, {"boundingVolume": {"box": [45.45433807373047, 233.77484130859375, -159.84671020507812, 6.15302848815918, 0.0, 0.0, 0.0, 16.075096130371094, 0.0, 0.0, 0.0, 2.8905563354492188]}, "content": {"uri": "pointcloud_034466.pnts"}, "geometricError": 0.10910225659608841, "refine": "ADD"}, {"boundingVolume": {"box": [90.99026489257812, 238.6071319580078, -159.9009246826172, 12.793670654296875, 0.0, 0.0, 0.0, 11.2418212890625, 0.0, 0.0, 0.0, 2.8411407470703125]}, "content": {"uri": "pointcloud_034467.pnts"}, "geometricError": 0.10910225659608841, "refine": "ADD"}], "content": {"uri": "pointcloud_03446.pnts"}, "geometricError": 0.21820451319217682, "refine": "ADD"}, {"boundingVolume": {"box": [136.04153442382812, 217.5992431640625, -162.03030395507812, 32.25068283081055, 0.0, 0.0, 0.0, 32.2508544921875, 0.0, 0.0, 0.0, 17.039817810058594]}, "children": [{"boundingVolume": {"box": [119.92464447021484, 201.53094482421875, -170.91026306152344, 16.110633850097656, 0.0, 0.0, 0.0, 16.059677124023438, 0.0, 0.0, 0.0, 8.152481079101562]}, "content": {"uri": "pointcloud_034470.pnts"}, "geometricError": 0.10910225659608841, "refine": "ADD"}, {"boundingVolume": {"box": [152.17437744140625, 203.80746459960938, -164.18984985351562, 16.117050170898438, 0.0, 0.0, 0.0, 13.738945007324219, 0.0, 0.0, 0.0, 1.4334030151367188]}, "content": {"uri": "pointcloud_034471.pnts"}, "geometricError": 0.10910225659608841, "refine": "ADD"}, {"boundingVolume": {"box": [119.92044067382812, 233.72593688964844, -164.01327514648438, 16.113750457763672, 0.0, 0.0, 0.0, 16.121536254882812, 0.0, 0.0, 0.0, 1.2556076049804688]}, "content": {"uri": "pointcloud_034472.pnts"}, "geometricError": 0.10910225659608841, "refine": "ADD"}, {"boundingVolume": {"box": [152.1802520751953, 229.77951049804688, -164.0207061767578, 16.096298217773438, 0.0, 0.0, 0.0, 12.174629211425781, 0.0, 0.0, 0.0, 1.252838134765625]}, "content": {"uri": "pointcloud_034473.pnts"}, "geometricError": 0.10910225659608841, "refine": "ADD"}, {"boundingVolume": {"box": [119.75064086914062, 201.46575927734375, -158.1652069091797, 15.93829345703125, 0.0, 0.0, 0.0, 16.11711883544922, 0.0, 0.0, 0.0, 4.5887298583984375]}, "content": {"uri": "pointcloud_034474.pnts"}, "geometricError": 0.10910225659608841, "refine": "ADD"}, {"boundingVolume": {"box": [152.18685913085938, 201.48236083984375, -159.3299560546875, 16.095664978027344, 0.0, 0.0, 0.0, 16.11530303955078, 0.0, 0.0, 0.0, 3.4039077758789062]}, "content": {"uri": "pointcloud_034475.pnts"}, "geometricError": 0.10910225659608841, "refine": "ADD"}, {"boundingVolume": {"box": [119.92254638671875, 233.7316436767578, -158.75721740722656, 16.118892669677734, 0.0, 0.0, 0.0, 16.108917236328125, 0.0, 0.0, 0.0, 3.99468994140625]}, "content": {"uri": "pointcloud_034476.pnts"}, "geometricError": 0.10910225659608841, "refine": "ADD"}, {"boundingVolume": {"box": [152.16746520996094, 233.72804260253906, -158.45834350585938, 16.124069213867188, 0.0, 0.0, 0.0, 16.121658325195312, 0.0, 0.0, 0.0, 4.28564453125]}, "content": {"uri": "pointcloud_034477.pnts"}, "geometricError": 0.10910225659608841, "refine": "ADD"}], "content": {"uri": "pointcloud_03447.pnts"}, "geometricError": 0.21820451319217682, "refine": "ADD"}], "content": {"uri": "pointcloud_0344.pnts"}, "geometricError": 0.43640902638435364, "refine": "ADD"}}