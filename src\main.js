// import { createApp } from 'vue';
import website from './config/website';
import axios from './axios';
import router from './router/';
import store from './store';
import i18n from './lang/';
import { language, messages } from './lang/';
import * as ElementPlusIconsVue from '@element-plus/icons-vue';
import ElementPlus from 'element-plus';
import 'element-plus/dist/index.css';
import Avue from '@smallwei/avue';
import '@smallwei/avue/lib/index.css';
import crudCommon from '@/mixins/crud.js';
import { getScreen } from './utils/util';
import './css/iconfont.css';
import './permission';
import error from './error';
import avueUeditor from 'avue-plugin-ueditor';
import basicBlock from '@/components/basic-block/main.vue';
import basicContainer from '@/components/basic-container/main.vue';
import thirdRegister from '@/components/third-register/main.vue';
import flowDesign from '@/components/flow-design/main.vue';

import '@/styles/dij/index.scss'
import { antComponents } from './antd'
import { CommonComponents } from './use-common-components'
import 'virtual:svg-icons-register'

import VConsole from 'vconsole'
import moment from 'moment';
import 'moment/dist/locale/zh-cn';
import { useDirectives } from './directives'
import App from './App.vue';
import 'animate.css';
import dayjs from 'dayjs';
import 'styles/common.scss';
// 业务组件
import tenantPackage from './views/system/tenantpackage.vue';
import { createInstance } from '@/root'
import permission from '@/utils/v-permission'
const app = createInstance(App);
window.$crudCommon = crudCommon;
window.axios = axios;
import { message } from 'ant-design-vue'
message.config({ top: `50px` });

for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component);
}
app.component('basicContainer', basicContainer);
app.component('basicBlock', basicBlock);
app.component('thirdRegister', thirdRegister);
app.component('flowDesign', flowDesign);
app.component('tenantPackage', tenantPackage);
app.use(CommonComponents)
app.use(antComponents)

app.config.globalProperties.$moment = moment;
app.config.globalProperties.$dayjs = dayjs;
app.config.globalProperties.website = website;
app.config.globalProperties.getScreen = getScreen;
app.use(error);
app.use(i18n);
app.use(store);
app.use(router);
moment.locale('zh-cn')

app.use(useDirectives)
app.directive('permission', permission)
// let vconsole = new VConsole()
// app.use(vconsole);
app.use(ElementPlus, {
  locale: messages[language],
});
app.use(Avue, {
  axios,
  calcHeight: 10,
  locale: messages[language],
});
app.use(avueUeditor, { axios })
// app.use(NfDesignBase);
app.mount('#app');
