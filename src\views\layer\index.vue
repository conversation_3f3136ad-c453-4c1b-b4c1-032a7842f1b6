<template>
   <div class="project-layer-wrapper height-100">
    <div class="left">
      <!-- <Sidebar /> -->
      <div class="main-content uranus-scrollbar dark">
        <div class="project-layer-wrapper height-100 bor-b">
          <a-row>
            <a-col :span="1"></a-col>
            <span :span="15">标注</span>
          </a-row>
        </div>
        <!-- 左边展开列表 -->
        <div class="scrollbar">
          <div style="padding:10px">
            <LayersTree
                :layer-data="mapLayers"
                class="project-layer-contentproject-layer-content"
                @check="checkLayer"
                @select="selectLayer"
                v-model:selectedKeys="selectedKeys"
                v-model:checkedKeys="checkedKeys"
            >
            </LayersTree>
          </div>
        </div>
      </div>
    </div>
    <div class="right">
      <div class="map-wrapper">
        <div class="g-map-wrapper">
          <a-drawer
              title="地图元素"
              placement="right"
              :closable="true"
              v-model:visible="visible"
              :mask="false"
              wrapClassName="drawer-element-wrapper"
              @close="closeDrawer"
              width="300"
          >
            <el-form label-width="80px">
              <el-form-item label="名称：" >
                <el-input type="textarea"
                          v-model="layerState.layerName"
                          placeholder="请输入名称"
                          @change="changeLayer" style="resize:none"
				:disabled="layerState.layerName.indexOf('-') != -1?true:false"
                ></el-input>
              </el-form-item>
              <el-form-item label="经度：" v-if="layerState.currentType === geoType.Point">
                <el-input
                    v-model:value="layerState.longitude"
                    placeholder="请输入经度" disabled
                    @change="changeLayer" style="margin-top:10px"
                ></el-input>
              </el-form-item>
              <el-form-item label="纬度：" v-if="layerState.currentType === geoType.Point">
                <el-input
                    v-model:value="layerState.latitude"
                    placeholder="请输入纬度" disabled
                    @change="changeLayer" style="margin-top:10px"
                ></el-input>
              </el-form-item>
              <el-form-item label="颜色：" v-if="layerState.layerName.indexOf('-') == -1">
                <div class="color-content" style="margin-top:10px">
                  <div
                      v-for="item in colors"
                      :key="item.id"
                      class="color-item"
                      :style="'background:' + item.color"
                      @click="changeColor(item)"
					  v-permission
                  >
                    <svg-icon
                        v-if="item.color === layerState.color"
                        :size="18"
                        name="check"
                    ></svg-icon>
                  </div>
                </div>
              </el-form-item>
            </el-form>

            <div class="flex-row flex-justify-around flex-align-center mt20">
              <el-button type="primary" @click="deleteElement" v-permission>删除</el-button>
            </div>
          </a-drawer>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import {onMounted, reactive, ref, watch, onBeforeUnmount} from 'vue';
import {deleteElementReq, getElementGroupsReq, updateElementsReq} from '@/api/layer';
import LayersTree from '@/components/LayersTree.vue';
import {MapDoodleColor, MapElementEnum} from '@/constants/map';
import {useGMapCover} from '@/hooks/use-c-map-cover';
import {getRoot} from '@/root';
import store from '@/store';
import {GeojsonCoordinate, LayerResource} from '@/types/map';
import {Color, GeoType} from '@/types/mapLayer';
import {generatePoint} from '@/utils/genjson';
import { GetFlightArea } from '@/api/flight-area';
import { useMapTool } from '@/hooks/use-map-tool';
import {message} from 'ant-design-vue';
import EventBus from '@/event-bus/'
import { useRoute } from 'vue-router';

const root = getRoot();
const route = useRoute();
let useGMapCoverHook = useGMapCover();
const mapLayers = ref(store.state.dock.Layers);
const checkedKeys = ref<string[]>([]);
const selectedKeys = ref<string[]>([]);
const selectedKey = ref<string>('');
const selectedLayer = ref<any>(null);
const visible = ref<boolean>(false);
store.commit('SET_DRAW_VISIBLE_INFO', visible.value);
const geoType = GeoType;
const layerState = reactive({
  layerName: '',
  layerId: '',
  longitude: 0,
  latitude: 0,
  currentType: '', // "LineString","Polygon","Point"
  color: '#212121',
});
const colors = ref<Color[]>([
  {id: 1, name: 'BLUE', color: '#2D8CF0', selected: true},
  {id: 2, name: 'GREEN', color: '#19BE6B', selected: false},
  {id: 3, name: 'YELLOW', color: '#FFBB00', selected: false},
  {id: 4, name: 'ORANGE', color: '#B620E0', selected: false},
  {id: 5, name: 'RED', color: '#E23C39', selected: false},
  {id: 6, name: 'NAME_DEFAULT', color: '#212121', selected: false},
]);
const scorllHeight = ref();
const textType = ref(false);
const textList = ref([]);
const positioning = ref([]);

async function getAllElement() {
	getElementGroups('init');
	setTimeout(() => {
		useGMapCoverHook = useGMapCover();
		initMapCover();
	}, 300);
}
function initMapCover() {
	if (route.path === '/layer/index') {
		mapLayers.value.forEach(item => {
			if (item.elements) {
				setMapCoverByElement(item.elements);
			}
		});
	}
}
//监听实时更新标注
watch(
	() => store.state.dock.Layers,
	newData => {
		if(route.path !== '/layer/index') return
		mapLayers.value = newData;
		// store.commit('SET_LAYER_INFO', newData);
		for(let i=0;i<mapLayers.value.length;i++){
			if(mapLayers.value[i].elements.length>0){
				for(let j=0;j<mapLayers.value[i].elements.length;j++){
					mapLayers.value[i].elements[j].eyeShow = 1;
				}
			}
		}
		if(textType.value==false){
			new Promise((resolve) => {
				setTimeout(() => {
					resolve(mapLayers.value[0].elements);
				}, 300);
			}).then(elements => {
				useGMapCoverHook.clearAllTextInfo(); // 清除所有标注
				initTextInfo(false,elements);
			});
		}else{
			useGMapCoverHook.clearAllTextInfo(); // 清除所有标注
			initTextInfo(true,mapLayers.value[0].elements);
		}
	},
	{
		deep: true,
	}
);

function initTextInfo(textState, newDataList) {
	if (newDataList.length > 0) {
		if (textState == false) {
			let coordinates = [];
			let promises = []; // 创建一个 Promise 列表
			if (textList.value.length < newDataList.length) {
				for (let i = textList.value.length; i < newDataList.length; i++) {
					let clonedElement = JSON.parse(JSON.stringify(newDataList[i]));
					// 创建一个新的 Promise 处理每个 clonedElement
					let promise = new Promise((resolve) => {
						updateCoordinates('wgs84-gcj02', clonedElement);
						resolve(clonedElement);
					});
					promises.push(promise); // 将 Promise 加入列表
				}
				// 等待所有 Promise 都执行完后处理
				Promise.all(promises).then((updatedElements) => {
					// 处理 newDataList 的逻辑
					newDataList.forEach(item => {
						updatedElements.forEach(updatedElement => {
							if (item.id === updatedElement.id) {
								// 这里使用 updatedElement 更新数据
								try {
									if (updatedElement.resource.content.geometry.type == "LineString") {
										coordinates = updatedElement.resource.content.geometry.coordinates[0];
										if (coordinates && coordinates.length >= 2) {
											positioning.value.push({
												id: updatedElement.id,
												coordinates: coordinates,
											});
											useGMapCoverHook.initTextInfo(updatedElement.name, [coordinates[0], coordinates[1]], updatedElement.id);
										}
									} else {
										coordinates = updatedElement.resource.content.geometry.coordinates[0][0];
										if (coordinates && coordinates.length >= 2) {
											positioning.value.push({
												id: updatedElement.id,
												coordinates: coordinates,
											});
											useGMapCoverHook.initTextInfo(updatedElement.name, [coordinates[0], coordinates[1]], updatedElement.id);
										}
									}
								} catch (e) {
									console.warn('标注初始化失败:', e, updatedElement.name, coordinates, updatedElement.id);
								}
							} else {
								// 非更新的元素，使用原始数据
								try {
									if (item.resource.content.geometry.type == "LineString") {
										coordinates = item.resource.content.geometry.coordinates[0];
										if (coordinates && coordinates.length >= 2) {
											// 清除旧标注并重新初始化
											useGMapCoverHook.clearTextInfo(item.id);
											useGMapCoverHook.initTextInfo(item.name, [coordinates[0], coordinates[1]], item.id);
										}
									} else {
										coordinates = item.resource.content.geometry.coordinates[0][0];
										if (coordinates && coordinates.length >= 2) {
											// 清除旧标注并重新初始化
											useGMapCoverHook.clearTextInfo(item.id);
											useGMapCoverHook.initTextInfo(item.name, [coordinates[0], coordinates[1]], item.id);
										}
									}
								} catch (e) {
									console.warn('标注初始化失败:', e, item.name, coordinates, item.id);
								}
							}
						});
					});
				});
			} else {
				// useGMapCoverHook.clearAllTextInfo(); // 清除所有标注
				newDataList.forEach(item => {
					try {
						if (item.resource.content.geometry.type == "LineString") {
							coordinates = item.resource.content.geometry.coordinates[0];
							if (coordinates && coordinates.length >= 2) {
								// 清除旧标注
								useGMapCoverHook.clearTextInfo(item.id);
								useGMapCoverHook.initTextInfo(item.name, [coordinates[0], coordinates[1]], item.id);
							}
						} else {
							coordinates = item.resource.content.geometry.coordinates[0][0];
							if (coordinates && coordinates.length >= 2) {
								// 清除旧标注
								useGMapCoverHook.clearTextInfo(item.id);
								useGMapCoverHook.initTextInfo(item.name, [coordinates[0], coordinates[1]], item.id);
							}
						}
					} catch (e) {
						console.warn('标注初始化失败:', e, item.name, coordinates, item.id);
					}
				});
			}
		} else {
			let coordinates = [];
			newDataList.forEach(item => {
				try {
					if (item.resource.content.geometry.type == "LineString") {
						coordinates = item.resource.content.geometry.coordinates[0];
						if (coordinates && coordinates.length >= 2) {
							// 清除旧标注
							useGMapCoverHook.clearTextInfo(item.id);
							useGMapCoverHook.initTextInfo(item.name, [coordinates[0], coordinates[1]], item.id);
						}
					} else {
						coordinates = item.resource.content.geometry.coordinates[0][0];
						if (coordinates && coordinates.length >= 2) {
							// 清除旧标注
							useGMapCoverHook.clearTextInfo(item.id);
							useGMapCoverHook.initTextInfo(item.name, [coordinates[0], coordinates[1]], item.id);
						}
					}
				} catch (e) {
					console.warn('标注初始化失败:', e, item.name, coordinates, item.id);
				}
			});
			textType.value=false;
		}
	}
}

function setMapCoverByElement(elements : LayerResource[]) {
	elements.forEach(element => {
		const name = element.name;
		const color = element.resource?.content.properties.color;
		const type = element.resource?.type as number;
		updateMapElement(element, name, color, '');
	});
}

function updateMapElement(element : LayerResource, name : string, color : string | undefined,tcType: string) {
	const geoType = element.resource?.content.geometry.type;
	const id = element.id;
	const type = element.resource?.type as number;
	if (MapElementEnum.PIN === type) {
		const coordinates = element.resource?.content.geometry.coordinates as GeojsonCoordinate;
		useGMapCoverHook.updatePinElement(id, name, coordinates, color,tcType);
	} else if (MapElementEnum.LINE === type && geoType === 'LineString') {
		const coordinates = element.resource?.content.geometry.coordinates as GeojsonCoordinate[];
		useGMapCoverHook.updatePolylineElement(id, name, coordinates, color,tcType);
	} else if (MapElementEnum.POLY === type && geoType === 'Polygon') {
		
		const coordinates = element.resource?.content.geometry.coordinates as GeojsonCoordinate[][];
		useGMapCoverHook.updatePolygonElement(id, name, coordinates, color,tcType);
	}
}

function checkLayer(keys : string[]) {
	console.log('checkLayer', keys, selectedKeys.value, checkedKeys.value);
}

function selectLayer(keys : string[], e) {
	if(e.node.eventKey.indexOf('resource')==-1){
		return false
	}
	if (e.selected) {
		selectedKey.value = e.node.eventKey;
		selectedLayer.value = getCurrentLayer(selectedKey.value);
		setBaseInfo();
	}
	visible.value = e.selected;
	store.commit('SET_DRAW_VISIBLE_INFO', visible.value);
	// store.dispatch('updateElement', { type: 'is_select', id: e.node.eventKey, bool: e.selected })
}
function getCurrentLayer(id : string) {
	const Layers = store.state.dock.Layers;
	const key = id.replace('resource__', '');
	let layer = null;
	const findCan = function (V) {
		V.forEach(item => {
			if (item.id == key) {
				layer = item;
			}
			if (item.elements) {
				findCan(item.elements);
			}
		});
	};
	findCan(Layers);
	// const layer = Layers.find(item => item.elements.find(el => el.id === key))
	// console.log('layer', layer);
	clickArea(layer);
	return layer;
}

function setBaseInfo() {
	const layer = selectedLayer.value;
	if (layer) {
		const geoType = layer.resource?.content.geometry.type;
		layerState.currentType = geoType;
		layerState.layerName = layer.name;
		layerState.layerId = layer.id;
		layerState.color = layer.resource?.content.properties.color;
		let coordinate : GeojsonCoordinate;
		switch (geoType) {
			case GeoType.Point:
				coordinate = layer.resource?.content.geometry.coordinates;
				layerState.longitude = coordinate[0];
				layerState.latitude = coordinate[1];
				break;
			case GeoType.LineString:
				layerState.longitude = layer.resource?.content.geometry.coordinates[0];
				layerState.latitude = layer.resource?.content.geometry.coordinates[1];
				break;
			case GeoType.Polygon:
				layerState.longitude = layer.resource?.content.geometry.coordinates[0];
				layerState.latitude = layer.resource?.content.geometry.coordinates[1];
				break;
		}
	}
}
let useMapToolHook = useMapTool();
const clickArea = (area: any) => {
	let coordinate;
	// 确保点击的数据坐标已经转换为 GCJ02
	if (area && area.resource != undefined) {
		const coordinates = area.resource.content.geometry.coordinates;
		switch (area.resource.content.geometry.type) {
			case 'Point':
				coordinate = coordinates;
				break;
			case 'LineString':
				coordinate = coordinates[0];
				break;
			case 'Polygon':
				coordinate = coordinates[0][0];
				break;
		}
		if(textType.value==false){
			for (let i = 0; i < positioning.value.length; i++) {
				if (area.id == positioning.value[i].id) {
					coordinate = positioning.value[i].coordinates
				}
			}
		}
		
		useMapToolHook.panTo(coordinate);
	}
};
onMounted(() => {

	//异步加载控件
	// const element = document.getElementsByClassName('scrollbar').item(0) as HTMLDivElement
	// const parent = element?.parentNode as HTMLDivElement
	// scorllHeight.value = parent?.clientHeight - parent.firstElementChild!.clientHeight
	getAllElement();
	// 监听地图点击线的事件
	EventBus.on('lineClick', (data)=>{
		selectedKeys.value = [`resource__${data.id}`]
	});
});

onBeforeUnmount(() => {
  //组件内实列被卸载前生命周期
  const root = getRoot();
  for (const id in store.state.dock.coverMap) {
    useGMapCoverHook.removeCoverFromMap(id)
  }
  EventBus.off('lineClick');
});

function closeDrawer() {
	store.commit('SET_DRAW_VISIBLE_INFO', false);
	selectedKeys.value = [];
}

function changeColor(color : Color) {
  /*修改的时候，标注显示*/
  useGMapCoverHook.hideCoverFromMap(selectedLayer.value.id,2)
	layerState.color = color.color;
	updateElements(true);
}

function changeLayer(val : string) {
  /*修改的时候，标注显示*/
  useGMapCoverHook.hideCoverFromMap(selectedLayer.value.id,2)
  updateElements(true);
}

async function deleteElement() {
	const elementid = selectedLayer.value.id;

	await deleteElementReq(elementid, {}).then(async (res : any) => {
		// console.log('delete element res:', res)
		if (res.code !== 0) {
			console.warn(res);
			return;
		}
		visible.value = false;
		message.success('删除成功');
		store.commit('SET_DRAW_VISIBLE_INFO', visible.value);
		// useGMapCoverHook.removeCoverFromMap(elementid);
		useGMapCoverHook.hideCoverFromMap(elementid,1);
		getElementGroups();
		EventBus.emit('flightList');
		if(textList.value.length>0){
			for (let i = 0; i < textList.value.length; i++) {
				if(textList.value[i]==elementid){
					textList.value.splice(i, 1)
				}
			}
		}
		textType.value=false;
	});
}

async function getElementGroups(type ?: string) {
	let mapType = false;
	store.commit('MAP_TYPE', mapType);
	let layerMapType=true;
	store.commit('LAYER_MAP_TYPE', layerMapType);
	const result = await getElementGroupsReq({
		groupId: '',
		isDistributed: true,
	});
	mapLayers.value = result.data;
	positioning.value=[];
	textList.value=[]
	if (type && type === 'init') {
		// console.log('获取所有标注数据接口信息' + JSON.stringify(mapLayers.value))
		store.dispatch('setLayerInfo', mapLayers.value); //actions setLayerInfo的方法 异步
		if(mapLayers.value[0].elements.length>0){
			mapLayers.value[0].elements.forEach(item => {
				textList.value.push(item.id)
			})
		}
	}else{
		if(mapLayers.value[0].elements.length>0){
			mapLayers.value[0].elements.forEach(item => {
				textList.value.push(item.id)
			})
			textType.value=true;  
		}
	}
	store.commit('SET_LAYER_INFO', mapLayers.value); //触发mutations SET_LAYER_INFO的方法 同步
}

async function updateElements(tcType) {
  let content = null;
  
  if (layerState.currentType === GeoType.Point) {
    const position = {
      height: 0,
      latitude: layerState.latitude || 0,
      longitude: layerState.longitude || 0,
    };
    const cxt = generatePoint(position, {
      color: layerState.color || MapDoodleColor.PinColor,
      clampToGround: true,
    });
    content = {
      type: MapElementEnum.PIN,
      geometry: cxt.geometry,
      properties: cxt.properties,
    };
    const currentLayer = selectedLayer.value;
    currentLayer.resource.content = content;
    selectedLayer.value = currentLayer;
  } else {
    const currentLayer = selectedLayer.value;
    content = currentLayer.resource.content;
    content.properties.color = layerState.color;
  }
  updateMapElement(selectedLayer.value, layerState.layerName, layerState.color,tcType);
  const result = await updateElementsReq(layerState.layerId, {
    name: layerState.layerName,
    content: content,
  });
  getElementGroups();
}

function updateCoordinates(transformType: string, element: LayerResource) {
  // 保留函数但不进行坐标转换
  return element;
}
</script>

<style lang="scss" scoped>
@import '@/styles/dij/index.scss';

.drawer-element-wrapper {
	.ant-drawer-content {
		background-color: $dark-highlight;
		color: $text-white-basic;

		.ant-drawer-header {
			background-color: $dark-highlight;

			.ant-drawer-title {
				color: $text-white-basic;
			}

			.ant-drawer-close {
				color: $text-white-basic;
			}
		}

		.ant-input {
			background-color: #101010;
			border-color: $dark-border;
			color: $text-white-basic;
		}
	}

	.color-content {
		display: flex;
		align-items: center;
		margin-top: 8px;

		.color-item {
			cursor: pointer;
			width: 18px;
			height: 18px;
			line-height: 18px;
			display: flex;
			align-items: center;
			margin-left: 5px;
		}
	}

	.title {
		display: inline-flex;
		width: 80px;
	}

	.element-item {
		margin-bottom: 10px;
	}
}

.scrollbar {
	overflow: auto;
}

.project-app-wrapper .left .main-content {
	flex: 1;
	width: 285px;
}

.bor-b {
	height: 50px;
	line-height: 50px;
	border-bottom: 1px solid #4f4f4f;
	font-weight: 450;
}

.color-item {
	cursor: pointer;
	width: 18px;
	height: 18px;
	line-height: 18px;
	display: flex;
	align-items: center;
	margin-left: 5px;
	float: left;
}
</style>