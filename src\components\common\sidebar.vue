<template>
  <div class="demo-project-sidebar-wrapper flex-justify-between">
    <div style="margin-top: 10px;">
		<div class="mb20 flex-display flex-column flex-align-center flex-justify-between">
		  <a-tooltip title="返回" placement="right">
		      <Icon class="fz20" icon="ImportOutlined" @click="goHome"/>
		  </a-tooltip>
		</div>
      <router-link v-for="item in options"
                   :key="item.key"
                   :to="item.path"
                   :class="{'menu-item': true , 'selected': $route.path === item.path}"
      >
        <a-tooltip :title="item.label" placement="right">
          <Icon class="fz20" :icon="item.icon"/>
        </a-tooltip>
      </router-link>
	  
    </div>
    
  </div>
</template>

<script lang="ts">
import {createVNode, defineComponent} from 'vue'
import {getRoot} from '@/root'
import * as icons from '@ant-design/icons-vue'
import {ERouterName} from '@/api/enum/index'
import router from '@/router'

interface IOptions {
  key: number
  label: string
  path:
      | string
      | {
    path: string
    query?: any
  }
  icon: string
}

const Icon = (props: { icon: string }) => {
  return createVNode((icons as any)[props.icon])
}

export default defineComponent({
  components: {
    Icon,
  },
  name: 'Sidebar',
  setup() {
    const root = getRoot()
    const options = [
      {key: 0, label: '工作区', path: '/' + ERouterName.TSA, icon: 'TeamOutlined'},
      {key: 1, label: '任务计划库', path: '/' + ERouterName.TASK, icon: 'CalendarOutlined'},
      {key: 2, label: '航线库', path: '/' + ERouterName.WAYLINE, icon: 'NodeIndexOutlined'},
      {key: 3, label: '标注', path: '/' + ERouterName.LAYER, icon: 'EnvironmentOutlined'},
      {key: 4, label: '自定义飞行区域', path: '/' + ERouterName.FLIGHT_AREA, icon: 'GroupOutlined'},
      // {key: 5, label: '直播', path: '/' + ERouterName.LIVESTREAM, icon: 'VideoCameraOutlined'},
      // {key: 6, label: '媒体库', path: '/' + ERouterName.MEDIA, icon: 'PictureOutlined'},
    ]

    function goHome() {
      router.push({
        path: '/',
        query: {
          target: 'tsa'
        }
      });
    }

    return {
      options, goHome
    }
  }
})
</script>

<style scoped lang="scss">
.demo-project-sidebar-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;

  border-right: 1px solid #4f4f4f;
  color: #fff;
  overflow: hidden;

  .menu-item {
    width: 100%;
    padding: 16px;
    display: flex;
    flex-direction: column;
    align-items: center;
    color: #fff;
    cursor: pointer;

    &.selected {
      background-color: #101010;
      // color: #333;
    }

    &.disabled {
      pointer-events: none;
      opacity: 0.45;
    }
  }

  .filling {
    flex: 1;
  }

  .setting-icon {
    font-size: 24px;
    margin-bottom: 24px;
    color: #fff;
  }
}

.ant-tooltip-open {
  border: 0;
}

.demo-project-sidebar-wrapper .menu-item.selected[data-v-056ccd48] {
  background-color: #101010;
  color: #2d8cf0;
}
</style>