import request from '@/axios';
import {ELocalStorageKey} from '@/api/enum/index';
import { getWorkspaceId } from '@/utils/storage'

//成员列表
export const membersList = (page, page_size) => {
  return request({
    url: '/hztech-flight-core/manage/api/v1/users/' + getWorkspaceId() + '/users',
    method: 'get',
    params: {
      page,
      page_size,
    }
  })
}

//修改用户
export const updateUser = (userId, params) => {
  return request({
    url: '/hztech-flight-core/manage/api/v1/users/' + getWorkspaceId() + '/users/' + userId + '',
    method: 'put',
    data: params
  })
}

//用户列表数据同步
export const synchronization = (tenantId) => {
  return request({
    url: '/hztech-flight-core/manage/api/v1/users/' + getWorkspaceId() + '/userSynchronization?tenantId=' + tenantId,
    method: 'post',
  })
}