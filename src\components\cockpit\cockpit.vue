<template>
  <div class="cockpitMap">
    <div id="cockpitMap" :class="['map-container', { 'map-container-small': isMapExpanded }]"></div>
    <topBar :cockpitDock="cockpit_dock" @closeCockpit="handleCloseCockpit" :deviceState="isChange"></topBar>
    <cockpitLayer></cockpitLayer>
    <dashBoardBox :deviceInfoAttrs="deviceInfoAttrs"></dashBoardBox>
    <controlContainer v-if="cockpit_dock.is_dock" class="z_index1" :deviceInfoAttrs="deviceInfoAttrs">
    </controlContainer>
    <cokpitProcess :deviceInfoAttrs="deviceInfoAttrs" :dockInfoAttrs="dockInfoAttrs"></cokpitProcess>
    <cockpitVideo ref="videoMap" :deviceInfoAttrs="deviceInfoAttrs" @change="videoMapChange" :class="isMapExpanded ? 'video-big' : 'video_box'"
      :devicePlay="devicePlay" :cockpit_dock="cockpit_dock"></cockpitVideo>
    <div class="map-coordinates-bar">
      <div class="coordinates-info">
        <span>经度: {{ mousePosition.longitude.toFixed(6) }}</span>
        <span>纬度: {{ mousePosition.latitude.toFixed(6) }}</span>
        <a-tooltip placement="top">
          <template #title>
            <div>
              <p><strong>海拔高度 (Above Sea Level)</strong></p>
              <p>指相对于平均海平面的高度，常用于飞行导航和地形分析。</p>
              <p>在无人机飞行中，ASL决定了飞行高度限制和障碍物安全裕度。</p>
            </div>
          </template>
          <span>ASL: {{ mousePosition.asl.toFixed(2) }} m</span>
        </a-tooltip>
        <a-tooltip placement="top">
          <template #title>
            <div>
              <p><strong>椭球高度 (Height Above Ellipsoid)</strong></p>
              <p>指相对于WGS84参考椭球体的高度，是GPS原始测量值。</p>
              <p>与ASL的区别：HAE是纯几何测量值，而ASL考虑了地球重力场变化。</p>
              <p>在某些地区，HAE与ASL可能有几十米的差异。</p>
            </div>
          </template>
          <span>HAE: {{ mousePosition.hae.toFixed(2) }} m</span>
        </a-tooltip>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import topBar from './topBar.vue';
import dashBoardBox from './dashBoardBox.vue';
import controlContainer from './controlContainer.vue';
import cockpitLayer from './cockpitLayer.vue';
import cokpitProcess from './cockpitProcess.vue';
import { useConnectMqtt } from '@/components/g-map/use-connect-mqtt';
import cockpitVideo from './cockpit_video.vue';
import store from '@/store';
import { mapGetters, mapState } from 'vuex';
import { EDeviceTypeName } from '@/types';
import { gcj02towgs84, wgs84togcj02 } from '@/vendors/coordtransform';
import dockIcon from '@/assets/dock.png'
import rcIcon from '@/assets/rc.png'
import droneIcon from '@/assets/m30.png'

import { useGMapManage } from '@/hooks/use-c-map';
import { cockpitTsaUpdate } from '@/hooks/use-c-map-cockpit';
import { getApp, getRoot } from '@/root';
import { geNowWayLineJob, getWayLineDetail } from '@/api/wayline';
import EventBus from '@/event-bus';
import { testDroneTrackDisplay } from '@/hooks/use-c-map-cockpit';
import DrawTool from '@/components/djimap/DrawTool.js';
import { useMousePosition } from '@/hooks/use-mouse-position';
export default {
  props: ['center', 'cockpit_dock'],
  emits: ['closeCockpit'],
  components: {
    topBar,
    dashBoardBox,
    controlContainer,
    cockpitVideo,
    cockpitLayer,
    cokpitProcess
  },
  data() {
    return {
      mapCo: '',
      // deviceInfo: store.state.dock.deviceState,
      cockpitTsaUpdateHook: cockpitTsaUpdate(),
      icons: new Map([
        [EDeviceTypeName.Aircraft, droneIcon],
        [EDeviceTypeName.Gateway, rcIcon],
        [EDeviceTypeName.Dock, dockIcon]
      ]),
      // markers: store.state.dock.markerInfo.coverMap,
      isMapExpanded: false,
      isCameraFollow: true, // 添加镜头跟随状态
      //
      points: [],
      /**起飞点数据*/
      startPoint: [],
      template_type: '',
      Polygon: '',
      /**起飞点图层*/
      overlay: [],
      polyline: null, // 存储折线数据
      startPolyline: null,
      overlayGroups: null, // 航点群体标识
      texts: null, // 航段中点距离标签
      drawTool: null, // 绘制工具,
      /*如果从4到1的话就退出航线，从1到4的话就应该重新获取航线*/
      modeCode: -1,
      // 设备状态
      deviceInfoAttrs: null,
      dockInfoAttrs: null,
      //直播在线
      devicePlay: null,
      // worker
      WRJWoker: null,
      // 初始化mousePosition为空对象，稍后会被替换为响应式对象
      mousePosition: {
        longitude: 0,
        latitude: 0,
        asl: 0,
        hae: 0
      },
      // 存储hook实例的引用
      mousePositionHook: null,
      // dockWorker: null
    };
  },
  // 在created中初始化非响应式属性
  created() {
    // 这些属性将被放在组件实例上，但不是响应式数据
    this.texts = []; // 航段中点距离标签
    this.overlay = null; // 起飞点图层
    this.polyline = null; // 存储折线数据
    this.overlayGroups = []; // 航点群体标识
    this.startPolyline = null; // 存储起飞点折线数据
    this.handler = null; // Cesium事件处理器
    this.dragHandler = null; // 拖拽事件处理器
    this.keyboardHandler = null; // 键盘事件处理器
    this.drawTool = null; // 绘制工具
    this.endMarker = null; // 控制点位的end图标

    this.cockpitTsaUpdateHook.removeAllMarker();
    // @ts-ignore
    this.WRJWoker = new Worker(new URL('./worker/cockpit_worker.js', import.meta.url));
  },
  computed: {

    isChange() {
      return store.state.dock.deviceState
    },
    online() {
      return store.state.dock.deviceStatusEvent.deviceOnline
    },
    offline() {
      return store.state.dock.deviceStatusEvent.deviceOffline
    }
  },
  watch: {
    'online': {
      handler(data, oldVal) {
        if (data.sn == this.cockpit_dock.sn) {
          this.devicePlay = true;
        }
      },
      deep: true,
    },
    'isChange': {
      handler(data, oldVal) {
        //无人机
        if (data.currentType === EDeviceTypeName.Aircraft && data.deviceInfo[data.currentSn]) {
          if (data.currentSn != this.cockpit_dock.sn) {
            return;
          }          
          const { longitude, latitude, height } = data.deviceInfo[data.currentSn]
          if (longitude && latitude) {
            this.WRJWoker.postMessage({ deviceSn: data.currentSn, longitude: longitude || 0, latitude: latitude || 0 })
            // 监听数据变化
            this.WRJWoker.onmessage = ({ data: { deviceSn, coordinate } }) => {
              this.cockpitTsaUpdateHook.moveTo(deviceSn, longitude, latitude, height);
              // let point = [longitude, latitude];
              // this.mapMoveTo(point)
            };
          }
          this.deviceInfoAttrs = data.deviceInfo[this.cockpit_dock.sn];
        }
        // 机场
        if (data.currentType === EDeviceTypeName.Dock && data.dockInfo[data.currentSn]) {
          if (data.currentSn != this.cockpit_dock.gateway.sn) {
            return;
          }
          const { longitude, latitude } = data.dockInfo[data.currentSn].basic_osd
          if (longitude && latitude) {
            // const [lng, lat] = gcj02towgs84(longitude, latitude)
            // this.cockpitTsaUpdateHook.addMarker(data.currentSn, lng, lat);
            this.cockpitTsaUpdateHook.addMarker(data.currentSn, longitude, latitude);
          }
          let mCode = data.dockInfo[data.currentSn].basic_osd.mode_code;
          if (this.modeCode != mCode) {
            this.clearWayLine();
            this.points = [];
            this.startPoint = [];
            this.getNowWayline();
            this.modeCode = mCode;
          }
          this.dockInfoAttrs = data.dockInfo[this.cockpit_dock.gateway.sn];
        }
        //遥控器
        if (data.currentType === EDeviceTypeName.Gateway && data.gatewayInfo[data.currentSn]) {
          if (data.currentSn != this.cockpit_dock.gateway.sn) {
            return;
          }
          const { longitude, latitude } = data.gatewayInfo[data.currentSn]
          if (longitude && latitude) {
            this.cockpitTsaUpdateHook.addMarker(data.currentSn, longitude, latitude);
          }
          this.dockInfoAttrs = this.deepClone(data.gatewayInfo[this.cockpit_dock.gateway.sn]);
          this.dockInfoAttrs.basic_osd = {};
          this.dockInfoAttrs.basic_osd.mode_code = 5;
        }

      },
      deep: true,
    },
    'offline': {
      handler(data, oldVal) {
        this.cockpitTsaUpdateHook.removeMarker(data.sn);
        if (data.sn == this.cockpit_dock.gateway.sn) {
          this.dockInfoAttrs = null;
        }
        if (data.sn == this.cockpit_dock.sn) {
          this.deviceInfoAttrs = null;
          this.devicePlay = false;
        }
      },
      deep: true,
    }
  },
  mounted() {
    this.init();
    // this.getNowWayline()
    useConnectMqtt();
    // 监听镜头跟随状态变化
    EventBus.on('toggle_camera_follow', this.handleCameraFollowToggle);
    // setTimeout(() => {
    //   testDroneTrackDisplay(120.66261,27.982012);
    // }, 1000);
  },
  beforeUnmount() {
    // 清理鼠标位置监听器
    if (this.mousePositionHook) {
      this.mousePositionHook.destroyMousePositionListener();
    }

    this.WRJWoker.terminate();
    // this.dockWorker.terminate();
    EventBus.off('toggle_camera_follow', this.handleCameraFollowToggle);
  },
  methods: {
    // 处理镜头跟随状态变化
    handleCameraFollowToggle(value) {
      this.isCameraFollow = value;
      if (this.isCameraFollow) {
        this.cockpitTsaUpdateHook.enableEntityFollow(this.cockpit_dock.sn)
      } else {
        this.cockpitTsaUpdateHook.disableEntityFollow()
      }
    },
    /**
     * 地图偏移
     * @param coordinate 
     */
    mapMoveTo(coordinate) {
      if (!this.isCameraFollow) return;
      setTimeout(() => {
        const root = getRoot();
        let viewer = root.$cockpitMap;
        if (viewer) {
          viewer.camera.flyTo({
            destination: Cesium.Cartesian3.fromDegrees(coordinate[0], coordinate[1], 500),
            orientation: {
              heading: Cesium.Math.toRadians(0.0),
              pitch: Cesium.Math.toRadians(-90),
              roll: 0.0
            }
          });
        }
      }, 0);
    },
    /**
     * 移除地图
     */
    removeMap() {
      const root = getRoot();
      let viewer = root.$cockpitMap;
      if (viewer) {
        viewer.entities.removeAll();
      }
    },
    // 判断数组arr1是否包含数组arr2的所有元素
    arrayContainsArray(arr1, arr2) {
      // 如果arr2的长度大于arr1，直接返回false
      if (arr2.length > arr1.length) {
        return false;
      }
      let arr1Index = 0;
      for (const item of arr2) {
        // 在arr1中从当前位置开始查找item
        while (arr1Index < arr1.length && arr1[arr1Index] !== item) {
          arr1Index++;
        }
        // 如果没有找到item，返回false
        if (arr1Index >= arr1.length) {
          return false;
        }
        // 移动到arr1的下一个位置（即使arr1中有重复的item，也继续向前）
        arr1Index++;
      }
      // 如果找到了arr2中的所有元素，返回true
      return true;
    },
    init() {
      const app = getApp();
      useGMapManage().globalPropertiesConfig(app, this.center, 'cockpitMap', 'Cesium');
      this.$nextTick(() => {
        this.mousePositionHook = useMousePosition();
        this.mousePositionHook.initMousePositionListener();
        this.mousePosition = this.mousePositionHook.mousePosition;
      })
    },
    videoMapChange(value) {
      this.isMapExpanded = value
    },
    async getNowWayline() {
      let gateWaySn = store.state.dock.osdVisible.gateway_sn;
      if (!gateWaySn) {
        return
      }
      const { data: res, code } = await geNowWayLineJob(gateWaySn);
      if (code == -1) {
        return;
      }
      // const res = await getWayLineDetail('c48acb21-1a1f-4570-a88f-e37ea6c5fa81')
      // 有数据-回显地图起飞点
      if (!res.data.waylineData) {
        return;
      }
      const { take_off_ref_point, placemark, template_type } = res.data.waylineData;
      if (take_off_ref_point && take_off_ref_point.substring(0, 3) != 'NaN') {
        const array = take_off_ref_point.split(',');
        this.startPoint = [parseFloat(array[1]), parseFloat(array[0]), parseFloat(array[2])];
      } else {
        this.startPoint = [];
      }
      this.template_type = template_type
      this.points = placemark.map(({ point, height }) => {
        const [lat, lng] = point.split(',').map(Number);
        return [lat, lng, height];
      });
      //多边形数据
      this.Polygon = res.data?.waylineData?.template_placemark?.polygon
      this.updateMapline();
    },
    updateMapline() {
      const root = getRoot()
      let viewer = root.$cockpitMap

      //删除所有图层
      this.clearWayLine()

      //添加起飞点
      if (this.startPoint && this.startPoint.length > 0) {
        const startAltitude = this.startPoint.length > 2 ? this.startPoint[2] : 0;
        const position = Cesium.Cartesian3.fromDegrees(this.startPoint[0], this.startPoint[1], startAltitude);
        this.overlay = viewer.entities.add({
          position: position,
          billboard: {
            image: '/img/start.png', // 需要添加起飞点图标
            verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
            scale: 0.7,
            width: 38,
            disableDepthTestDistance: Number.POSITIVE_INFINITY,
            // clampToGround: true,
            // heightReference: Cesium.HeightReference.RELATIVE_TO_GROUND,
          },
          label: {
            text: '起飞点',
            font: '14px sans-serif',
            style: Cesium.LabelStyle.FILL_AND_OUTLINE,
            outlinewidth: 4,
            verticalOrigin: Cesium.VerticalOrigin.TOP,
            pixelOffset: new Cesium.Cartesian2(0, -10),
            disableDepthTestDistance: Number.POSITIVE_INFINITY,
            // clampToGround: true,
            // heightReference: Cesium.HeightReference.RELATIVE_TO_GROUND,
          }
        });
      }

      // 如果points数组为空，则不添加连接线和多边形，只保留起飞点
      if (!this.points || this.points.length === 0) {
        // 强制Cesium重新渲染
        if (viewer) {
          viewer.scene.requestRender();
        }
        return;
      }

      //添加起飞点距离第一个航线的连线
      this.createStartPolyline(viewer);

      if (this.template_type === 'waypoint') {
        //添加航线折线数据
        if (this.points.length > 1) {
          console.log(this.points);

          const positions = this.points.map(point =>
            Cesium.Cartesian3.fromDegrees(point[0], point[1], point[2] || 0)
          );

          this.polyline = viewer.entities.add({
            polyline: {
              positions: positions,
              width: 4,
              material: Cesium.Color.RED,
              clampToGround: false // 设置为false，确保折线不贴地
            }
          });
        }
        //添加航点标点
        this.createPointIcont(viewer)
      } else if (this.template_type === 'mapping2d' || this.template_type === 'mapping3d') {
        // 面状航线逻辑 - 无论drawTool是否存在都创建基于points的线条
        if (this.points.length > 0) {
          const positions = this.points.map(point =>
            Cesium.Cartesian3.fromDegrees(point[0], point[1], point[2] || 0)
          );

          this.polyline = viewer.entities.add({
            polyline: {
              positions: positions,
              width: 4,
              material: Cesium.Color.LIME,
              clampToGround: false // 设置为false，确保折线不贴地
            }
          });

          const point = this.points[0]
          const altitude = point.length > 2 ? point[2] : 0;
          const position = Cesium.Cartesian3.fromDegrees(point[0], point[1], altitude);

          // 不再使用高度参考系统，直接使用坐标中的高度值
          const entity = viewer.entities.add({
            position: position,
            billboard: {
              image: (() => {
                const canvas = document.createElement('canvas');
                canvas.width = 36;
                canvas.height = 36;
                const context = canvas.getContext('2d');
                
                context.beginPath();
                context.moveTo(18, 30);  // 底部中点
                context.lineTo(3, 6);    // 左上角
                context.lineTo(33, 6);   // 右上角
                context.closePath();
                
                context.fillStyle = '#2d8cf0';
                context.fill();
                
                context.lineWidth = 2;
                context.strokeStyle = '#ffffff';
                context.stroke();
                
                context.font = 'bold 14px sans-serif';
                context.fillStyle = '#ffffff';
                context.textAlign = 'center';
                context.textBaseline = 'middle';
                context.fillText('S', 18, 14);
                
                return canvas;
              })(),
              verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
              scale: 1.2,
              width: 36,
              height: 36,
              disableDepthTestDistance: Number.POSITIVE_INFINITY
            },
            altitude: altitude
          });

          this.overlayGroups.push(entity);
        }

        // 仅在DrawTool不存在时创建外部多边形
        if (!this.drawTool && this.Polygon && typeof this.Polygon === 'string' && this.Polygon.trim() !== '') {
          this.createOuterRing(viewer);
        }
      }
    },
    createOuterRing(viewer) {
      if (!this.Polygon || typeof this.Polygon !== 'string') return;

      // 解析多边形字符串
      const positionArray = this.Polygon.split('\n')
        .filter(line => line.trim() !== '')
        .map(line => {
          const parts = line.split(',');
          const lng = parseFloat(parts[0]);
          const lat = parseFloat(parts[1]);
          // 读取高度值，如果存在的话
          const height = parts.length > 2 ? parseFloat(parts[2]) : 0;
          return [lng, lat, height];
        });

      if (positionArray.length < 3) return; // 至少需要3个点才能构成多边形

      // 创建Cesium多边形顶点数组
      const positions = positionArray.map(point =>
        Cesium.Cartesian3.fromDegrees(point[0], point[1], point[2] || 0)
      );

      // 添加多边形实体
      const polygonEntity = viewer.entities.add({
        polygon: {
          hierarchy: new Cesium.PolygonHierarchy(positions),
          material: new Cesium.ColorMaterialProperty(
            Cesium.Color.fromCssColorString('#2d8cf0').withAlpha(0.3)
          ),
          outline: true,
          outlineColor: Cesium.Color.fromCssColorString('#2d8cf0'),
          outlinewidth: 4,
          heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
          extrudedHeight: 0, // 添加拉伸高度，默认为0
          clampToGround: true
        },
        polyline: {
          positions: positions,
          clampToGround: true,
          width: 4,
          material: new Cesium.ColorMaterialProperty(
            Cesium.Color.fromCssColorString('#2d8cf0')
          ),
          depthFailMaterial: new Cesium.ColorMaterialProperty(
            Cesium.Color.fromCssColorString('#2d8cf0')
          ),
          classificationType: Cesium.ClassificationType.BOTH,
          shadows: Cesium.ShadowMode.DISABLED,
          distanceDisplayCondition: new Cesium.DistanceDisplayCondition(0, 9999999)
        }
      });

      // 将多边形实体添加到覆盖组
      this.overlayGroups.push(polygonEntity);
    },
    // 添加起飞点到第一个航点的连线
    createStartPolyline(viewer) {
      if (this.startPoint.length > 0 && this.points.length > 0) {
        // 提取起飞点和第一个航点的高度值
        const startAltitude = this.startPoint.length > 2 ? this.startPoint[2] : 0;
        const endAltitude = this.points[0].length > 2 ? this.points[0][2] : 0;

        // 检查当前是否为2D模式
        const is2DMode = viewer.scene.mode === 2;

        // 根据模式创建不同的路径
        let positions;

        if (is2DMode) {
          // 2D模式：直接连接起飞点和第一个航点
          positions = [
            // 起飞点
            Cesium.Cartesian3.fromDegrees(this.startPoint[0], this.startPoint[1], startAltitude),
            // 第一个航点
            Cesium.Cartesian3.fromDegrees(this.points[0][0], this.points[0][1], endAltitude)
          ];
        } else {
          // 3D模式：使用L形路径（先垂直上升再水平移动）
          positions = [
            // 起飞点
            Cesium.Cartesian3.fromDegrees(this.startPoint[0], this.startPoint[1], startAltitude),
            // 垂直上升点（与起飞点经纬度相同，但高度为第一个航点的高度）
            Cesium.Cartesian3.fromDegrees(this.startPoint[0], this.startPoint[1], endAltitude),
            // 第一个航点
            Cesium.Cartesian3.fromDegrees(this.points[0][0], this.points[0][1], endAltitude)
          ];
        }

        this.startPolyline = viewer.entities.add({
          polyline: {
            positions: positions,
            width: 4,
            material: Cesium.Color.LIME,
            clampToGround: false, // 设置为false，确保连线不贴地,
            horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
            verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
          }
        });
      }
    },
    // 添加航点标记
    createPointIcont(viewer) {
      if (this.points.length > 0) {
        this.points.forEach((point, index) => {
          const altitude = point.length > 2 ? Number(point[2]) : 0;
          const position = Cesium.Cartesian3.fromDegrees(point[0], point[1], altitude);

          const entity = viewer.entities.add({
            position: position,
            billboard: {
              image: '/img/mid.png', // 需要添加航点图标
              verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
              scale: 0.7,
              width: 38,
              height: 60,
              disableDepthTestDistance: Number.POSITIVE_INFINITY
            },
            label: {
              text: (index + 1).toString(),
              font: '24px sans-serif',
              style: Cesium.LabelStyle.FILL_AND_OUTLINE,
              outlinewidth: 4,
              verticalOrigin: Cesium.VerticalOrigin.TOP,
              pixelOffset: new Cesium.Cartesian2(0, -10),
              disableDepthTestDistance: Number.POSITIVE_INFINITY
            },
            pointIndex: index, // 添加点位索引标识
            // 添加高度属性以便于后续操作
            altitude: altitude
          });

          this.overlayGroups.push(entity);
        });
      }
    },
    clearWayLine() {
      const root = getRoot();
      let viewer = root.$cockpitMap;

      if (this.overlay) {
        viewer.entities.remove(this.overlay);
        this.overlay = null;
      }

      // 删除航点标识
      if (this.overlayGroups && this.overlayGroups.length > 0) {
        this.overlayGroups.forEach(entity => {
          viewer.entities.remove(entity);
        });
        this.overlayGroups = [];
      }

      // 删除航线
      if (this.polyline) {
        viewer.entities.remove(this.polyline);
        this.polyline = null;
      }

      // 删除起飞点到第一条线
      if (this.startPolyline) {
        viewer.entities.remove(this.startPolyline);
        this.startPolyline = null;
      }

      // 删除距离文字
      if (this.texts && this.texts.length > 0) {
        this.texts.forEach(entity => {
          viewer.entities.remove(entity);
        });
        this.texts = [];  // 修改为空数组而不是null
      }
    },
    // 计算航线两点之间中心点
    getMidPoints(pointA, pointB) {
      // 点A和点B的经纬度坐标
      let lat1 = pointA[1];
      let lon1 = pointA[0];
      let lat2 = pointB[1];
      let lon2 = pointB[0];
      // 将经纬度从十进制转换为弧度
      lat1 = lat1 * Math.PI / 180;
      lon1 = lon1 * Math.PI / 180;
      lat2 = lat2 * Math.PI / 180;
      lon2 = lon2 * Math.PI / 180;
      // 计算中心点的经纬度（弧度）
      let midLat = (lat1 + lat2) / 2;
      let midLon = (lon1 + lon2) / 2;
      // 将结果从弧度转换回十进制
      midLat = midLat * 180 / Math.PI;
      midLon = midLon * 180 / Math.PI;
      return [midLon, midLat];
    },
    handleCloseCockpit() {
      this.$refs.videoMap.onStop();
      this.$emit('closeCockpit', false);
    }
  }
}
</script>
<style lang="scss" scoped>
.cockpitMap {
  position: relative;
  width: 100%;
  height: 100%;
}

.map-container {
  width: 100%;
  height: 100%
}

.map-container-small {
  position: absolute;
  width: 300px;
  height: 200px;
  left: 80vw;
  top: 15vh;
  z-index: 1;
}

.video_box {
  background: rgba(60, 60, 60, 0.38);
  position: absolute;
  bottom: 35vh;
  left: 80vw;
  transform: translateX(-50%);
  border-radius: 0.25rem;
  color: #fff;
  scale: 0.7;
  width: 35vw;
}

.z_index1 {
  z-index: 1;
}

.video-big {
  background: rgba(60, 60, 60, 0.38);
  position: absolute;
  /* bottom: 32vh; */
  /* left: 80vw; */
  /* transform: translateX(-50%); */
  border-radius: 0.25rem;
  color: #fff;
  scale: 1;
  width: 100vw;
  height: 50vh;
  top: 42px;
}

::v-deep(.amap-marker .amap-ranging-label) {
  background: #15b8ff;
}

:deep(.cesium-viewer-bottom) {
  display: none;
}

.map-coordinates-bar {
  position: absolute;
  bottom: 0;
  right: 0;
  height: 30px;
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding: 0 20px;
  z-index: 1000;
}

.coordinates-info {
  display: flex;
  gap: 20px;
  font-size: 14px;
}
</style>