<template>
    <div class="device-select">
        <div class="item-group">
            <el-popover placement="left" :width="200" trigger="click" v-model:visible="algoPopoverVisible">
                <template #reference>
                    <el-button style="width: 80px;" type="primary" size="small" @click="selectAlgorith">选择算法</el-button>
                </template>
                <div class="algo-list">
                    <div class="algo-item" @click="selectAlgo({})">
                        原始画面
                    </div>
                    <div v-for="algo in algorithmList" :key="algo.id" class="algo-item" @click="selectAlgo(algo)">
                        {{ algo.name || algo.algorithm_name }}
                    </div>
                </div>
            </el-popover>
        </div>
        <div class="item-group">
            终端:<el-select v-model="currentDockSn" placeholder="请选择机场">
                <el-option v-for="item in mergedDevices" :key="item.sn" :value="item.sn"
                    :label="item.gateway.callsign"></el-option>
            </el-select>
        </div>
        <div class="item-group">
            设备:{{ currentDockInfo?.model }}
        </div>
    </div>
</template>

<script setup>
import { ref, onMounted, watch, reactive, computed, onBeforeUnmount } from 'vue';
import { getDeviceTopo, getUnreadDeviceHms, updateDeviceHms } from '@/api/manage/index';
import { getPlayAlgoList } from '@/api/workSite/index.js';
import { EDeviceTypeName, ELocalStorageKey } from '@/types';
import { EModeCode, EDockModeCode } from '@/types/device'
import { getWorkspaceId } from '@/utils/storage';
import EventBus from '@/event-bus';
import { ElMessage } from 'element-plus';

const emit = defineEmits(['changeUrl', 'changeDomain']);

const onlineDocks = reactive({
    data: [],
});
const onlineDevices = reactive({
    data: [],
});
const currentDockSn = ref('')
const algoPopoverVisible = ref(false);

// 合并 onlineDocks 和 onlineDevices 的数据
const mergedDevices = computed(() => {
    return [...onlineDocks.data, ...onlineDevices.data];
});

// 计算属性：根据SN获取完整的机场信息
const currentDockInfo = computed(() => {
    return mergedDevices.value.find(item => item.sn === currentDockSn.value) || {}
})

onMounted(() => {
    getOnlineTopo()
    
    // 监听算法状态变更事件
    EventBus.on('algoStatusChanged', (data) => {
        if (data && data.sn === currentDockSn.value) {
            fetchAlgoHosts(currentDockSn.value);
        }
    });
})

const getOnlineTopo = () => {
    getDeviceTopo(getWorkspaceId()).then(res => {
        if (res.data.code !== 0) {
            return;
        }

        onlineDevices.data = [];
        onlineDocks.data = [];
        res.data.data.forEach((gateway) => {
            const child = gateway.children
            const device = {
                model: child?.device_name,
                callsign: child?.nickname,
                sn: child?.device_sn,
                mode: EModeCode.未连接,
                gateway: {
                    model: gateway?.device_name,
                    callsign: gateway?.nickname,
                    sn: gateway?.device_sn,
                    domain: gateway?.domain
                },
                payload: []
            }
            child?.payloads_list?.forEach((payload) => {
                device.payload.push({
                    index: payload.index,
                    model: payload.model,
                    payload_name: payload.payload_name,
                    payload_sn: payload.payload_sn,
                    control_source: payload.control_source,
                    payload_index: payload.payload_index
                })
            })
            if (EDeviceTypeName.Dock === gateway.domain) {
                onlineDocks.data.push(device)
            }

            if (gateway.status && EDeviceTypeName.Gateway === gateway.domain) {
                onlineDevices.data.push(device)
                // 更新 Vuex 中的 deviceInfo
                if (device.sn) {
                    store.commit('UPDATE_DEVICE_INFO', {
                        sn: device.sn,
                        model: device.model,
                        callsign: device.callsign
                    })
                }
            }
        })
        
        // 如果列表有数据，自动选择第一个机场
        if (mergedDevices.value.length > 0) {
            nextTick(() => {
                currentDockSn.value = mergedDevices.value[0].sn;
            })
        }
    });
}

const algorithmList = ref([])
const fetchAlgoHosts = async (sn) => {
    if (!sn) return;
    
    try {
        const res = await getPlayAlgoList(1, 100, { sn })
        algorithmList.value = res.data.data
    } catch (error) {
        console.error('获取算法列表失败:', error);
    }
}

// 组件卸载前移除事件监听
onBeforeUnmount(() => {
    EventBus.off('algoStatusChanged');
});

watch(currentDockSn, async (newDockSn) => {
    if (newDockSn) {
        const dockInfo = currentDockInfo.value
        emit('changeDomain', dockInfo)
        await fetchAlgoHosts(newDockSn)
    }
})

const selectAlgorith = () => {
    // 弹出窗口显示由el-popover的v-model:visible控制
    if (!currentDockSn.value) {
        // 如果没有选择机场，可以给用户提示
        ElMessage.warning('请先选择机场');
        algoPopoverVisible.value = false;
    }
}

const selectAlgo = (algo) => {
    // 发送选中的算法信息给父组件
    console.log(algo);

    emit('changeUrl', algo.httpUrl);
    // 关闭弹窗
    algoPopoverVisible.value = false;
}
</script>

<style lang="scss" scoped>
.device-select {
    display: flex;
    align-items: center;
    flex-direction: row;
    flex-wrap: nowrap;
    gap: 20px;
    margin: 10px;
    background-color: #454f5d;
    color: white;
    padding: 10px;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);

    .item-group {
        display: flex;
        align-items: center;
        white-space: nowrap;
    }

    :deep(.el-select) {
        margin-left: 8px;
        width: 140px;
    }

    :deep(.el-input__wrapper) {
        background-color: rgba(255, 255, 255, 0.1);
        box-shadow: 0 0 0 1px rgba(255, 255, 255, 0.2) inset;
    }

    :deep(.el-input__inner) {
        color: white;
    }

    :deep(.el-select__popper) {
        background-color: #454f5d;
        border-color: rgba(255, 255, 255, 0.2);
    }
}

.algo-list {
    max-height: 250px;
    overflow-y: auto;

    .algo-item {
        padding: 8px 12px;
        cursor: pointer;
        transition: background-color 0.2s;

        &:hover {
            background-color: #409eff;
            color: white;
        }
    }
}
</style>