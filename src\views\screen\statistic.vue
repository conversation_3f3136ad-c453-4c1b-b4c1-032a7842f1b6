<template>
  <div class="main">
    <div class="left">
      <div class="search" style="margin-top: 2vh;">
        <el-date-picker v-model="searchTime" type="daterange" start-placeholder="开始时间" end-placeholder="结束时间"
          range-separator="-" @change="changeTime" @clear="clearTime" style="width: 98%;" />

        <div class="titleBg" style="margin-top: 2vh;">
          <span>飞行时长</span>
        </div>
        <div class="count">总时长：{{ Math.round(totalTime) }} 分钟</div>
        <div ref="chartDom1" class="chartDom1"></div>

        <div class="titleBg">
          <span>飞行里程</span>
        </div>
        <div class="count">总里程：{{ Math.round(totalDistance) }} m</div>
        <div ref="chartDom2" class="chartDom2"></div>
      </div>
    </div>
    <div class="right">
      <div class="titleBg" style="margin-top: 7vh;">
        <span>飞行架次</span>
      </div>
      <div class="count" style="text-align: right;">平均 {{ averageSortie }} 次</div>
      <div ref="chartDom3" class="chartDom3"></div>

      <div class="titleBg">
        <span>飞行速度</span>
      </div>
      <div class="count" style="text-align: right;">平均 {{ averageSpeed }} m/s</div>
      <div ref="chartDom4" class="chartDom4"></div>
    </div>
    <div class="center">
      <div class="topSummaryItem">
        <div style="height: 4vh;text-align: center;">总飞行架次</div>
        <img src="@/assets/screen/statisticBg.png" alt="" />
        <div class="text">{{ data.allFlightCount }}</div>
      </div>
      <div class="topSummaryItem">
        <div style="height: 4vh;text-align: center;">总飞行时长</div>
        <img src="@/assets/screen/statisticBg.png" alt="" />
        <div class="text">{{ Math.round(data.allFlightTime / 60) }}</div>
      </div>
      <div class="topSummaryItem">
        <div style="height: 4vh;text-align: center;">总飞行里程</div>
        <img src="@/assets/screen/statisticBg.png" alt="" />
        <div class="text">{{ data.allFlightDistance }}</div>
      </div>
    </div>
  </div>
</template>

<script setup>
import moment from 'moment';
import * as echarts from 'echarts';
import { ref, onMounted, onUnmounted } from 'vue';
import { FlightStat } from '@/api/screen';

let data = ref({});
let totalTime = ref(null);
let totalDistance = ref(null);
let averageSortie = ref(null);
let averageSpeed = ref(null);
let searchTime = ref(null);
let startTime = ref(null);
let endTime = ref(null);
let myChart1 = null;
let chartDom1 = ref(null);
let myChart2 = null;
let chartDom2 = ref(null);
let myChart3 = null;
let chartDom3 = ref(null);
let myChart4 = null;
let chartDom4 = ref(null);

const init = async () => {
  let params = {
    startTime: startTime.value,
    endTime: endTime.value,
  }
  await FlightStat(params).then(res => {
    data.value = res.data.data
    data.value.allFlightDistance = Math.round(data.value.allFlightDistance)
    // 求飞行总时长
    totalTime.value = data.value.flightTime.length > 0
      ? data.value.flightTime.map(Number).reduce((i, k) => i + k, 0) / 60
      : 0;
    // 求飞行总里程
    totalDistance.value = data.value.flightDistance.length > 0
      ? data.value.flightDistance.map(Number).reduce((i, k) => i + k, 0)
      : 0;
    // 求平均飞行架次
    averageSortie.value = data.value.flightCount.length > 0 
      ? Math.round(data.value.flightCount.reduce((i, k) => i + k, 0) / data.value.flightCount.length)
      : 0;
    // 求平均飞行速度
    let numbers = data.value.flightDistanceAvg.map(Number);
    let sum = numbers.length > 0 ? numbers.reduce((i, k) => i + k, 0) : 0;
    averageSpeed.value = numbers.length > 0 ? (sum / numbers.length).toFixed(2) : '0.00';
  })
  window.addEventListener('resize', resizeChart);
  initEchart1();
  initEchart2();
}

const setDefaultTimeRange = () => {
  const endDate = new Date();
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - 90);
  searchTime.value = [startDate, endDate];
  changeTime();
};

const changeTime = () => {
  if (searchTime.value) {
    let Dates = searchTime.value.map(date => {
      return moment(date).format('YYYY-MM-DD');
    });
    startTime.value = Dates[0]
    endTime.value = Dates[1]
    init();
  }
}

const clearTime = () => {
  const endDate = new Date();
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - 90);
  searchTime.value = [startDate, endDate];
  changeTime();
}

const initEchart1 = () => {
  myChart1 = echarts.init(chartDom1.value);
  myChart3 = echarts.init(chartDom3.value);
  const CubeLeft = echarts.graphic.extendShape({
    shape: { x: 0, y: 0 },
    buildPath: function (ctx, shape) {
      const xAxisPoint = shape.xAxisPoint
      const c0 = [shape.x, shape.y]
      const c1 = [shape.x - 9, shape.y - 9]
      const c2 = [xAxisPoint[0] - 9, xAxisPoint[1] - 9]
      const c3 = [xAxisPoint[0], xAxisPoint[1]]
      ctx.moveTo(c0[0], c0[1]).lineTo(c1[0], c1[1]).lineTo(c2[0], c2[1]).lineTo(c3[0], c3[1]).closePath()
    }
  })
  const CubeRight = echarts.graphic.extendShape({
    shape: { x: 0, y: 0 },
    buildPath: function (ctx, shape) {
      const xAxisPoint = shape.xAxisPoint
      const c1 = [shape.x, shape.y]
      const c2 = [xAxisPoint[0], xAxisPoint[1]]
      const c3 = [xAxisPoint[0] + 18, xAxisPoint[1] - 9]
      const c4 = [shape.x + 18, shape.y - 9]
      ctx.moveTo(c1[0], c1[1]).lineTo(c2[0], c2[1]).lineTo(c3[0], c3[1]).lineTo(c4[0], c4[1]).closePath()
    }
  })
  const CubeTop = echarts.graphic.extendShape({
    shape: { x: 0, y: 0 },
    buildPath: function (ctx, shape) {
      const c1 = [shape.x, shape.y]
      const c2 = [shape.x + 18, shape.y - 9]
      const c3 = [shape.x + 9, shape.y - 18]
      const c4 = [shape.x - 9, shape.y - 9]
      ctx.moveTo(c1[0], c1[1]).lineTo(c2[0], c2[1]).lineTo(c3[0], c3[1]).lineTo(c4[0], c4[1]).closePath()
    }
  })
  echarts.graphic.registerShape('CubeLeft', CubeLeft)
  echarts.graphic.registerShape('CubeRight', CubeRight)
  echarts.graphic.registerShape('CubeTop', CubeTop)

  const TIME = data.value.datas
  const MAX1 = data.value.flightTime.map(item => item = Math.round(item / 60))
  const VALUE1 = data.value.flightTime.map(item => item = Math.round(item / 60))
  const MAX2 = data.value.flightCount
  const VALUE2 = data.value.flightCount
  let option1 = {
    grid: {
      left: 10,
      right: 10,
      top: "4%",
      bottom: "10%",
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: TIME,
      axisLine: {
        show: true,
        lineStyle: {
          color: 'white'
        }
      },
      offset: 20,
      axisTick: {
        show: false,
        length: 9,
        alignWithLabel: true,
        lineStyle: {
          color: '#7DFFFD'
        }
      },
      axisLabel: {
        fontSize: 10
      }
    },
    yAxis: {
      type: 'value',
      min: 0,
      axisLine: {
        show: true,
        lineStyle: {
          color: 'white'
        }
      },
      splitLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        fontSize: 12
      },
      boundaryGap: ['20%', '20%']
    },
    series: [
      {
        type: 'custom',
        renderItem: function (params, api) {
          const location = api.coord([api.value(0), api.value(1)])
          return {
            type: 'group',
            children: [{
              type: 'CubeLeft',
              shape: {
                api,
                xValue: api.value(0),
                yValue: api.value(1),
                x: location[0],
                y: location[1],
                xAxisPoint: api.coord([api.value(0), 0])
              },
              style: {
                fill: 'rgba(47,102,192,.27)'
              }
            }, {
              type: 'CubeRight',
              shape: {
                api,
                xValue: api.value(0),
                yValue: api.value(1),
                x: location[0],
                y: location[1],
                xAxisPoint: api.coord([api.value(0), 0])
              },
              style: {
                fill: 'rgba(59,128,226,.27)'
              }
            }, {
              type: 'CubeTop',
              shape: {
                api,
                xValue: api.value(0),
                yValue: api.value(1),
                x: location[0],
                y: location[1],
                xAxisPoint: api.coord([api.value(0), 0])
              },
              style: {
                fill: 'rgba(72,156,221,.27)'
              }
            }]
          }
        },
        data: MAX1
      },
      {
        type: 'custom',
        renderItem: (params, api) => {
          const location = api.coord([api.value(0), api.value(1)])
          return {
            type: 'group',
            children: [{
              type: 'CubeLeft',
              shape: {
                api,
                xValue: api.value(0),
                yValue: api.value(1),
                x: location[0],
                y: location[1],
                xAxisPoint: api.coord([api.value(0), 0])
              },
              style: {
                fill: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                  offset: 0,
                  color: '#3B80E2'
                },
                {
                  offset: 1,
                  color: '#49BEE5'
                }
                ])
              }
            }, {
              type: 'CubeRight',
              shape: {
                api,
                xValue: api.value(0),
                yValue: api.value(1),
                x: location[0],
                y: location[1],
                xAxisPoint: api.coord([api.value(0), 0])
              },
              style: {
                fill: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                  offset: 0,
                  color: '#3B80E2'
                },
                {
                  offset: 1,
                  color: '#49BEE5'
                }
                ])
              }
            }, {
              type: 'CubeTop',
              shape: {
                api,
                xValue: api.value(0),
                yValue: api.value(1),
                x: location[0],
                y: location[1],
                xAxisPoint: api.coord([api.value(0), 0])
              },
              style: {
                fill: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                  offset: 0,
                  color: '#3B80E2'
                },
                {
                  offset: 1,
                  color: '#49BEE5'
                }
                ])
              }
            }]
          }
        },
        data: VALUE1
      },
      {
        type: 'bar',
        label: {
          normal: {
            show: true,
            position: 'top',
            fontSize: 12,
            color: '#FFFFFF',
            offset: [4, -16]
          }
        },
        itemStyle: {
          color: 'transparent'
        },
        data: MAX1
      }
    ]
  }
  option1 && myChart1.setOption(option1);

  let option3 = {
    grid: {
      left: 10,
      right: 10,
      top: "4%",
      bottom: "10%",
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: TIME,
      axisLine: {
        show: true,
        lineStyle: {
          color: 'white'
        }
      },
      offset: 20,
      axisTick: {
        show: false,
        length: 9,
        alignWithLabel: true,
        lineStyle: {
          color: '#7DFFFD'
        }
      },
      axisLabel: {
        fontSize: 10
      }
    },
    yAxis: {
      type: 'value',
      min: 0,
      axisLine: {
        show: true,
        lineStyle: {
          color: 'white'
        }
      },
      splitLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        fontSize: 12
      },
      boundaryGap: ['20%', '20%']
    },
    series: [
      {
        type: 'custom',
        renderItem: function (params, api) {
          const location = api.coord([api.value(0), api.value(1)])
          return {
            type: 'group',
            children: [{
              type: 'CubeLeft',
              shape: {
                api,
                xValue: api.value(0),
                yValue: api.value(1),
                x: location[0],
                y: location[1],
                xAxisPoint: api.coord([api.value(0), 0])
              },
              style: {
                fill: 'rgba(47,102,192,.27)'
              }
            }, {
              type: 'CubeRight',
              shape: {
                api,
                xValue: api.value(0),
                yValue: api.value(1),
                x: location[0],
                y: location[1],
                xAxisPoint: api.coord([api.value(0), 0])
              },
              style: {
                fill: 'rgba(59,128,226,.27)'
              }
            }, {
              type: 'CubeTop',
              shape: {
                api,
                xValue: api.value(0),
                yValue: api.value(1),
                x: location[0],
                y: location[1],
                xAxisPoint: api.coord([api.value(0), 0])
              },
              style: {
                fill: 'rgba(72,156,221,.27)'
              }
            }]
          }
        },
        data: MAX2
      },
      {
        type: 'custom',
        renderItem: (params, api) => {
          const location = api.coord([api.value(0), api.value(1)])
          return {
            type: 'group',
            children: [{
              type: 'CubeLeft',
              shape: {
                api,
                xValue: api.value(0),
                yValue: api.value(1),
                x: location[0],
                y: location[1],
                xAxisPoint: api.coord([api.value(0), 0])
              },
              style: {
                fill: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                  offset: 0,
                  color: '#3B80E2'
                },
                {
                  offset: 1,
                  color: '#49BEE5'
                }
                ])
              }
            }, {
              type: 'CubeRight',
              shape: {
                api,
                xValue: api.value(0),
                yValue: api.value(1),
                x: location[0],
                y: location[1],
                xAxisPoint: api.coord([api.value(0), 0])
              },
              style: {
                fill: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                  offset: 0,
                  color: '#3B80E2'
                },
                {
                  offset: 1,
                  color: '#49BEE5'
                }
                ])
              }
            }, {
              type: 'CubeTop',
              shape: {
                api,
                xValue: api.value(0),
                yValue: api.value(1),
                x: location[0],
                y: location[1],
                xAxisPoint: api.coord([api.value(0), 0])
              },
              style: {
                fill: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                  offset: 0,
                  color: '#3B80E2'
                },
                {
                  offset: 1,
                  color: '#49BEE5'
                }
                ])
              }
            }]
          }
        },
        data: VALUE2
      },
      {
        type: 'bar',
        label: {
          normal: {
            show: true,
            position: 'top',
            fontSize: 12,
            color: '#FFFFFF',
            offset: [4, -16]
          }
        },
        itemStyle: {
          color: 'transparent'
        },
        data: MAX2
      }
    ]
  }
  option3 && myChart3.setOption(option3);
};

const initEchart2 = () => {
  myChart2 = echarts.init(chartDom2.value);
  myChart4 = echarts.init(chartDom4.value);

  let chartsDataX = data.value.datas
  let chartsDataY1 = data.value.flightDistance
  let chartsDataY2 = data.value.flightDistanceAvg
  let option2 = {
    grid: {
      top: '8%',
      left: '1%',
      right: '1%',
      bottom: '1%',
      containLabel: true,
    },
    legend: {
      itemGap: 50,
      textStyle: {
        color: '#f9f9f9',
        borderColor: '#fff',
      },
    },
    xAxis: [
      {
        type: 'category',
        axisLine: {
          show: false, //设置为false来隐藏刻度线
        },
        axisLabel: {
          textStyle: {
            color: '#fff',
            margin: 15,
          },
        },
        axisTick: {
          show: false,
        },
        data: chartsDataX,
      },
    ],
    yAxis: [
      {
        type: 'value',
        min: 0,
        splitNumber: 7,
        splitLine: {
          // 设置分割线（刻度线）
          show: true, // 显示分割线
          lineStyle: {
            color: ['#989797'], // 设置颜色为灰色
            type: 'dashed', // 设置线型为虚线
          },
        },
        axisLine: {
          show: false,
        },
        axisLabel: {
          margin: 20,
          textStyle: {
            color: '#fff',
          },
        },
        axisTick: {
          show: false,
        },
      },
    ],
    series: [
      {
        type: 'line',
        //smooth: true, //是否平滑曲线显示
        //symbol:'circle', //默认是空心圆（中间是白色的），改成实心圆
        showAllSymbol: true,
        symbol: 'emptyCircle',
        symbolSize: 6,
        lineStyle: {
          normal: {
            color: '#0073FF', //线条颜色
          },
        },
        label: {
          show: true,
          position: 'top',
          textStyle: {
            color: '#fff',
          },
        },
        itemStyle: {
          normal: {
            color: '#0073FF',
          },
        },
        tooltip: {
          show: false,
        },
        areaStyle: {
          //区域填充样式
          normal: {
            color: new echarts.graphic.LinearGradient(
              0,
              0,
              0,
              1,
              [
                { offset: 0, color: '#0073ff' },
                { offset: 1, color: 'rgba(0,0,0,0)' },
              ],
              false
            ),
            shadowColor: '#50ACFF', //阴影颜色
            shadowBlur: 20,
          },
        },
        data: chartsDataY1,
      },
    ],
  };
  option2 && myChart2.setOption(option2);

  let option4 = {
    grid: {
      top: '8%',
      left: '1%',
      right: '1%',
      bottom: '1%',
      containLabel: true,
    },
    legend: {
      itemGap: 50,
      textStyle: {
        color: '#f9f9f9',
        borderColor: '#fff',
      },
    },
    xAxis: [
      {
        type: 'category',
        axisLine: {
          show: false, //设置为false来隐藏刻度线
        },
        axisLabel: {
          textStyle: {
            color: '#fff',
            margin: 15,
          },
        },
        axisTick: {
          show: false,
        },
        data: chartsDataX,
      },
    ],
    yAxis: [
      {
        type: 'value',
        min: 0,
        splitNumber: 7,
        splitLine: {
          // 设置分割线（刻度线）
          show: true, // 显示分割线
          lineStyle: {
            color: ['#989797'], // 设置颜色为灰色
            type: 'dashed', // 设置线型为虚线
          },
        },
        axisLine: {
          show: false,
        },
        axisLabel: {
          margin: 20,
          textStyle: {
            color: '#fff',
          },
        },
        axisTick: {
          show: false,
        },
      },
    ],
    series: [
      {
        type: 'line',
        //smooth: true, //是否平滑曲线显示
        //symbol:'circle', //默认是空心圆（中间是白色的），改成实心圆
        showAllSymbol: true,
        symbol: 'emptyCircle',
        symbolSize: 6,
        lineStyle: {
          normal: {
            color: '#0073FF', //线条颜色
          },
        },
        label: {
          show: true,
          position: 'top',
          textStyle: {
            color: '#fff',
          },
        },
        itemStyle: {
          normal: {
            color: '#0073FF',
          },
        },
        tooltip: {
          show: false,
        },
        areaStyle: {
          //区域填充样式
          normal: {
            color: new echarts.graphic.LinearGradient(
              0,
              0,
              0,
              1,
              [
                { offset: 0, color: '#0073ff' },
                { offset: 1, color: 'rgba(0,0,0,0)' },
              ],
              false
            ),
            shadowColor: '#50ACFF', //阴影颜色
            shadowBlur: 20,
          },
        },
        data: chartsDataY2,
      },
    ],
  };
  option4 && myChart4.setOption(option4);
};

const resizeChart = () => {
  myChart1 && myChart1.resize();
  myChart2 && myChart2.resize();
  myChart3 && myChart3.resize();
  myChart4 && myChart4.resize();
}

onMounted(() => {
  setDefaultTimeRange();
});

onUnmounted(() => {
  window.removeEventListener('resize', resizeChart);
  myChart1.dispose();
  myChart2.dispose();
  myChart3.dispose();
  myChart4.dispose();
});
</script>

<style lang="scss" scoped>
.main {
  width: 100%;
  height: 100%;
  position: relative;
}

.left {
  position: absolute;
  left: 1.3vw;
  top: 0;
  width: 29.4vw;
  height: 100%;

  .search {
    width: 100%;
    height: 3.5vh;
  }

  .chartDom1 {
    width: 100%;
    height: 34vh;
  }

  .chartDom2 {
    width: 100%;
    height: 34vh;
  }
}

.right {
  position: absolute;
  right: 1.3vw;
  top: 0;
  width: 29.4vw;
  height: 100%;

  .chartDom3 {
    width: 100%;
    height: 34vh;
  }

  .chartDom4 {
    width: 100%;
    height: 34vh;
  }
}

.center {
  position: fixed;
  top: 10vh;
  left: 36vw;
  width: 28vw;
  height: 7vh;
  display: flex;
  justify-content: space-between;

  .topSummaryItem {
    width: 33%;
    height: 100%;
    position: relative;

    .text {
      width: 100%;
      line-height: 20px;
      font-size: 24px;
      text-align: center;
      font-weight: bold;
      margin: 0;
      background: linear-gradient(180deg, #478AFF 0%, #FFFFFF 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }

    img {
      position: absolute;
      bottom: 0;
      left: 20px;
      z-index: -1;
    }
  }
}

.count {
  height: 30px;
  line-height: 30px;
  color: #0084FF;
}

.titleBg {
  width: 29.4vw;
  height: 40px;
  line-height: 40px;
  background-position: center center;
  background-image: url('@/assets/screen/titleBg.png');

  span {
    margin-left: 42px;
    font-size: 16px;
    letter-spacing: .1em;
  }
}

:deep(.el-date-editor.el-input__wrapper) {
  background: rgba(11, 35, 57, 0.4);
  box-shadow: 0 0 0 1px #09559a inset;
}

:deep(.el-date-editor .el-range__icon) {
  color: #FFFFFF;
}

:deep(.el-date-editor .el-range-input) {
  color: #FFFFFF;
}
</style>