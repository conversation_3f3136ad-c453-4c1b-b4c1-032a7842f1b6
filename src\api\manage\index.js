import request from '@/axios';
import {ELocalStorageKey } from '@/api/enum/index';
const HTTP_PREFIX = '/hztech-flight-core/manage/api/v1'
const HTTP_live_PREFIX = '/hztech-flight-core/'

//成员列表
export const login = (id, params) => {
  return request({
    url: `${HTTP_PREFIX}/login`,
    method: 'post',
    params: {
      ...params,
    },
  });
};
export const getDeviceBySn = async function (workspace_id,device_sn) {
  return request({
    url:  `${HTTP_PREFIX}/devices/${workspace_id}/devices/${device_sn}`,
    method: 'get',
  });
}
export const getPlatformInfo = async function () {
  return request({
    url: `${HTTP_PREFIX}/workspaces/current`,
    method: 'get'
  });
}
export const getUserInfo = async function (username,workSpaceId) {
  return request({
    url: `${HTTP_PREFIX}/users/current?username=${username}&workSpaceId=${workSpaceId}`,
    method: 'get'
  });
}

export const bindDevice = async function (params) {
  return request({
    url:`${HTTP_PREFIX}/devices/${params.device_sn}/binding`,
    method: 'post',
    data:params,
  });
}


// Refresh Token
export const refreshToken = async function (body) {
  const url = `${HTTP_PREFIX}/token/refresh`
  const result = await request.post(url, body)
  return result.data
}



export const getDeviceTopo = async function (workspace_id) {
  return request({
    url: `${HTTP_PREFIX}/devices/${workspace_id}/devices`,
    method: 'get'
  });
}


// // Get Livestream Capacity
// export const getLiveCapacity = async function (body: {}): Promise<IWorkspaceResponse<any>> {
//   const url = `${HTTP_PREFIX}/live/capacity`
//   const result = await request.get(url, body)
//   return result.data
// }

// // Start Livestream
// export const startLivestream = async function (body: {}): Promise<IWorkspaceResponse<any>> {
//   const url = `${HTTP_PREFIX}/live/streams/start`
//   const result = await request.post(url, body)
//   return result.data
// }

// // Stop Livestream
// export const stopLivestream = async function (body: {}): Promise<IWorkspaceResponse<any>> {
//   const url = `${HTTP_PREFIX}/live/streams/stop`
//   const result = await request.post(url, body)
//   return result.data
// }
// // Update Quality
// export const setLivestreamQuality = async function (body: {}): Promise<IWorkspaceResponse<any>> {
//   const url = `${HTTP_PREFIX}/live/streams/update`
//   const result = await request.post(url, body)
//   return result.data
// }

// export const getAllUsersInfo = async function (wid: string, body: IPage): Promise<CommonListResponse<any>> {
//   const url = `${HTTP_PREFIX}/users/${wid}/users?&page=${body.page}&page_size=${body.page_size}`
//   const result = await request.get(url)
//   return result.data
// }

// export const updateUserInfo = async function (wid: string, user_id: string, body: {}): Promise<IWorkspaceResponse<any>> {
//   const url = `${HTTP_PREFIX}/users/${wid}/users/${user_id}`
//   const result = await request.put(url, body)
//   return result.data
// }



export const unbindDevice = async function (device_sn) {
  const url = `${HTTP_PREFIX}/devices/${device_sn}/unbinding`
  const result = await request.delete(url)
  return result.data
}


// /**
//  * 获取绑定设备信息
//  * @param workspace_id
//  * @param body
//  * @param domain
//  * @returns
//  */
// export const getBindingDevices = async function (workspace_id: string, body: IPage, domain: number): Promise<IListWorkspaceResponse<Device>> {
//   const url = `${HTTP_PREFIX}/devices/${workspace_id}/devices/bound?&page=${body.page}&page_size=${body.page_size}&domain=${domain}`
//   const result = await request.get(url)
//   return result.data
// }

// export const updateDevice = async function (body: {}, workspace_id: string, device_sn: string): Promise<IWorkspaceResponse<any>> {
//   const url = `${HTTP_PREFIX}/devices/${workspace_id}/devices/${device_sn}`
//   const result = await request.put(url, body)
//   return result.data
// }

export const getUnreadDeviceHms = async function (workspace_id, device_sn) {
  const url = `${HTTP_PREFIX}/devices/${workspace_id}/devices/hms/${device_sn}`
  const result = await request.get(url)
  return result.data
}

export const updateDeviceHms = async function (workspace_id, device_sn){
  const url = `${HTTP_PREFIX}/devices/${workspace_id}/devices/hms/${device_sn}`
  const result = await request.put(url)
  return result.data
}

// export const getDeviceHms = async function (body: HmsQueryBody, workspace_id: string, pagination: IPage): Promise<IListWorkspaceResponse<any>> {
//   let url = `${HTTP_PREFIX}/devices/${workspace_id}/devices/hms?page=${pagination.page}&page_size=${pagination.page_size}` +
//     `&level=${body.level ?? ''}&begin_time=${body.begin_time ?? ''}&end_time=${body.end_time ?? ''}&message=${body.message ?? ''}&language=${body.language}`
//   body.sns.forEach((sn: string) => {
//     if (sn !== '') {
//       url = url.concat(`&device_sn=${sn}`)
//     }
//   })
//   const result = await request.get(url)
//   return result.data
// }

// export const changeLivestreamLens = async function (body: {}): Promise<IWorkspaceResponse<any>> {
//   const url = `${HTTP_PREFIX}/live/streams/switch`
//   const result = await request.post(url, body)
//   return result.data
// }

// export const getFirmwares = async function (workspace_id: string, page: IPage, body: FirmwareQueryParam): Promise<IListWorkspaceResponse<Firmware>> {
//   const url = `${HTTP_PREFIX}/workspaces/${workspace_id}/firmwares?page=${page.page}&page_size=${page.page_size}` +
//     `&device_name=${body.device_name}&product_version=${body.product_version}&status=${body.firmware_status ?? ''}`
//   const result = await request.get(url)
//   return result.data
// }

// export const importFirmareFile = async function (workspaceId: string, param: FormData): Promise<IWorkspaceResponse<any>> {
//   const url = `${HTTP_PREFIX}/workspaces/${workspaceId}/firmwares/file/upload`
//   const result = await request.post(url, param)
//   return result.data
// }

// export const changeFirmareStatus = async function (workspaceId: string, firmwareId: string, param: {status: boolean}): Promise<IWorkspaceResponse<any>> {
//   const url = `${HTTP_PREFIX}/workspaces/${workspaceId}/firmwares/${firmwareId}`
//   const result = await request.put(url, param)
//   return result.data
// }

// 根据设备序列号获取视频设备信息 getMediaGb28181
export const getMediaGb28181 = async function (params) {
  return request({
    url:  `${HTTP_live_PREFIX}/app/liveMedia/getMediaGb28181`,
    method: 'get',
    params: {
      ...params,
    },
  });
}