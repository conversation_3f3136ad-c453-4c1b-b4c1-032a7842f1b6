<template>
  <div class="main-container">
    <div class="returnL" @click="goBack"></div>
    <div class="returnR" @click="goBack"></div>

    <div class="title">
      <div class="menuL">
        <div :class="{'active': menuIndex === 1}" @click="toMenu(1)">设备总览</div>
        <div :class="{'active': menuIndex === 2}" @click="toMenu(2)">飞行统计</div>
      </div>
      <div class="menuR">
        <div :class="{'active': menuIndex === 3}" @click="toMenu(3)">飞行记录</div>
        <div :class="{'active': menuIndex === 4}" @click="toMenu(4)">航线总览</div>
      </div>
    </div>

    <div class="component">
      <device v-if="menuIndex === 1"/>
      <statistic v-if="menuIndex === 2"/>
      <record v-if="menuIndex === 3"/>
      <airline v-if="menuIndex === 4"/>
    </div>
  </div>
</template>

<script setup>
import {ref} from 'vue';
import {useRouter} from 'vue-router';
import device from './device.vue'; // 设备总览
import statistic from './statistic.vue'; // 飞行统计
import record from './record.vue'; // 飞行记录
import airline from './airline.vue'; // 航线总览

let menuIndex = ref(1);
let router = useRouter();

const goBack = () => {
  router.push({
      path: '/',
      query: {
        target: 'screen'
      }
    });
}

const toMenu = index => {
  menuIndex.value = index;
}
</script>

<style lang="scss" scoped>
.main-container {
  width: 100vw;
  height: 100vh;
  background-size: 100% 100%;
  background-position: center center;
  background-image: url('@/assets/screen/background.png');
  color: #FFFFFF;

  .returnL {
    position: fixed;
    top: 2.6vh;
    left: 1vw;
    width: 80px;
    height: 40px;
    cursor: pointer;
    background-size: 100% 100%;
    background-position: center center;
    background-image: url('@/assets/screen/returnL.png');
  }

  .returnR {
    position: fixed;
    top: 2.6vh;
    right: 1vw;
    width: 80px;
    height: 40px;
    cursor: pointer;
    background-size: 100% 100%;
    background-position: center center;
    background-image: url('@/assets/screen/returnR.png');
  }

  .title {
    width: 100%;
    height: 8.7vh;
    background-size: 100% 100%;
    background-position: center center;
    background-image: url('@/assets/screen/title.png');
    font-size: 16px;
    font-weight: bold;
    letter-spacing: 0.2em;

    .menuL {
      position: fixed;
      top: 2.6vh;
      left: 6vw;
      width: 360px;
      height: 40px;
      display: flex;
      justify-content: space-between;

      div {
        width: 166px;
        height: 40px;
        line-height: 40px;
        text-align: center;
        background-size: 100% 100%;
        background-position: center center;
        background-image: url('@/assets/screen/menuL.png');
        cursor: pointer;
      }

      div.active {
        background-image: url('@/assets/screen/menuL_selected.png');
      }
    }

    .menuR {
      position: fixed;
      top: 2.6vh;
      right: 6vw;
      width: 360px;
      height: 40px;
      display: flex;
      justify-content: space-between;

      div {
        width: 166px;
        height: 40px;
        line-height: 40px;
        text-align: center;
        background-size: 100% 100%;
        background-position: center center;
        background-image: url('@/assets/screen/menuR.png');
        cursor: pointer;
      }

      div.active {
        background-image: url('@/assets/screen/menuR_selected.png');
      }
    }
  }

  .component {
    width: 100%;
    height: 91.3vh;
  }
}
</style>