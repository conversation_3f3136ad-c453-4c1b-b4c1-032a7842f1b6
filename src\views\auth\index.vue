<template>
    <div class="auth-container">
        <div v-if="loading" class="loading">
            <p>登录中...</p>
        </div>
    </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useStore } from 'vuex'
import { authToken } from '@/api/user'

const router = useRouter()
const route = useRoute()
const store = useStore()
const loading = ref(true)
const res = ref(null)

onMounted(async () => {
    try {
        let token = localStorage.getItem('hztech-token') ? JSON.parse(localStorage.getItem('hztech-token')) : {}
        if (token.content) {
            console.log('有token');
            LogOut()
            // return
        }
        
        if (route.query.c) {
            await autoLogin()
        } else {
            console.log('无code参数');
            LogOut()
            router.push({ path: '/login' })
        }
    } catch (error) {
        console.error('自动登录失败:', error)
        LogOut()
        router.push({ path: '/login' });
    } finally {
        loading.value = false
    }
})

const LogOut = () => {
    store.commit('SET_THEME_NAME', '');
    store.dispatch('LogOut')
}

const autoLogin = async () => {
    try {
        const code = decodeURIComponent(route.query.c)
        if (!code) {
            throw new Error('未获取到授权码')
        } else {
            console.log(code);
        }

        // 调用authToken接口
        localStorage.setItem('authType', 'third')
        const tokenRes = await authToken({ code })
        res.value = tokenRes.data

        // 设置token
        store.commit('SET_TOKEN', res.value.access_token)
        store.commit('SET_REFRESH_TOKEN', res.value.refresh_token)

        // 解析并保存workspaceId
        try {
            const extObj = JSON.parse(res.value.detail.ext)
            const workspaceId = extObj.workspaceId
            localStorage.setItem('workspace_id', workspaceId)
        } catch (error) {
            console.error('解析workspaceId失败:', error)
        }

        // 保存用户信息
        const userInfo = {
            account: res.value.account,
            user_id: res.value.user_id,
            user_name: res.value.user_name,
            real_name: res.value.real_name,
            avatar: res.value.avatar,
            role_name: res.value.role_name,
            role_id: res.value.role_id,
            dept_id: res.value.dept_id,
            nick_name: res.value.nick_name
        }

        store.commit('SET_USER_INFO', userInfo)

        router.push('/screen')
    } catch (error) {
        console.error('登录失败:', error)
        throw error
    }
}
</script>

<style lang="scss" scoped>
.auth-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
    width: 100%;
}

.loading {
    text-align: center;
}
</style>