<template>
    <div class="alarm-container">
        <div class="alarm-header">
            <div class="title">实时报警信息</div>
            <div class="filter-options">
                <div class="status-filters">
                    <label class="filter-item" v-for="option in statusOptions" :key="option.value">
                        <input type="radio" v-model="handleStatus" :value="option.value" name="status" />
                        <span class="radio-circle"></span>
                        <span class="label-text">{{ option.label }}</span>
                    </label>
                </div>
                <div class="safety-filter">
                    <el-select style="width:150px" v-model="algoId" placeholder="请选择算法类型">
                        <el-option v-for="item in algorithmList" :key="item.algoId" :value="item.algoId"
                            :label="item.algoName"></el-option>
                    </el-select>
                </div>
            </div>
        </div>

        <div class="alarm-list" ref="alarmListRef">
            <div class="alarm-content">
                <div v-for="(item, index) in alarmList" :key="index" class="alarm-item">
                    <div class="left-section">
                        <div class="building-info">{{ item.algoName }}</div>
                        <div class="status-device-row">
                            <div class="status-tag" :class="statusMap[item.handleStatus]?.className || ''">{{
                                statusMap[item.handleStatus]?.label || item.handleStatus }}</div>
                            <div class="device-info">{{ item.deviceName }}</div>
                        </div>
                    </div>
                    <div class="right-section">
                        <div class="alarm-time">{{ item.happenTime }}</div>
                        <div class="action-button" v-if="item.handleStatus == 0 || item.handleStatus == 2">
                            <button @click="viewDetail(item)">查看</button>
                        </div>
                    </div>
                </div>
                <div class="loading-more" v-if="loading">加载中...</div>
                <div class="no-more-data" v-if="noMoreData">没有更多数据</div>
            </div>
        </div>

        <!-- 报警详情弹窗 -->
        <div class="alarm-detail-modal" v-if="showDetailModal">
            <div class="modal-content">
                <div class="modal-header">
                    <div class="device-info">
                        <div class="info-row">
                            <span class="label">算法名称:</span>
                            <span class="value">{{ currentAlarm.algoName }}</span>
                            <span class="label">设备名称:</span>
                            <span class="value">{{ currentAlarm.deviceName }}</span>
                        </div>
                        <div class="info-row">
                            <span class="label">设备ID:</span>
                            <span class="value">{{ currentAlarm.cid }}</span>
                            <span class="label">报警状态:</span>
                            <span class="value">{{ statusMap[currentAlarm.handleStatus]?.label ||
                                currentAlarm.handleStatus }}</span>
                        </div>
                        <div class="info-row">
                            <span class="label">发生时间:</span>
                            <span class="value">{{ currentAlarm.happenTime }}</span>
                        </div>
                    </div>
                    <div class="close-btn" @click="closeDetailModal">×</div>
                </div>
                <div class="modal-body">
                    <div class="image-section">
                        <div class="image-container">
                            <div class="image-title">算法分析前图片</div>
                            <div class="image-content">
                                <el-image :src="currentAlarm.srcpicUrl || ''" fit="contain" preview-teleported="true"
                                    :preview-src-list="[currentAlarm.srcpicUrl, currentAlarm.picUrl]" alt="分析前图片" />
                            </div>
                        </div>
                        <div class="image-container">
                            <div class="image-title">算法分析后图片</div>
                            <div class="image-content">
                                <el-image :src="currentAlarm.picUrl || ''" fit="contain" preview-teleported="true"
                                    :preview-src-list="[currentAlarm.picUrl, currentAlarm.srcpicUrl]" alt="分析后图片" />
                            </div>
                        </div>
                    </div>
                    <div v-if="currentAlarm.handleStatus != 2" class="process-section">
                        <div class="section-title">处理</div>
                        <div class="process-form">
                            <div class="form-item">
                                <span class="required">*</span>
                                <span class="label">确认结果</span>
                                <div class="radio-group">
                                    <label>
                                        <input type="radio" v-model="processForm.eventType" value="2" />
                                        <span>误报</span>
                                    </label>
                                    <label>
                                        <input type="radio" v-model="processForm.eventType" value="1" />
                                        <span>报警</span>
                                    </label>
                                </div>
                            </div>
                            <div v-if="processForm.eventType == '1'" class="form-item"
                                :class="{ 'has-error': formErrors.handlePeopleId }">
                                <span class="required">*</span>
                                <span class="label">处理人</span>
                                <div class="input-wrapper">
                                    <el-select v-model="processForm.handlePeopleId" placeholder="请选择处理人">
                                        <el-option v-for="user in userList" :key="user.id" :value="user.realName"
                                            :label="user.realName"></el-option>
                                    </el-select>
                                    <div class="error-message" v-if="formErrors.handlePeopleId">{{
                                        formErrors.handlePeopleId }}</div>
                                </div>
                            </div>
                            <div v-if="processForm.eventType == '1'" class="form-item"
                                :class="{ 'has-error': formErrors.handleCommand }">
                                <span class="required">*</span>
                                <span class="label">下发命令</span>
                                <div class="input-wrapper">
                                    <textarea v-model="processForm.handleCommand" placeholder="请输入下发命令"
                                        maxlength="120"></textarea>
                                    <div class="char-count">{{ processForm.handleCommand.length }}/120</div>
                                    <div class="error-message" v-if="formErrors.handleCommand">{{
                                        formErrors.handleCommand }}</div>
                                </div>
                            </div>
                            <div v-if="processForm.eventType == '1'" class="form-item"
                                :class="{ 'has-error': formErrors.handleRemark }">
                                <span class="required">*</span>
                                <span class="label">处理意见</span>
                                <div class="input-wrapper">
                                    <textarea v-model="processForm.handleRemark" placeholder="请输入处理意见"
                                        maxlength="120"></textarea>
                                    <div class="char-count">{{ processForm.handleRemark.length }}/120</div>
                                    <div class="error-message" v-if="formErrors.handleRemark">{{ formErrors.handleRemark
                                        }}</div>
                                </div>
                            </div>
                            <div class="form-actions">
                                <button class="submit-btn" @click="submitProcess">提交处理</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { getList, getAlgorithmList, createWork, getUserList } from '@/api/workSite/index.js';
import { update } from "@/api/alarmManage/index";
import { ref, reactive, watch, onMounted, onUnmounted, computed } from 'vue'
// 筛选状态
const handleStatus = ref('0')
const algoId = ref('')
const props = defineProps({
    cockpit_dock: {
        type: Object,
        default: () => ({})
    }
})

// 状态选项和映射
const statusOptions = [
    { value: '0', label: '未处理', className: 'status-unhandled' },
    { value: '1', label: '处理中', className: 'status-processing' },
    { value: '2', label: '已处理', className: 'status-handled' }
]

// 使用计算属性生成状态映射对象
const statusMap = computed(() => {
    const map = {}
    statusOptions.forEach(option => {
        map[option.value] = { label: option.label, className: option.className }
    })
    return map
})

// 报警数据列表
const alarmList = reactive([])
const loading = ref(false)
const noMoreData = ref(false)
const page = ref(1)
const pageSize = ref(10)
const algorithmList = ref([])
const userList = ref([]) // 用户列表
// 轮询定时器
let pollingTimer = null

// 弹窗相关状态
const alarmListRef = ref(null)
const showDetailModal = ref(false)
const currentAlarm = ref({})
const processForm = reactive({
    eventType: '1',
    handlePeopleId: '',
    handleCommand: '',
    handleRemark: ''
})
const formErrors = reactive({
    handlePeopleId: '',
    handleCommand: '',
    handleRemark: ''
})

// 监听滚动到底部加载更多
const handleScroll = () => {
    if (loading.value || noMoreData.value) return

    const scrollContainer = alarmListRef.value
    if (!scrollContainer) return

    const scrollHeight = scrollContainer.scrollHeight
    const scrollTop = scrollContainer.scrollTop
    const clientHeight = scrollContainer.clientHeight

    // 距离底部20px时加载更多
    if (scrollHeight - scrollTop - clientHeight < 20) {
        loadMoreData()
    }
}

// 刷新列表数据
const refreshList = async () => {
    try {
        page.value = 1
        noMoreData.value = false
        await fetchAlarmList(true)
    } catch (error) {
        console.error('刷新列表失败', error)
    }
}

// 加载更多数据
const loadMoreData = async () => {
    if (loading.value || noMoreData.value) return
    page.value += 1
    await fetchAlarmList()
}

// 获取报警列表数据
const fetchAlarmList = async (isRefresh = false) => {
    loading.value = true
    try {
        // 构建参数
        const params = {
            handleStatus: handleStatus.value,
            algoId: algoId.value,
            cid: props.cockpit_dock.sn
        }

        // 使用FlightRecord接口获取数据
        const response = await getList(page.value, pageSize.value, params)

        // 处理数据
        const { records, total } = response.data.data

        if (isRefresh) {
            // 刷新操作，替换所有数据
            alarmList.splice(0, alarmList.length, ...records)
        } else {
            // 加载更多，追加数据
            alarmList.push(...records)
        }

        // 判断是否还有更多数据
        noMoreData.value = alarmList.length >= total

    } catch (error) {
        console.error('获取报警列表失败', error)
    } finally {
        loading.value = false
    }
}

// 监听筛选条件变化
watch([handleStatus, algoId], () => {
    refreshList()
}, { immediate: false })

// 查看详情
const viewDetail = (item) => {
    currentAlarm.value = item

    // 重置处理表单
    processForm.eventType = '1'
    processForm.handlePeopleId = ''
    processForm.handleCommand = ''
    processForm.handleRemark = ''

    // 显示弹窗
    showDetailModal.value = true
}

// 关闭详情弹窗
const closeDetailModal = () => {
    showDetailModal.value = false
    // 清除所有错误提示
    Object.keys(formErrors).forEach(key => {
        formErrors[key] = ''
    })
}

// 检查表单字段
const validateField = (field, message) => {
    if (!processForm[field]) {
        formErrors[field] = message
        return false
    }
    formErrors[field] = ''
    return true
}

// 提交处理
const submitProcess = async () => {
    if (processForm.eventType == '2') {
        currentAlarm.value.handleStatus = 2;
        update(currentAlarm.value).then(() => {
            closeDetailModal()
            refreshList()
        })
        return;
    }
    // 重置错误信息
    Object.keys(formErrors).forEach(key => {
        formErrors[key] = ''
    })

    // 表单验证
    const isHandlerValid = validateField('handlePeopleId', '请选择处理人')
    const isCommandValid = validateField('handleCommand', '请输入下发命令')
    const isOpinionValid = validateField('handleRemark', '请输入处理意见')

    if (!isHandlerValid || !isCommandValid || !isOpinionValid) {
        return
    }
    processForm.eventId = currentAlarm.value.id
    processForm.eventName = currentAlarm.value.algoName
    try {
        // 这里添加处理提交的API调用
        createWork(processForm).then(res => {
            closeDetailModal()
            refreshList()
        })
    } catch (error) {
        console.error('处理报警失败', error)
    }
}

// 组件卸载时移除监听
onUnmounted(() => {
    if (alarmListRef.value) {
        alarmListRef.value.removeEventListener('scroll', handleScroll)
    }
    // 清除轮询定时器
    if (pollingTimer) {
        clearInterval(pollingTimer)
    }
})

const pollingUpdate = async () => {
    try {
        const params = {
            handleStatus: handleStatus.value,
            algoId: algoId.value,
            cid: props.cockpit_dock.sn
        }

        const response = await getList(1, pageSize.value, params)

        const { records } = response.data.data

        if (records && records.length > 0) {
            const existingAlarmsMap = new Map()
            alarmList.forEach((item, index) => {
                existingAlarmsMap.set(item.id, { item, index })
            })

            const newItems = []

            for (const record of records) {
                const existingRecord = existingAlarmsMap.get(record.id)

                if (!existingRecord) {
                    newItems.push(record)
                } else {
                    alarmList[existingRecord.index] = record
                }
            }

            if (newItems.length > 0) {
                alarmList.unshift(...newItems)
            }
        }
    } catch (error) {
        console.error('轮询更新失败', error)
    }
}

const startPolling = () => {
    if (pollingTimer) {
        clearInterval(pollingTimer)
    }

    pollingTimer = setInterval(() => {
        pollingUpdate()
    }, 30000)
}

// 组件挂载时获取数据
onMounted(async () => {
    const res = await getAlgorithmList(1, 100, { algoType: 5 })
    algorithmList.value = res.data.data.records
    algoId.value = algorithmList.value[0].algoId
    await fetchAlarmList()

    // 获取用户列表
    const userRes = await getUserList()
    userList.value = userRes.data.data.records

    // 添加滚动监听
    if (alarmListRef.value) {
        alarmListRef.value.addEventListener('scroll', handleScroll)
    }

    // 启动30秒轮询
    startPolling()
})
</script>

<style lang="scss" scoped>
.alarm-container {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;

    .alarm-header {
        padding: 10px;
        background-color: #f0f2f5;

        .title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 10px;
            text-align: right;
        }

        .filter-options {
            display: flex;
            justify-content: space-between;
            align-items: center;

            .status-filters {
                display: flex;
                align-items: center;

                .filter-item {
                    display: flex;
                    align-items: center;
                    margin-right: 15px;
                    cursor: pointer;

                    input[type="radio"] {
                        display: none;
                    }

                    .radio-circle {
                        width: 16px;
                        height: 16px;
                        border-radius: 50%;
                        border: 1px solid #ccc;
                        margin-right: 5px;
                        position: relative;

                        &:before {
                            content: "";
                            width: 10px;
                            height: 10px;
                            background-color: #1890ff;
                            border-radius: 50%;
                            position: absolute;
                            top: 50%;
                            left: 50%;
                            transform: translate(-50%, -50%);
                            display: none;
                        }
                    }

                    input[type="radio"]:checked+.radio-circle:before {
                        display: block;
                    }
                }
            }

            .safety-filter {
                select {
                    padding: 5px 10px;
                    border: 1px solid #ccc;
                    border-radius: 4px;
                    outline: none;
                }
            }
        }
    }

    .alarm-list {
        flex: 1;
        overflow-y: auto;
        overflow-x: hidden;
        position: relative;

        .alarm-content {
            .alarm-item {
                display: flex;
                padding: 10px 15px;
                border-bottom: 1px solid #ffcc00;
                align-items: flex-start;
                background-color: #0052a3;
                color: white;
                justify-content: space-between;

                .left-section {
                    display: flex;
                    flex-direction: column;
                    width: 60%;

                    .building-info {
                        font-weight: bold;
                        margin-bottom: 5px;
                    }

                    .status-device-row {
                        display: flex;
                        align-items: center;

                        .status-tag {
                            display: inline-block;
                            padding: 2px 8px;
                            color: white;
                            border-radius: 2px;
                            font-size: 12px;
                            width: fit-content;
                            margin-right: 10px;
                            background-color: #f5222d;
                            /* 默认颜色 */

                            &.status-unhandled {
                                background-color: #f5222d;
                            }

                            &.status-processing {
                                background-color: #faad14;
                            }

                            &.status-handled {
                                background-color: #52c41a;
                            }
                        }

                        .device-info {
                            color: white;
                        }
                    }
                }

                .right-section {
                    display: flex;
                    flex-direction: column;
                    align-items: flex-end;
                    width: 35%;

                    .alarm-time {
                        margin-bottom: 5px;
                    }

                    .action-button {
                        button {
                            padding: 4px 15px;
                            background-color: #1890ff;
                            color: white;
                            border: none;
                            border-radius: 4px;
                            cursor: pointer;

                            &:hover {
                                background-color: #40a9ff;
                            }
                        }
                    }
                }
            }

            .loading-more,
            .no-more-data {
                text-align: center;
                padding: 10px;
                color: #666;
            }
        }
    }

    /* 弹窗样式 */
    .alarm-detail-modal {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 1000;

        .modal-content {
            width: 90%;
            max-width: 800px;
            background-color: #001529;
            border-radius: 5px;
            overflow: hidden;
            color: white;
            max-height: 90vh;
            display: flex;
            flex-direction: column;

            .modal-header {
                padding: 15px;
                background-color: #001a33;
                position: relative;

                .device-info {
                    .info-row {
                        display: flex;
                        margin-bottom: 8px;
                        flex-wrap: wrap;

                        .label {
                            margin-right: 5px;
                            color: #a0a0a0;
                            min-width: 70px;
                        }

                        .value {
                            margin-right: 15px;
                            flex: 1;
                        }
                    }
                }

                .close-btn {
                    position: absolute;
                    top: 10px;
                    right: 15px;
                    font-size: 24px;
                    cursor: pointer;
                    color: #a0a0a0;

                    &:hover {
                        color: white;
                    }
                }
            }

            .modal-body {
                display: flex;
                padding: 15px;
                max-height: calc(90vh - 120px);
                overflow: auto;

                .image-section {
                    display: flex;
                    flex-direction: column;
                    width: 60%;
                    margin-right: 15px;
                    flex: 1;

                    &:only-child {
                        width: 100%;
                        margin-right: 0;
                    }

                    .image-container {
                        margin-bottom: 15px;
                        background-color: #172a45;
                        border-radius: 5px;
                        overflow: hidden;

                        .image-title {
                            padding: 10px;
                            background-color: #0d1b2a;
                            font-weight: bold;
                        }

                        .image-content {
                            padding: 10px;
                            display: flex;
                            justify-content: center;
                            align-items: center;
                            height: 200px;

                            :deep(.el-image) {
                                width: 100%;
                                height: 100%;
                                display: flex;
                                justify-content: center;
                                align-items: center;
                            }

                            :deep(.el-image__inner) {
                                max-width: 100%;
                                max-height: 100%;
                                object-fit: contain;
                            }
                        }
                    }
                }

                .process-section {
                    width: 40%;
                    background-color: #172a45;
                    border-radius: 5px;
                    padding: 15px;

                    .section-title {
                        font-weight: bold;
                        margin-bottom: 15px;
                    }

                    .process-form {
                        .form-item {
                            margin-bottom: 15px;
                            display: flex;
                            align-items: flex-start;

                            &.has-error {

                                input,
                                textarea {
                                    border-color: #f5222d;
                                }
                            }

                            .required {
                                color: #f5222d;
                                margin-right: 3px;
                            }

                            .label {
                                width: 80px;
                                margin-top: 3px;
                            }

                            .radio-group {
                                flex: 1;
                                display: flex;

                                label {
                                    margin-right: 15px;
                                    display: flex;
                                    align-items: center;

                                    input {
                                        margin-right: 5px;
                                    }
                                }
                            }

                            .input-wrapper {
                                flex: 1;
                                position: relative;

                                input,
                                textarea {
                                    width: 100%;
                                    padding: 8px;
                                    background-color: #0d1b2a;
                                    border: 1px solid #364d79;
                                    border-radius: 4px;
                                    color: white;
                                    font-size: 14px;
                                    outline: none;

                                    &:focus {
                                        border-color: #1890ff;
                                    }
                                }

                                textarea {
                                    min-height: 80px;
                                    resize: vertical;
                                }

                                .char-count {
                                    position: absolute;
                                    bottom: 5px;
                                    right: 10px;
                                    font-size: 12px;
                                    color: #a0a0a0;
                                }

                                .error-message {
                                    color: #f5222d;
                                    font-size: 12px;
                                    margin-top: 3px;
                                }
                            }
                        }

                        .form-actions {
                            display: flex;
                            justify-content: center;
                            margin-top: 20px;

                            .submit-btn {
                                padding: 8px 30px;
                                background-color: #1890ff;
                                color: white;
                                border: none;
                                border-radius: 4px;
                                cursor: pointer;
                                font-weight: bold;

                                &:hover {
                                    background-color: #40a9ff;
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
</style>