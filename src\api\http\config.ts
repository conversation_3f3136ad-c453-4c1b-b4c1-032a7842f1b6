export const CURRENT_CONFIG = {

  // license
  appId: '147912', // You need to go to the development website to apply.
  appKey: '389ef8955b463b600648381c518b375', // You need to go to the development website to apply.
  appLicense: 'SRtUNy5oW1U1h0MFZbkr+N1m+MRhPMmzDxyOlq6MZBpDDbEyZZFAwOfsmAuTMBxI4+W8s2b4XZ+dGkd+8znIIDoKLMyuhQYRxP2IHnlcrfOqIaTaNLK+V9fAqGx4/4alqTeW6oIjemhX6u+gevG2bdtLVyzB+eJSwwmGBvqKV3M=', // You need to go to the development website to apply.

  // http
  baseURL: 'http://************:8200/', // This url must end with "/". Example: 'http://***********:6789/'
  websocketURL: 'ws://************:8200/api/v1/ws', // Example: 'ws://***********:6789/api/v1/ws'

  // livestreaming 直播功能
  // RTMP  Note: This IP is the address of the streaming server. If you want to see livestream on web page, you need to convert the RTMP stream to WebRTC stream.
  rtmpURL: 'Please enter the rtmp access address.', // Example: 'rtmp://***********/live/'
  // GB28181 Note:If you don't know what these parameters mean, you can go to Pilot2 and select the GB28181 page in the cloud platform. Where the parameters same as these parameters.
  gbServerIp: 'Please enter the server ip.',
  gbServerPort: 'Please enter the server port.',
  gbServerId: 'Please enter the server id.',
  gbAgentId: 'Please enter the agent id',
  gbPassword: 'Please enter the agent password',
  gbAgentPort: 'Please enter the local port.',
  gbAgentChannel: 'Please enter the channel.',
  // RTSP
  rtspUserName: 'Please enter the username.',
  rtspPassword: 'Please enter the password.',
  rtspPort: '8554',
  // Agora
  agoraAPPID: 'Please enter the agora app id.',
  agoraToken: 'Please enter the agora temporary token.',
  agoraChannel: 'Please enter the agora chan51000nel.',

  // map
  // You can apply on the AMap website.
  amapKey: 'dff806562692e8df22b09e11c178ee6d',

}
