<template>
	<div class="topBar">
		<div class="msgSection">
			<!-- <div class="left">
				<div class="leftItem">
					<span >0</span>
				</div>
				<div class="leftItem">
					<span>0</span>
				</div>
			</div> -->
			<div class="textContent">
				<!-- <span class="noItem">暂无消息</span> -->
				<div v-if="hmsInfo[cockpitDock.sn] && EHmsLevel" class="flex-align-center flex-row">
					<div :class="hmsInfo[cockpitDock.sn][0].level === EHmsLevel.CAUTION
						? 'caution-blink'
						: hmsInfo[cockpitDock.sn][0].level === EHmsLevel.WARN
							? 'warn-blink'
							: 'notice-blink'
						" style="width: 22px; height:22px; text-align: center">
						<span :style="hmsInfo[cockpitDock.sn].length > 99
							? 'font-size: 11px'
							: 'font-size: 12px'
							">{{ hmsInfo[cockpitDock.sn].length }}</span>
						<span class="fz10">{{
							hmsInfo[cockpitDock.sn].length > 99 ? '+' : ''
						}}</span>
					</div>
					<a-popover trigger="click" placement="bottom" color="black" v-model:visible="visiable"
						@visibleChange="readHms(visiable, cockpitDock.sn)"
						:overlayStyle="{ width: '200px', height: '300px', bottom: '10px' }">
						<div :class="hmsInfo[cockpitDock.sn][0].level === EHmsLevel.CAUTION
							? 'caution'
							: hmsInfo[cockpitDock.sn][0].level === EHmsLevel.WARN
								? 'warn'
								: 'notice'
							" style="margin-left: 3px;">
							<span class="word-loop">{{ hmsInfo[cockpitDock.sn][0].message_zh }}</span>
						</div>
						<template #content>
							<a-collapse style="background: black; height: 300px; overflow-y: auto" :bordered="false"
								expand-icon-position="right" :accordion="true">
								<a-collapse-panel v-for="hms in hmsInfo[cockpitDock.sn]" :key="hms.hms_id"
									:showArrow="false" style="
					          margin: 0 auto 3px auto;
					          border: 0;
					          width: 140px;
					          border-radius: 3px;
					        " :class="hms.level === EHmsLevel.CAUTION
								? 'caution'
								: hms.level === EHmsLevel.WARN
									? 'warn'
									: 'notice'
								">
									<template #header>
										<div class="flex-row flex-align-center" style="width: 130px">
											<div style="width: 110px">
												<span class="word-loop">{{ hms.message_zh }}</span>
											</div>
											<div style="
					                width: 20px;
					                height: 15px;
					                font-size: 10px;
					                z-index: 2;
					              " class="flex-row flex-align-center flex-justify-center" :class="hms.level === EHmsLevel.CAUTION
									? 'caution'
									: hms.level === EHmsLevel.WARN
										? 'warn'
										: 'notice'
									">
												<DoubleRightOutlined />
											</div>
										</div>
									</template>

									<a-tooltip :title="hms.create_time">
										<div style="color: white" class="text-hidden">
											{{ hms.create_time }}
										</div>
									</a-tooltip>
								</a-collapse-panel>
							</a-collapse>
						</template>
					</a-popover>
				</div>
			</div>
			<div class="textContent">{{ droneControlSource }}</div>
		</div>
		<div class="titleSection" style="color: rgb(29, 203, 99);">{{ operationName }}</div>
		<div class="infoSection" v-if="deviceInfo.device">
			<div class="gps" v-if="!deviceInfo.device || deviceInfo.device?.mode_code === EModeCode.未连接">
				<a-tooltip>
					<span class="gps-title">
						<SignalFilled :style="{ color: '#ff0000' }" />
					</span>
					<div class="gps-value">
						<span class="gps-value-text">暂无信号</span>
					</div>
				</a-tooltip>
			</div>
			<!--      下线退出-->
			<template v-else>
				<div class="gps">
					<a-tooltip>
						<span class="gps-title">
							<SignalFilled
								:style="{ color: ['非常差', '弱'].includes(qualityName) ? '#ff0000' : '#1dcb63' }" />
						</span>
						<div class="gps-value">
							<span class="gps-value-text">{{ qualityName }}</span>
						</div>
					</a-tooltip>
				</div>
				<div class="gps">
					<a-tooltip title="电量">
						<span class="gps-title" :class="{ 'batterying': isCharge }">
							<ThunderboltOutlined style="color:rgb(25, 190, 107)" class="fz14" />
						</span>
						<div class="gps-value">
							<span class="gps-value-text" :class="{ 'batterying': isCharge }">{{ deviceInfo.device ?
								deviceInfo.device.battery.capacity_percent + '%' : '--' }}</span>
						</div>
					</a-tooltip>
				</div>
				<div class="gps">
					<a-tooltip title="GPS">
						<span class="gps-title">GPS</span>
						<div class="gps-value">
							<span class="gps-value-text">{{ deviceInfo.device ?
								deviceInfo.device.position_state.gps_number : '--' }}</span>
						</div>
					</a-tooltip>
				</div>
				<div class="gps">
					<a-tooltip :title="deviceTitle.Altitude">
						<span class="gps-title">ASL</span>
						<div class="gps-value">
							<span class="gps-value-text">{{ fixedNum('height') + ' m' }}</span>
						</div>
					</a-tooltip>
				</div>
				<div class="gps">
					<a-tooltip :title="deviceTitle.takeoff">
						<span class="gps-title">ALT</span>
						<div class="gps-value">
							<span class="gps-value-text">{{ fixedNum('elevation') + ' m' }}</span>
						</div>
					</a-tooltip>
				</div>
				<div class="gps">
					<a-tooltip :title="deviceTitle.horizontal_speed">
						<span class="gps-title">H.S</span>
						<div class="gps-value">
							<span class="gps-value-text">{{ fixedNum('horizontal_speed') + ' m/s' }}</span>
						</div>
					</a-tooltip>
				</div>
				<div class="gps">
					<a-tooltip :title="deviceTitle.vertical_speed">
						<span class="gps-title">V.S</span>
						<div class="gps-value">
							<span class="gps-value-text">{{ fixedNum('vertical_speed') + ' m/s' }}</span>
						</div>
					</a-tooltip>
				</div>
				<div class="gps">
					<a-tooltip :title="deviceTitle.wind_speed">
						<span class="gps-title">W.S</span>
						<div class="gps-value">
							<span class="gps-value-text">{{ fixedNum('wind_speed', 10) + ' m/s' }}</span>
						</div>
					</a-tooltip>
				</div>
			</template>
			<div class="close" @click="closeCockpit">
				<span class="closeText">退出驾驶舱</span>
				<ExportOutlined />
			</div>
		</div>
		<!-- 无信号退出驾驶舱按钮 -->
		<div v-else class="infoSection">
			<div class="close" @click="closeCockpit">
				<span class="closeText">退出驾驶舱</span>
				<ExportOutlined />
			</div>
		</div>
		<div class="topBarBatteryLine left">
			<div class="battery-slide" v-if="deviceInfo.device && deviceInfo.device.battery.remain_flight_time !== 0"
				style="border: 1px solid red">
				<div style="background: #535759;" class="width-100"></div>
				<div class="capacity-percent" :style="{ width: deviceInfo.device.battery.capacity_percent + '%' }">
				</div>
				<div class="return-home" :style="{ width: deviceInfo.device.battery.return_home_power + '%' }"></div>
				<div class="landing" :style="{ width: deviceInfo.device.battery.landing_power + '%' }"></div>
				<div class="white-point" :style="{ left: deviceInfo.device.battery.landing_power + '%' }"></div>
				<div class="battery" :style="{ left: deviceInfo.device.battery.capacity_percent + '%' }">
					{{ Math.floor(deviceInfo.device.battery.remain_flight_time / 60) }}:
					{{ 10 > (deviceInfo.device.battery.remain_flight_time % 60) ? '0' :
						'' }}{{ deviceInfo.device.battery.remain_flight_time % 60 }}
				</div>
			</div>
		</div>
		<!-- <div class="topBarBatteryLine right" style="--value: 100%; --percentColor: rgb(27, 209, 133);"></div> -->
	</div>
</template>

<script lang="ts">
import { ExportOutlined, DoubleRightOutlined, SignalFilled, ThunderboltOutlined } from '@ant-design/icons-vue';
import store from '@/store'
import { EHmsLevel } from '@/types/enums';
import { updateDeviceHms } from '@/api/manage/index';
import { ELocalStorageKey, EDeviceTypeName } from '@/types';
import { cockpitTsaUpdate } from '@/hooks/use-g-map-cockpit';
import { deviceTitle } from '@/api/enum/index'
import { EModeCode } from '@/types/device';
import EventBus from '@/event-bus';
import getEnum from './enum.ts';
import { getWorkspaceId } from '@/utils/storage'

export default {
	props: ['deviceState', 'cockpitDock'],
	components: {
		ExportOutlined,
		DoubleRightOutlined,
		SignalFilled,
		ThunderboltOutlined
	},
	data() {
		return {
			hmsInfo: store.state.dock.hmsInfo,
			EHmsLevel: EHmsLevel,
			EDeviceTypeName: EDeviceTypeName,
			workspaceId: getWorkspaceId(),
			osdVisible: store.state.dock.osdVisible,
			cockpitTsaUpdateHook: cockpitTsaUpdate(),
			deviceTitle: deviceTitle,
			EModeCode: EModeCode,
			visiable: false,
			operationName: '非手动控制模式',
			deviceInfo: null,
			/**信号强度 */
			qualityName: null,
			/**是否充电 */
			isCharge: false,
			// worker
			worker: null,
			droneControlSource: '',
			// 消息队列
			messageQueue: [],
			// 是否正在显示消息
			isShowingMessage: false,
		};
	},
	created(){
		//@ts-ignore
		// this.worker = new Worker(new URL('./worker/cockpit_worker.js', import.meta.url));
	},
	mounted() {
		// console.log(this.cockpitHmsVisible,this.cockpitDock)
		EventBus.on('cockpit_control', (data) => {
			this.operationName = data;
		})
		EventBus.on('droneControlSource', (data) => {
			// 将新消息添加到队列
			this.messageQueue.push(data);
			// 如果当前没有正在显示的消息，开始显示
			if (!this.isShowingMessage) {
				this.showNextMessage();
			}
		})
	},
	computed: {
		// deviceState() {
		//   return this.$store.state.dock.deviceState;
		// },
		//   deviceInfo(){
		//     return this.deviceState.deviceInfo
		//   },
	},
	watch: {
		deviceState: {
			handler(data) {
				this.deviceInfo = data.deviceInfo
				// 处理新数据
				if (data.currentType === this.EDeviceTypeName.Aircraft && data.deviceInfo[data.currentSn]) {
					// const { longitude, latitude } = data.deviceInfo[data.currentSn]
					// this.worker.postMessage({ longitude: longitude || 0, latitude: latitude || 0 })
					// this.worker.onmessage = ({ data: { coordinate } }) => {
					// 	this.cockpitTsaUpdateHook.moveTo(data.currentSn, coordinate[0], coordinate[1])
					// };
					if (this.osdVisible.sn !== '') {
						this.deviceInfo.device = data.deviceInfo[this.osdVisible.sn]
					}
				}
				this.dockInfoChange()
			},
			deep: true, // 深度监听
			immediate: true,
		}
	},
	methods: {
		showNextMessage() {
			// 如果队列为空，标记为没有正在显示的消息并返回
			if (this.messageQueue.length === 0) {
				this.isShowingMessage = false;
				this.droneControlSource = '';
				return;
			}
			
			// 设置正在显示消息的标志
			this.isShowingMessage = true;
			
			// 从队列中取出第一条消息并显示
			this.droneControlSource = this.messageQueue.shift();
			
			// 设置定时器，2秒后显示下一条消息
			setTimeout(() => {
				this.showNextMessage();
			}, 2000);
		},
		/**
		 * 信号强度 0-5,
		 */
		dockInfoChange() {
			// const { deviceState, gateway } = store.state.dock;
			const { dockInfo, currentSn, } = this.deviceState
			const val = dockInfo[currentSn]?.link_osd?.wireless_link?.sdr_quality;
			// 是否充电
			const drone_charge_state = dockInfo[currentSn]?.basic_osd?.drone_charge_state.state
			this.isCharge = drone_charge_state ? true : false
			if (!val) {
				return
			}
			this.qualityName = getEnum('qualityEnum', val);
		},
		readHms(visiable, sn) {
			if (!visiable) {
				updateDeviceHms(this.workspaceId, sn).then(res => {
					if (res.code === 0) {
						delete this.hmsInfo[sn]
					}
				})
			}
		},
		fixedNum(field, per?: number) {
			let val = this.deviceInfo.device[field];
			if (!val) {
				return 0.0;
			}
			val = Number(val)
			val = per ? (val / per) : val;
			return val.toFixed(2);
		},
		//退出驾驶舱
		closeCockpit() {
			this.$emit('closeCockpit', false);
		}
	},
	beforeDestroy() {
		// this.worker.terminate()
		EventBus.off('cockpit_control')
	},
}
</script>

<style lang="scss" scoped>
.topBar {
	display: flex;
	justify-content: space-between;

	width: 100%;
	height: 3em;
	position: absolute;
	top: 0;
	left: 0;
	background-color: rgba(0, 0, 0, .6);
	align-items: center;
	padding: 0 1em;
	box-shadow: 0 3px 5px rgba(0, 0, 0, .4);

	.msgSection {
		width: 47%;
		height: 2.4em;
		position: relative;
		min-width: 280px;
		display: flex;
		justify-content: space-between;

		.left {
			width: 1.3em;
			height: 100%;
			display: flex;
			flex: none;
			flex-direction: column;
			border-radius: .2em;
			overflow: hidden;
		}

		.leftItem {
			flex: 1 1 0;
			width: 100%;
			display: flex;
			justify-content: center;
			;
			align-items: center;
			font-size: .5em;
			line-height: 1;
			color: #fff;

			span {
				width: 100%;
				height: 100%;
				display: flex;
				justify-content: center;
				align-items: center;
				transform: scale(.9);
			}
		}

		.leftItem:first-child {
			background-color: #f01d1d;
		}

		.textContent {
			border-radius: .2em;
			height: 100%;
			width: calc(100% - 2em);
			padding: 0 .5em;
			display: flex;
			-webkit-box-align: center;
			align-items: center;
			cursor: pointer;
			/* background-color: #f01d1d;
			span {
				font-size: .8em;
				white-space: nowrap;
				color: #fff;
				overflow: hidden;
				text-overflow: ellipsis;
			} */
		}

		.noItem {
			width: 100%;
			text-align: center;
		}
	}

	.titleSection {
		position: absolute;
		left: 50%;
		top: 50%;
		transform: translate(-50%, -50%);
		font-weight: 700;
	}

	.infoSection {
		width: 35%;
		min-width: 300px;
		height: 2em;
		display: flex;
		justify-content: space-between;
		align-items: center;
		color: #fff;

		/* .signal {
			display: flex;
			align-items: center;
			font-size: .8em;
			margin-top: .2em;
			img {
			    width: 1.2em;
			    height: 1em;
			    margin-right: .3em;
			}
		} */
		.gps {
			display: flex;
			flex-direction: column;
			align-items: flex-start;
			// justify-content: center;
			text-align: center;
			font-size: .8em;
		}

		.gps-title {
			transform-origin: 0 50%;
		}

		.gps-value {
			transform-origin: 0 0;
			display: -webkit-box;
			display: -ms-flexbox;
			display: flex;
			align-items: center;
		}

		.iconfont {
			margin-right: .2em;
			font-family: iconfont !important;
			font-size: 16px;
			font-style: normal;
			-webkit-font-smoothing: antialiased;
		}

		/* .info {
			font-size: .8em;
			display: flex;
			-webkit-box-orient: vertical;
			-webkit-box-direction: normal;
			flex-direction: column;
			-webkit-box-align: start;
			align-items: flex-start;
			-webkit-box-pack: center;
			justify-content: center;
		} */
		.close {
			cursor: pointer;
			background-color: #ff4949;
			display: -webkit-box;
			display: flex;
			align-items: center;
			padding: .2em .5em;
			border-radius: .2em;
			transition: all .3s;
			border: 1px solid transparent;

			.closeText {
				font-size: .8em;
				line-height: 1;
				margin-right: .5em;
				margin-bottom: 1px;
			}
		}
	}

	.topBarBatteryLine {
		width: 100%;
		position: absolute;
		height: 3px;
		bottom: -2px;
		z-index: 3;

		// background-color: #505050;
		.left {
			left: 0;
		}

		.right {
			right: 0;
		}
	}
}

.notice-blink {
	background: #313130;
	animation: blink 500ms infinite;
}

.caution-blink {
	background: orange;
	animation: blink 500ms infinite;
}

.warn-blink {
	background: red;
	animation: blink 500ms infinite;
}

.notice {
	background: #333333;
	overflow: hidden;
	cursor: pointer;
}

.caution {
	background: orange;
	cursor: pointer;
	overflow: hidden;
}

.warn {
	background: red;
	cursor: pointer;
	overflow: hidden;
}

.word-loop {
	white-space: nowrap;
	display: inline-block;
	animation: 10s loop linear infinite normal;
}

.text-hidden {
	overflow: hidden !important;
	text-overflow: ellipsis !important;
	white-space: nowrap;
	-o-text-overflow: ellipsis;
}

.batterying {
	animation: breathe 2s ease-in-out infinite;
}

@keyframes breathe {

	0%,
	100% {
		// transform: scale(1);
		opacity: 0.8;
	}

	50% {
		// transform: scale(1.2);
		/* 元素放大到原来的1.2倍 */
		opacity: 0.3;
		/* 透明度减小到0.7 */
	}
}
</style>