<template>
  <div class="main">
    <div class="left">
      <div class="search">
        <el-date-picker style="width: 58%;margin-right: 2%;" v-model="searchTime" type="daterange" range-separator="-"
                        start-placeholder="开始时间" end-placeholder="结束时间" @change="changeTime" @clear="clearTime"/>

        <el-select v-model="deviceSn" style="width: 40%;" @change="changeDevice">
          <el-option v-for="(item, index) in options" :key="index" :label="`${item.device_name} (${item.device_sn})`" :value="item.device_sn"/>
        </el-select>
      </div>

      <div class="list" ref="scrollContainer">
        <table class="flight-table">
          <thead>
          <tr>
            <th v-for="(item, index) in headers" :key="index">{{ item }}</th>
          </tr>
          </thead>
          <tbody>
          <tr v-for="(item, index) in flightOptions" :key="index" style="cursor: pointer;" @click="clickDetail(item, index)"
              :class="{ 'odd-row': index % 2 === 0, 'even-row': index % 2 !== 0, 'selected-row': selectedIndex === index }">
            <td>{{ item.job_name }}</td>
            <td>{{ enumerateType[item.task_type] }}</td>
            <td>{{ item.file_name }}</td>
            <td>{{ WaylineTypeMap[item.wayline_type]}}</td>
            <td>{{ enumerateStatus[item.status] }}</td>
            <td>{{ item.begin_time }}<br>{{ item.end_time }}</td>
            <td>详情</td>
          </tr>
          </tbody>
        </table>
      </div>

      <div class="titleBg">
        <span>飞行画面</span>
      </div>
    </div>
   
    <!-- <div v-if="isVideo" class="right">
      <div id="g-container" :style="{ width: '100%', height: '100%' }"/>
      <videoAmp :VideoId="VideoId" :name="VideoName"></videoAmp>
    </div>
    <div v-else class="right">
      <div id="g-container" :style="{ width: '100%', height: '100%' }"/>
      <videoAmp :name="VideoName"></videoAmp>
    </div> -->
    <div class="right">
      <div id="g-container" :style="{ width: '100%', height: '100%' }"/>
      <videoAmp :VideoId="VideoId" :name="VideoName" :isVideo="isVideo"></videoAmp>
    </div>
  </div>
</template>

<script setup>
import moment from 'moment';
import {ref, onMounted, onUnmounted} from 'vue';
import {getCheckFlightMp4} from "@/api/wayline";
import {DockOnlineDevices, FlightRecord} from '@/api/screen';
import videoAmp from './map/recordMap.vue';
import {useGMapManage} from "@/hooks/use-c-map";
import {getApp} from "@/root";
import {WaylineTypeMap} from '@/types/task';

let VideoId = ref(null);
let VideoName = ref(null);
let isVideo = ref(true);
let searchTime = ref(null);
let deviceSn = ref(null);
let selectedIndex = ref(null);
const scrollContainer = ref(null);
const enumerateType = {
  0: '立即执行',
  1: '定时执行',
  2: '连续执行',
};
const enumerateStatus = {
  1: '待执行',
  2: '执行中',
  3: '执行成功',
  4: '执行终止',
  5: '执行失败',
  6: '已暂停',
};
const params = ref({
  startTime: '',
  endTime: '',
  dockSn: '',
  page_size: 10,
  page: 1,
});
const options = ref([]);
const flightOptions = ref([]);
const headers = ref(['飞行名称', '类型', '航线名称', '航线类型', '执行状态', '飞行时间', '操作']);
const loading = ref(false);
const hasMore = ref(true);

const initData = async () => {
  try {
    const res = await DockOnlineDevices();
    const data = res.data.data;
    if (data.length > 0) {
      options.value = data;
      deviceSn.value = data[0].device_sn;
      params.value.dockSn = data[0].device_sn;
      init();
    }
  } catch (error) {
    console.error('初始化数据失败:', error);
  }
}

const init = () => {
  if (loading.value || !hasMore.value) return;
  
  loading.value = true;
  FlightRecord(params.value).then(res => {
    if (res.data.data.list.length > 0) {
      flightOptions.value.push(...res.data.data.list);
      hasMore.value = res.data.data.list.length === params.value.page_size;
    } else {
      hasMore.value = false;
    }
  }).finally(() => {
    loading.value = false;
  });
}

const clickDetail = (item, index) => {
  selectedIndex.value = index
  VideoId.value = ''
  getCheckFlightMp4(item.job_id).then(res => {
    isVideo.value = true
  }).catch(err => {
    isVideo.value = false
  }).finally(() => {
    VideoId.value = item.job_id
    VideoName.value = item.job_name
  })
}

const changeTime = () => {
  if (searchTime.value) {
    let Dates = searchTime.value.map(date => {
      return moment(date).format('YYYY-MM-DD');
    });
    params.value.startTime = Dates[0];
    params.value.endTime = Dates[1];
    params.value.page = 1;
    flightOptions.value = [];
    hasMore.value = true;
    init();
  }
}

const clearTime = () => {
  params.value.startTime = '';
  params.value.endTime = '';
  params.value.page = 1;
  flightOptions.value = [];
  hasMore.value = true;
  init();
}

const handleScroll = () => {
  if (scrollContainer.value.scrollHeight - scrollContainer.value.scrollTop <= scrollContainer.value.clientHeight + 30) {
    if (!loading.value && hasMore.value) {
      params.value.page++;
      init();
    }
  }
}

onMounted(() => {
  scrollContainer.value.addEventListener('scroll', handleScroll);
  initData();
  const app = getApp();
  useGMapManage().globalPropertiesConfig(app);
})

const changeDevice = () => {
  params.value.page = 1;
  flightOptions.value = [];
  hasMore.value = true;
  params.value.dockSn = deviceSn.value;
  init();
}

onUnmounted(() => {
  scrollContainer.value.removeEventListener('scroll', handleScroll);
});
</script>

<style lang="scss" scoped>
.main {
  width: 100%;
  height: 100%;
  position: relative;

  .left {
    position: absolute;
    left: 1.3vw;
    top: 0;
    width: 35.8vw;
    height: 100%;
  }

  .right {
    position: absolute;
    top: 0;
    right: 0;
    width: 62.6vw;
    height: 100%;
  }
}

.search {
  width: 100%;
  height: 4vh;
}

.list {
  width: 100%;
  height: 40vh;
  margin-top: 6px;
  overflow-y: auto;
  overflow-x: hidden;

  .flight-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    margin-top: 0;
  }

  .flight-table th {
    position: sticky;
    top: 0;
    color: #fff;
    background-color: silver;
    z-index: 1;
  }

  .flight-table th,
  .flight-table td {
    border: none;
    padding: 4px;
    font-size: .7vw;
    text-align: center;
  }

  .flight-table .odd-row {
    height: 0.5vh;
    background: linear-gradient(270deg, rgba(69, 78, 92, 0) 0%, rgba(178, 192, 213, 0.41) 100%);
  }

  .flight-table .even-row {
    height: 0.5vh;
    background: linear-gradient(270deg, rgba(111, 153, 215, 0) 0%, rgba(111, 153, 215, 0.51) 100%);
  }
}

.titleBg {
  width: 100%;
  height: 40px;
  line-height: 40px;
  margin: 1vh 0;
  background-size: 100% 100%;
  background-position: center center;
  background-image: url('@/assets/screen/titleBgLong.png');

  span {
    margin-left: 42px;
    font-size: 16px;
    letter-spacing: .1em;
  }
}

.selected-row {
  background-color: #09559a !important;
}

:deep(.el-date-editor.el-input__wrapper) {
  background: rgba(11, 35, 57, 0.4);
  box-shadow: 0 0 0 1px #09559a inset;
}

:deep(.el-date-editor .el-range-input) {
  color: #FFFFFF;
}

:deep(.el-date-editor .el-range__icon) {
  color: #FFFFFF;
}

:deep(.el-select__wrapper) {
  background: rgba(11, 35, 57, 0.4);
  box-shadow: 0 0 0 1px #09559a;
}

:deep(.el-select__placeholder) {
  color: #FFFFFF;
}
:deep(.cesium-viewer-bottom) {
  display: none;
}
</style>