<template>
  <div class="dock-control-panel">
    <!-- title -->
    <div class="dock-control-panel-header fz16 pl5 pr5 flex-align-center flex-row flex-justify-between">
      <span class="fz12">设备控制<span class="fz12 pl15">{{ props.sn }}</span></span>
      <span @click="closeControlPanel">
        <CloseOutlined />
      </span>
    </div>
    <!-- setting -->
    <DeviceSettingBox :sn="props.sn" :deviceInfo="props.deviceInfo"></DeviceSettingBox>
    <!-- cmd -->
    <div class="control-cmd-wrapper">
      <div class="control-cmd-header">
        设备远程控制
        <a-switch class="debug-btn" checked-children="开" un-checked-children="关" v-model:checked="debugStatus"
          @change="onDeviceStatusChange" />
      </div>
      <div class="control-cmd-box">
        <div v-for="(cmdItem, index) in cmdList" :key="cmdItem.cmdKey" class="control-cmd-item">
          <div class="control-cmd-item-left">
            <div class="item-label">{{ cmdItem.label }}</div>
            <div class="item-status">{{ cmdItem.status }}</div>
          </div>
          <div class="control-cmd-item-right">
            <a-dropdown :disabled="!debugStatus || cmdItem.disabled" v-if="cmdItem.func === 'airConditionerStatus'">
              <template #overlay>
                <a-menu @click="handleMenuClick">
                  <a-menu-item key="0">
                    空闲
                  </a-menu-item>
                  <a-menu-item key="1">
                    制冷
                  </a-menu-item>
                  <a-menu-item key="2">
                    制热
                  </a-menu-item>
                  <a-menu-item key="3">
                    除湿
                  </a-menu-item>
                </a-menu>
              </template>
              <a-button :loading="cmdItem.loading" size="small" type="primary">
                切换
                <DownOutlined />
              </a-button>
            </a-dropdown>
            <a-button v-else :disabled="!debugStatus || cmdItem.disabled" :loading="cmdItem.loading" size="small"
              type="primary" @click="sendControlCmd(cmdItem, index)">
              {{ cmdItem.operateText }}
            </a-button>
          </div>
        </div>
      </div>
    </div>
  </div>

</template>

<script setup lang="ts">
import { defineProps, defineEmits, ref, watch } from 'vue'
import {
  CloseOutlined, DownOutlined
} from '@ant-design/icons-vue'
import { useDockControl } from './use-dock-control'
import { DeviceInfoType, EDockModeCode } from '@/types/device'
import { cmdList as baseCmdList, DeviceCmdItem } from '@/types/device-cmd'
import store from '@/store'
import { updateDeviceCmdInfoByOsd, updateDeviceCmdInfoByExecuteInfo } from '@/utils/device-cmd'
import DeviceSettingBox from './DeviceSettingBox.vue'

const props = defineProps<{
  sn: string,
  deviceInfo: DeviceInfoType,
}>()


const initCmdList = baseCmdList.map(cmdItem => Object.assign({}, cmdItem))
const cmdList = ref(initCmdList)

// 根据机场指令执行状态更新信息
watch(() => store.state.dock.devicesCmdExecuteInfo, (devicesCmdExecuteInfo) => {
  if (props.sn && devicesCmdExecuteInfo[props.sn]) {
    updateDeviceCmdInfoByExecuteInfo(cmdList.value, devicesCmdExecuteInfo[props.sn])
  }
}, {
  immediate: true,
  deep: true,
})

// 根据设备osd信息更新信息
watch(() => props.deviceInfo, (value) => {
  updateDeviceCmdInfoByOsd(cmdList.value, value)
  // console.log('deviceInfo', value)
}, {
  immediate: true,
  deep: true
})

// dock 控制指令
const debugStatus = ref(props.deviceInfo.dock?.basic_osd?.mode_code === EDockModeCode.远程调试中)
const emit = defineEmits(['close-control-panel'])

function closeControlPanel() {
  emit('close-control-panel', props.sn, debugStatus.value)
}

async function onDeviceStatusChange(status: boolean) {
  let result = false
  if (status) {
    result = await dockDebugOnOff(props.sn, true)
  } else {
    result = await dockDebugOnOff(props.sn, false)
  }
  if (!result) {
    if (status) {
      debugStatus.value = false
    } else {
      debugStatus.value = true
    }
  }
}

const {
  sendDockControlCmd,
  dockDebugOnOff
} = useDockControl()

async function sendControlCmd(cmdItem: DeviceCmdItem, index: number) {
  const success = await sendDockControlCmd({
    sn: props.sn,
    cmd: cmdItem.cmdKey,
    action: cmdItem.action
  }, true)
  if (success) {
    // updateDeviceSingleCmdInfo(cmdList.value[index])
  }
}

const handleMenuClick = async (e: { key: string }) => {
  const index = cmdList.value.findIndex(item => item.func === 'airConditionerStatus')
  await sendDockControlCmd({
    sn: props.sn,
    cmd: cmdList.value[index].cmdKey,
    action: e.key
  }, true)
}

watch(() => {
  const modeCode = props.deviceInfo?.dock?.basic_osd?.mode_code
  return modeCode !== undefined ? modeCode : -1  // 使用默认值
}, (newModeCode) => {
  debugStatus.value = newModeCode === EDockModeCode.远程调试中
}, {
  immediate: true
})

// 监听sn变化，重置cmdList状态
watch(() => props.sn, () => {
  // 重置cmdList为初始状态
  cmdList.value = baseCmdList.map(cmdItem => Object.assign({}, cmdItem))
  // 立即根据新设备的信息更新cmdList状态
  if (props.deviceInfo) {
    updateDeviceCmdInfoByOsd(cmdList.value, props.deviceInfo)
  }
  // 根据指令执行状态更新
  if (props.sn && store.state.dock.devicesCmdExecuteInfo[props.sn]) {
    updateDeviceCmdInfoByExecuteInfo(cmdList.value, store.state.dock.devicesCmdExecuteInfo[props.sn])
  }
})

</script>

<style lang='scss' scoped>
.dock-control-panel {
  position: absolute;
  left: calc(100% + 10px);
  top: 0px;
  width: calc(100% - 255px);
  height: calc(100%);
  padding: 0 !important;
  background: #000;
  color: #fff;
  border-radius: 2px;
  overflow-y: scroll;

  .dock-control-panel-header {
    border-bottom: 1px solid #515151;
  }

  .control-cmd-wrapper {
    .control-cmd-header {
      font-size: 14px;
      font-weight: 600;
      padding: 10px 10px 0px;

      .debug-btn {
        margin-left: 10px;
        border: 1px solid #585858;
      }
    }

    .control-cmd-box {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      padding: 4px 10px;

      .control-cmd-item {
        width: 220px;
        height: 58px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        border: 1px solid #666;
        margin: 4px 0;
        padding: 0 8px;

        .control-cmd-item-left {
          display: flex;
          flex-direction: column;

          .item-label {
            font-weight: 700;
          }
        }
      }
    }
  }
}
</style>
