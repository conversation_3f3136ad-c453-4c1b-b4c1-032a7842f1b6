<template>
  <div>
    <basic-container>
      <avue-form :option="index === 0 ? option1 : option2" v-model="form" @tab-click="handleTabClick"
        @submit="handleSubmit"></avue-form>
    </basic-container>
  </div>
</template>

<script>
// import option from "@/option/user/info";
import { getUserInfo, updateInfo, updatePassword } from "@/api/system/user";
import md5 from "js-md5";
import func from '@/utils/func';
export default {
  data() {

    const validatePass = (rule, value, callback) => {
      const strongRegex = new RegExp(
        /^(?![^a-zA-Z]+$)(?!\D+$)(?=.*[$@$!%*?&/])/
      );
      if (value.indexOf(" ") >= 0) {
        callback(new Error("密码不能带空格"));
      } else if (strongRegex.test(value) == false || value.length < 8) {
        callback(
          new Error("请使用字母、数字、特殊字符组合的密码，长度至少为8位。")
        );
      } else {
        callback();
      }
    };

    const validatePass2 = (rule, value, callback) => {
      if (this.index == 0) {
        callback();
      } else if (value === "") {
        callback(new Error("请再次输入密码"));
      } else if (value !== this.form.newPassword) {
        callback(new Error("两次输入密码不一致!"));
      } else if (value.indexOf(" ") >= 0) {
        callback(new Error("密码不能带空格"));
      } else {
        callback();
      }
    };
    return {
      index: 0,
      option1: {
        tabs: true,
        tabsActive: 1,
        group: [
          {
            label: "个人信息",
            prop: "info",
            column: [
              {
                label: "头像",
                type: "upload",
                listType: "picture-img",
                propsHttp: {
                  res: "data",
                  url: "link",
                },
                canvasOption: {
                  text: " ",
                  ratio: 0.1,
                },
                action: "/api/blade-resource/oss/endpoint/put-file",
                tip: "只能上传jpg/png用户头像，且不超过500kb",
                span: 12,
                row: true,
                prop: "avatar",
              },
              {
                label: "姓名",
                span: 12,
                row: true,
                prop: "name",
              },
              {
                label: "用户名",
                span: 12,
                row: true,
                prop: "realName",
              },
              {
                label: "手机号",
                span: 12,
                row: true,
                prop: "phone",
              },
              {
                label: "邮箱",
                prop: "email",
                span: 12,
                row: true,
              },
            ],
          },
          {
            label: "修改密码",
            prop: "password",
            column: [
              {
                label: "原密码",
                span: 12,
                row: true,
                type: "password",
                prop: "oldPassword",
                // rules: [
                //   { required: true, validator: validatePass, trigger: "blur" },
                // ],
              },
              {
                label: "新密码",
                span: 12,
                row: true,
                type: "password",
                prop: "newPassword",
                // rules: [
                //   { required: true, validator: validatePass, trigger: "blur" },
                // ],
              },
              {
                label: "确认密码",
                span: 12,
                row: true,
                type: "password",
                prop: "newPassword1",
                // rules: [
                //   { required: true, validator: validatePass2, trigger: "blur" },
                // ],
              },
            ],
          },
        ],
      },
      option2: {
        tabs: true,
        tabsActive: 1,
        group: [
          {
            label: "个人信息",
            prop: "info",
            column: [
              {
                label: "头像",
                type: "upload",
                listType: "picture-img",
                propsHttp: {
                  res: "data",
                  url: "link",
                },
                canvasOption: {
                  text: " ",
                  ratio: 0.1,
                },
                action: "/api/blade-resource/oss/endpoint/put-file",
                tip: "只能上传jpg/png用户头像，且不超过500kb",
                span: 12,
                row: true,
                prop: "avatar",
              },
              {
                label: "姓名",
                span: 12,
                row: true,
                prop: "name",
              },
              {
                label: "用户名",
                span: 12,
                row: true,
                prop: "realName",
              },
              {
                label: "手机号",
                span: 12,
                row: true,
                prop: "phone",
              },
              {
                label: "邮箱",
                prop: "email",
                span: 12,
                row: true,
              },
            ],
          },
          {
            label: "修改密码",
            prop: "password",
            column: [
              {
                label: "原密码",
                span: 12,
                row: true,
                type: "password",
                prop: "oldPassword",
                rules: [
                  { required: true, trigger: "blur", message: '请输入原密码' },
                ],
              },
              {
                label: "新密码",
                span: 12,
                row: true,
                type: "password",
                prop: "newPassword",
                rules: [
                  { required: true, validator: validatePass, trigger: "blur" },
                ],
              },
              {
                label: "确认密码",
                span: 12,
                row: true,
                type: "password",
                prop: "newPassword1",
                rules: [
                  { required: true, validator: validatePass2, trigger: "blur" },
                ],
              },
            ],
          },
        ],
      },
      form: {},
    };
  },
  created() {
    console.log(this.option, "option");
    this.handleWitch();
    if (this.$route.query.tab == 2) {
      this.option.tabsActive = 2;
    }
  },
  methods: {
    handleSubmit(form, done) {
      console.log(this.index, "index");
      if (this.index === 0) {
        updateInfo(form).then(
          (res) => {
            if (res.data.success) {
              this.$message({
                type: "success",
                message: "修改信息成功!",
              });
            } else {
              this.$message({
                type: "error",
                message: res.data.msg,
              });
            }
            done();
          },
          (error) => {
            window.console.log(error);
            done();
          }
        );
      } else {
        // const strongRegex = new RegExp("^(?=.{8,})(?=.*[A-Z])(?=.*[a-z])(?=.*[0-9])(?=.*\\W).*$", "g");
        // const isResult = strongRegex.test(form.oldPassword)
        // if(isResult==false){
        //   this.$message.error('请至少使用大小写字母、数字、符号两种类型组合的密码，长度至少为8位。')
        //   return done();
        // }
        updatePassword(
          md5(form.oldPassword),
          md5(form.newPassword),
          md5(form.newPassword1)
        ).then(
          (res) => {
            if (res.data.success) {
              this.$message({
                type: "success",
                message: "修改密码成功!",
              });
            } else {
              this.$message({
                type: "error",
                message: res.data.msg,
              });
            }
            done();
          },
          (error) => {
            window.console.log(error);
            done();
          }
        );
      }
    },
    handleWitch() {
      if (this.index === 0) {
        getUserInfo().then((res) => {
          const user = res.data.data;
          this.form = {
            id: user.id,
            avatar: user.avatar,
            name: user.name,
            realName: user.realName,
            phone: user.phone,
            email: user.email,
          };
        });
      }
    },
    handleTabClick(tabs) {
      console.log(tabs.index, "tabs");
      if (tabs.index !== undefined) {
        this.index = func.toInt(tabs.index);
        this.handleWitch();
      }
    },
  },
};
</script>

<style></style>
