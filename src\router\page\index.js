import Store from '@/store';
import {ERouterName} from '@/api/enum/index'

export default [
  {
    path: '/login',
    name: '登录页',
    component: () =>
      Store.getters.isMacOs ? import('@/mac/login.vue') : import('@/page/login/index.vue'),
    meta: {
      requiresAuth: false,
      keepAlive: true,
      isTab: false,
      isAuth: false,
    },
  },
  {
    path: '/auth',
    name: '认证',
    component: () => import('@/views/auth/index.vue'),
    meta: {
      isAuth: false,
    },
  },
  {
    path: '/screen',
    name: '大屏',
    component: () => import('@/views/screen/index.vue'),
  },
  {
    path: '/workSite',
    name: '工地大屏 ',
    component: () => import('@/views/workSite/index.vue'),
  },
  {
    path: '/modelBase/showModel',
    name: '模型显示',
    component: () => import('@/views/modelBase/showModel.vue'),
  },
  {
    path: '/pilot-login',
    name: '无人机pilot登录页',
    component: () => import('@/page/page-pilot/pilot-index.vue'),
    meta: {
      keepAlive: true,
      requiresAuth: true,
      isTab: false,
      isAuth: false,
    },
  },
  {
    path: '/pilot-home',
    name: '无人机pilot主页',
    component: () => import('@/page/page-pilot/pilot-home.vue'),
    meta: {
      keepAlive: true,
      isTab: false,
    },
  },
  {
    path: '/pilot-media',
    name: '媒体文件上传-云飞',
    component: () => import('@/page/page-pilot/pilot-media.vue'),
    meta: {
      keepAlive: true,
      isTab: false,
    },
  },
  {
    path: '/pilot-liveshare',
    name: '无人机直播-云飞',
    component: () => import('@/page/page-pilot/pilot-liveshare.vue'),
    meta: {
      keepAlive: true,
      isTab: false,
    },
  },
  {
    path: '/pilot-bind',
    name: '无人机-云飞',
    component: () => import('@/page/page-pilot/pilot-bind.vue'),
    meta: {
      keepAlive: true,
      isTab: false,
    },
  },
  {
    path: '/lock',
    name: '锁屏页',
    component: () =>
      Store.getters.isMacOs ? import('@/mac/lock.vue') : import('@/page/lock/index.vue'),
    meta: {
      keepAlive: true,
      isTab: false,
      isAuth: false,
    },
  },
  {
    path: '/404',
    component: () => import(/* webpackChunkName: "page" */ '@/components/error-page/404.vue'),
    name: '404',
    meta: {
      keepAlive: true,
      isTab: false,
      isAuth: false,
    },
  },
  {
    path: '/403',
    component: () => import(/* webpackChunkName: "page" */ '@/components/error-page/403.vue'),
    name: '403',
    meta: {
      keepAlive: true,
      isTab: false,
      isAuth: false,
    },
  },
  {
    path: '/500',
    component: () => import(/* webpackChunkName: "page" */ '@/components/error-page/500.vue'),
    name: '500',
    meta: {
      keepAlive: true,
      isTab: false,
      isAuth: true,
    },
  },
  {
    path: '/',
    name: '主页',
    redirect: '/wel',
  },
  {
    path: '/' + ERouterName.WORKSPACE,
    name: ERouterName.WORKSPACE,
    component: () => import('@/views/workspace/workspace.vue'),
    redirect: '/' + ERouterName.TSA,
    children: [
      {
        path: '/' + ERouterName.TSA,
        component: () => import('@/views/tsa/index.vue')
      },
      {
        path: '/' + ERouterName.TASK,
        name: '任务计划库',
        component: () => import('@/views/task/index.vue'),
      },
      {
        path: '/' + ERouterName.WAYLINE,
        name: '航线库',
        component: () => import('@/views/wayline/index.vue')
      },
      {
        path: '/' + ERouterName.LAYER,
        name: '标注',
        component: () => import('@/views/layer/index.vue')
      },
      {
        path: '/' + ERouterName.FLIGHT_AREA,
        name: '自定义飞行区',
        component: () => import('@/views/flight-area/index.vue')
      },
      // {
      //   path: '/' + ERouterName.LIVESTREAM,
      //   name: '直播',
      //   component: () => import('@/views/livestream/index.vue')
      // },
    ]
  },
  {
    path: '/' + ERouterName.MEMBERS,
    name: '成员',
    component: () => import('@/views/members/index.vue')
  },
  {
    path: '/' + ERouterName.MEDIA,
    name: '媒体库',
    component: () => import('@/views/media/index.vue'),
  },
];