{"asset": {"gltfUpAxis": "Z", "version": "0.0"}, "geometricError": 0.31417059898376465, "root": {"boundingVolume": {"box": [-6.60688591003418, -43.45102310180664, -34.64681625366211, 26.761232376098633, 0.0, 0.0, 0.0, 20.44466781616211, 0.0, 0.0, 0.0, 8.099579811096191]}, "children": [{"boundingVolume": {"box": [-17.757400512695312, -43.45102310180664, -33.186641693115234, 15.610718727111816, 0.0, 0.0, 0.0, 20.44466781616211, 0.0, 0.0, 0.0, 6.636504173278809]}, "children": [{"boundingVolume": {"box": [-14.507698059082031, -51.969635009765625, -37.27845764160156, 12.361017227172852, 0.0, 0.0, 0.0, 11.926055908203125, 0.0, 0.0, 0.0, 2.544689178466797]}, "children": [{"boundingVolume": {"box": [-19.655532836914062, -51.59199523925781, -38.030941009521484, 7.209527015686035, 0.0, 0.0, 0.0, 11.54841423034668, 0.0, 0.0, 0.0, 1.7731742858886719]}, "children": [{"boundingVolume": {"box": [-19.655532836914062, -51.59199523925781, -38.029544830322266, 7.209527015686035, 0.0, 0.0, 0.0, 11.54841423034668, 0.0, 0.0, 0.0, 1.7717781066894531]}, "children": [{"boundingVolume": {"box": [-17.764053344726562, -56.403831481933594, -39.20366668701172, 5.318046569824219, 0.0, 0.0, 0.0, 6.736574172973633, 0.0, 0.0, 0.0, 0.5920619964599609]}, "content": {"uri": "Block_L23_231.b3dm"}, "geometricError": 0.010726920329034328, "refine": "REPLACE"}, {"boundingVolume": {"box": [-19.655532836914062, -44.85541915893555, -37.921226501464844, 7.209527015686035, 0.0, 0.0, 0.0, 4.811840057373047, 0.0, 0.0, 0.0, 1.6634578704833984]}, "content": {"uri": "Block_L23_230.b3dm"}, "geometricError": 0.01032212469726801, "refine": "REPLACE"}], "content": {"uri": "Block_L22_234.b3dm"}, "geometricError": 0.020936233922839165, "refine": "REPLACE"}], "content": {"uri": "Block_L21_149.b3dm"}, "geometricError": 0.04188736900687218, "refine": "REPLACE"}, {"boundingVolume": {"box": [-7.296343803405762, -51.969635009765625, -37.249900817871094, 5.149662971496582, 0.0, 0.0, 0.0, 11.926055908203125, 0.0, 0.0, 0.0, 2.5161304473876953]}, "children": [{"boundingVolume": {"box": [-7.296343803405762, -56.93882751464844, -37.40959548950195, 5.149662971496582, 0.0, 0.0, 0.0, 6.956865310668945, 0.0, 0.0, 0.0, 2.356433868408203]}, "children": [{"boundingVolume": {"box": [-7.296343803405762, -56.93882751464844, -37.40951919555664, 5.149662971496582, 0.0, 0.0, 0.0, 6.956865310668945, 0.0, 0.0, 0.0, 2.3563575744628906]}, "content": {"uri": "Block_L23_229.b3dm"}, "geometricError": 0.010474681854248047, "refine": "REPLACE"}], "content": {"uri": "Block_L22_233.b3dm"}, "geometricError": 0.020946411415934563, "refine": "REPLACE"}, {"boundingVolume": {"box": [-9.442036628723145, -45.01277160644531, -38.04574966430664, 3.00396990776062, 0.0, 0.0, 0.0, 4.96919059753418, 0.0, 0.0, 0.0, 1.7026176452636719]}, "children": [{"boundingVolume": {"box": [-9.442036628723145, -45.01277160644531, -38.04475402832031, 3.00396990776062, 0.0, 0.0, 0.0, 4.96919059753418, 0.0, 0.0, 0.0, 1.6976070404052734]}, "content": {"uri": "Block_L23_228.b3dm"}, "geometricError": 0.010327423922717571, "refine": "REPLACE"}], "content": {"uri": "Block_L22_232.b3dm"}, "geometricError": 0.020644553005695343, "refine": "REPLACE"}, {"boundingVolume": {"box": [-4.2923736572265625, -45.01277160644531, -37.24522399902344, 2.145693063735962, 0.0, 0.0, 0.0, 4.96919059753418, 0.0, 0.0, 0.0, 2.508523941040039]}, "children": [{"boundingVolume": {"box": [-4.2923736572265625, -45.01277160644531, -37.24522399902344, 2.145693063735962, 0.0, 0.0, 0.0, 4.96919059753418, 0.0, 0.0, 0.0, 2.508523941040039]}, "content": {"uri": "Block_L23_227.b3dm"}, "geometricError": 0.009911313652992249, "refine": "REPLACE"}], "content": {"uri": "Block_L22_231.b3dm"}, "geometricError": 0.019818704575300217, "refine": "REPLACE"}], "content": {"uri": "Block_L21_148.b3dm"}, "geometricError": 0.04090038314461708, "refine": "REPLACE"}], "content": {"uri": "Block_L20_76.b3dm"}, "geometricError": 0.08233332633972168, "refine": "REPLACE"}, {"boundingVolume": {"box": [-17.757400512695312, -31.524967193603516, -32.77069091796875, 15.610718727111816, 0.0, 0.0, 0.0, 8.518611907958984, 0.0, 0.0, 0.0, 6.220551490783691]}, "content": {"uri": "Block_L20_75.json"}, "geometricError": 10000000000.0, "refine": "REPLACE"}], "content": {"uri": "Block_L19_36.b3dm"}, "geometricError": 0.15417098999023438, "refine": "REPLACE"}, {"boundingVolume": {"box": [9.003832817077637, -47.404808044433594, -37.87029266357422, 11.150513648986816, 0.0, 0.0, 0.0, 8.132818222045898, 0.0, 0.0, 0.0, 3.5440845489501953]}, "content": {"uri": "Block_L19_35.json"}, "geometricError": 10000000000.0, "refine": "REPLACE"}, {"boundingVolume": {"box": [9.003832817077637, -31.13917350769043, -38.37422180175781, 11.150513648986816, 0.0, 0.0, 0.0, 8.132818222045898, 0.0, 0.0, 0.0, 4.3501434326171875]}, "content": {"uri": "Block_L19_34.json"}, "geometricError": 10000000000.0, "refine": "REPLACE"}], "content": {"uri": "Block_L18_16.b3dm"}, "geometricError": 0.31417059898376465, "refine": "REPLACE"}}