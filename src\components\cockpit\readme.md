## 驾驶舱功能开发

## 功能列表

- 驾驶舱首页 topBar.vue
- 驾驶舱内容 cockpit.vue
- 仪表盘 dashBoardBox.vue
- 进度条以及状态 cockpitProcess.vue
- 视频操作 cockpit_video.vue
- 地图控件 cockpitLayer.vue
- 负载控制器 controlContainer.vue

## 功能开发

### 1. 驾驶舱首页 topBar.vue

- 功能：展示当前时间、天气、温度、湿度等信息。

### 2. 驾驶舱内容 cockpit.vue
- 功能：展示驾驶舱的主要内容，包括仪表盘、进度条、视频操作、地图控件等。

### 3. 仪表盘 dashBoardBox.vue

- 功能：高度，速度。指南针为顺时针0~180deg，逆时针0~-180deg。

### 4. 进度条以及状态 cockpitProcess.vue

- 功能：展示进度条以及机场飞行器状态，

### 5. 视频操作 cockpit_video.vue

- 功能：展示视频操作，包含设备。以及设备与负载控制器的联动。

### 6. 地图控件 cockpitLayer.vue
- 功能：展示地图控件，包含地图、图层、标注等。

### 7. 负载控制器 controlContainer.vue

- 功能：展示负载控制器，包含负载、负载状态、负载控制等。

## 功能开发进度

- [y] 驾驶舱首页 topBar.vue
- [y] 驾驶舱内容 cockpit.vue
- [y] 仪表盘 dashBoardBox.vue
- [y] 进度条以及状态 cockpitProcess.vue
- [-] 视频操作 cockpit_video.vue 90%
- [y] 地图控件 cockpitLayer.vue
- [-] 负载控制器 controlContainer.vue 90%

## 功能开发原理
- 基本上操作cockpit以及controlContainer的联动；
- 视频cockpit_video和负载controlContainer的联动；
- 监听都处在cockpit.vue中，通过EventBus传递和props给其他组件；
- worker是用来接口转译的。通过postMessage传递消息，通过onmessage接收消息。
- 新增了一个use-g-map-cockpit.ts 图层，用于地图的图层展示以及一系列操作。

## 遗留内容

- 新功能的负载需要加一下

[接口](https://apifox.com/apidoc/shared-8d4455c5-8c5b-4135-8979-83adf745d4f4/api-241205667)

![alt text](Snipaste_2024-12-10_18-37-20.png) 


[负载文档](https://developer.dji.com/doc/cloud-api-tutorial/cn/api-reference/dock-to-cloud/mqtt/dock/dock2/drc.html)

![alt text](Snipaste_2024-12-10_18-38-14.png)

