[INFO]    Initializing ODM 3.5.5 - Fri May 30 02:07:16  2025
[INFO]    ==============
[INFO]    3d_tiles: False
[INFO]    align: None
[INFO]    auto_boundary: True
[INFO]    auto_boundary_distance: 0
[INFO]    bg_removal: False
[INFO]    boundary: {}
[INFO]    build_overviews: False
[INFO]    camera_lens: auto
[INFO]    cameras: {}
[INFO]    cog: True
[INFO]    copy_to: None
[INFO]    crop: 3
[INFO]    dem_decimation: 1
[INFO]    dem_euclidean_map: False
[INFO]    dem_gapfill_steps: 3
[INFO]    dem_resolution: 5
[INFO]    dsm: False
[INFO]    dtm: False
[INFO]    end_with: odm_postprocess
[INFO]    fast_orthophoto: False
[INFO]    feature_quality: high
[INFO]    feature_type: dspsift
[INFO]    force_gps: False
[INFO]    gcp: None
[INFO]    geo: None
[INFO]    gltf: True
[INFO]    gps_accuracy: 3
[INFO]    ignore_gsd: False
[INFO]    matcher_neighbors: 0
[INFO]    matcher_order: 0
[INFO]    matcher_type: flann
[INFO]    max_concurrency: 32
[INFO]    merge: all
[INFO]    mesh_octree_depth: 12
[INFO]    mesh_size: 300000
[INFO]    min_num_features: 10000
[INFO]    name: 782885c2-b560-4bde-9689-8f08139ca019
[INFO]    no_gpu: False
[INFO]    optimize_disk_space: False
[INFO]    orthophoto_compression: DEFLATE
[INFO]    orthophoto_cutline: False
[INFO]    orthophoto_kmz: False
[INFO]    orthophoto_no_tiled: False
[INFO]    orthophoto_png: False
[INFO]    orthophoto_resolution: 5
[INFO]    pc_classify: False
[INFO]    pc_copc: False
[INFO]    pc_csv: False
[INFO]    pc_ept: True
[INFO]    pc_filter: 5
[INFO]    pc_las: False
[INFO]    pc_quality: high
[INFO]    pc_rectify: False
[INFO]    pc_sample: 0
[INFO]    pc_skip_geometric: False
[INFO]    primary_band: auto
[INFO]    project_path: /var/www/data/782885c2-b560-4bde-9689-8f08139ca019
[INFO]    radiometric_calibration: none
[INFO]    rerun: None
[INFO]    rerun_all: False
[INFO]    rerun_from: ['odm_postprocess']
[INFO]    rolling_shutter: False
[INFO]    rolling_shutter_readout: 0
[INFO]    sfm_algorithm: incremental
[INFO]    sfm_no_partial: False
[INFO]    skip_3dmodel: False
[INFO]    skip_band_alignment: False
[INFO]    skip_orthophoto: False
[INFO]    skip_report: False
[INFO]    sky_removal: False
[INFO]    sm_cluster: None
[INFO]    sm_no_align: False
[INFO]    smrf_scalar: 1.25
[INFO]    smrf_slope: 0.15
[INFO]    smrf_threshold: 0.5
[INFO]    smrf_window: 18.0
[INFO]    split: 999999
[INFO]    split_image_groups: None
[INFO]    split_overlap: 150
[INFO]    texturing_keep_unseen_faces: False
[INFO]    texturing_single_material: False
[INFO]    texturing_skip_global_seam_leveling: False
[INFO]    tiles: False
[INFO]    use_3dmesh: True
[INFO]    use_exif: False
[INFO]    use_fixed_camera_params: False
[INFO]    use_hybrid_bundle_adjustment: False
[INFO]    video_limit: 500
[INFO]    video_resolution: 4000
[INFO]    ==============
[INFO]    Running dataset stage
[INFO]    Loading dataset from: /var/www/data/782885c2-b560-4bde-9689-8f08139ca019/images
[INFO]    Loading images database: /var/www/data/782885c2-b560-4bde-9689-8f08139ca019/images.json
[INFO]    Found 104 usable images
[INFO]    Coordinates file already exist: /var/www/data/782885c2-b560-4bde-9689-8f08139ca019/odm_georeferencing/coords.txt
[INFO]    Model geo file already exist: /var/www/data/782885c2-b560-4bde-9689-8f08139ca019/odm_georeferencing/odm_georeferencing_model_geo.txt
[INFO]    Finished dataset stage
[INFO]    Running split stage
[INFO]    Normal dataset, will process all at once.
[INFO]    Finished split stage
[INFO]    Running merge stage
[INFO]    Normal dataset, nothing to merge.
[INFO]    Finished merge stage
[INFO]    Running opensfm stage
[WARNING] /var/www/data/782885c2-b560-4bde-9689-8f08139ca019/opensfm/image_list.txt already exists, not rerunning OpenSfM setup
[WARNING] /var/www/data/782885c2-b560-4bde-9689-8f08139ca019/opensfm/exif already exists, not rerunning photo to metadata
[WARNING] Detect features already done: /var/www/data/782885c2-b560-4bde-9689-8f08139ca019/opensfm/features exists
[WARNING] Match features already done: /var/www/data/782885c2-b560-4bde-9689-8f08139ca019/opensfm/matches exists
[WARNING] Found a valid OpenSfM tracks file in: /var/www/data/782885c2-b560-4bde-9689-8f08139ca019/opensfm/tracks.csv
[WARNING] Found a valid OpenSfM reconstruction file in: /var/www/data/782885c2-b560-4bde-9689-8f08139ca019/opensfm/reconstruction.json
[INFO]    Already extracted cameras
[INFO]    Export reconstruction stats
[WARNING] Found existing reconstruction stats /var/www/data/782885c2-b560-4bde-9689-8f08139ca019/opensfm/stats/stats.json
[WARNING] Will skip exporting /var/www/data/782885c2-b560-4bde-9689-8f08139ca019/opensfm/reconstruction.geocoords.json
[INFO]    Undistorting /var/www/data/782885c2-b560-4bde-9689-8f08139ca019/opensfm ...
[WARNING] Already undistorted (nominal)
[WARNING] Found a valid OpenSfM NVM reconstruction file in: /var/www/data/782885c2-b560-4bde-9689-8f08139ca019/opensfm/undistorted/reconstruction.nvm
[INFO]    Finished opensfm stage
[INFO]    Running openmvs stage
[WARNING] Found a valid OpenMVS reconstruction file in: /var/www/data/782885c2-b560-4bde-9689-8f08139ca019/opensfm/undistorted/openmvs/scene_dense_dense_filtered.ply
[INFO]    Finished openmvs stage
[INFO]    Running odm_filterpoints stage
[WARNING] Found a valid point cloud file in: /var/www/data/782885c2-b560-4bde-9689-8f08139ca019/odm_filterpoints/point_cloud.ply
[INFO]    Finished odm_filterpoints stage
[INFO]    Running odm_meshing stage
[WARNING] Found a valid ODM Mesh file in: /var/www/data/782885c2-b560-4bde-9689-8f08139ca019/odm_meshing/odm_mesh.ply
[INFO]    Finished odm_meshing stage
[INFO]    Running mvs_texturing stage
[INFO]    Writing MVS Textured file in: /var/www/data/782885c2-b560-4bde-9689-8f08139ca019/odm_texturing/odm_textured_model_geo.obj
[INFO]    Removing old tmp directory /var/www/data/782885c2-b560-4bde-9689-8f08139ca019/odm_texturing/tmp
[INFO]    running "/code/SuperBuild/install/bin/texrecon" "/var/www/data/782885c2-b560-4bde-9689-8f08139ca019/opensfm/undistorted/reconstruction.nvm" "/var/www/data/782885c2-b560-4bde-9689-8f08139ca019/odm_meshing/odm_mesh.ply" "/var/www/data/782885c2-b560-4bde-9689-8f08139ca019/odm_texturing/odm_textured_model_geo" -d gmi -o gauss_clamping -t none --no_intermediate_results     --max_texture_size=8192
/code/SuperBuild/install/bin/texrecon (built on Apr 16 2025, 02:44:39)
Load and prepare mesh:
Reading PLY: 278497 verts... 557719 faces... done.
Generating texture views:
NVM: Loading file...
NVM: Number of views: 104
NVM: Number of features: 128947


Loading 100%... done. (Took 1.714s)
Building adjacency graph:


Adding edges 100%... done. (Took 0.755s)
836349 total edges.
View selection:
Building BVH from 557719 faces... done. (Took: 236 ms)


Calculating face qualities 100%... done. (Took 5.384s)


Postprocessing face infos 100%... done. (Took 0.094s)
Maximum quality of a face within an image: 2857.85
Clamping qualities to 559.338 within normalization.
Optimizing:
Time[s]	Energy
0	555145
8	551602
9	544949
9	541602
9	540248
9	539413
10	538996
11	538696
12	538435
12	538285
12	538199
12	538113
13	538041
13	537979
219717 faces have not been seen
Took: 20.14s
Generating texture patches:
Running... done. (Took 5.285s)
14811 texture patches.
Running global seam leveling:
Create matrices for optimization...  done.
Lhs dimensionality: 242677 x 242677
Calculating adjustments:
Color channel 1: CG took 116 iterations. Residual is 9.80889e-05
Color channel 0: CG took 117 iterations. Residual is 9.13255e-05
Color channel 2: CG took 117 iterations. Residual is 9.46827e-05
Took 0.44 seconds


Adjusting texture patches 100%... done. (Took 1.017s)
Running local seam leveling:


Blending texture patches 100%... done. (Took 67.125s)
Generating texture atlases:
Sorting texture patches... done.

Working on atlas 75 100%... done.
Finalizing texture atlases... done. (Took: 0s)
Building objmodel:
Saving model... done.
Whole texturing procedure took: 190.705s
[INFO]    Generating glTF Binary
[INFO]    Converting /var/www/data/782885c2-b560-4bde-9689-8f08139ca019/odm_texturing/odm_textured_model_geo.obj --> /var/www/data/782885c2-b560-4bde-9689-8f08139ca019/odm_texturing/odm_textured_model_geo.glb
[INFO]    Loading /var/www/data/782885c2-b560-4bde-9689-8f08139ca019/odm_texturing/odm_textured_model_geo.obj
[INFO]    Loading odm_textured_model_geo_material0000_map_Kd.png
[INFO]    Loading odm_textured_model_geo_material0001_map_Kd.png
[INFO]    Loading odm_textured_model_geo_material0002_map_Kd.png
[INFO]    Loading odm_textured_model_geo_material0003_map_Kd.png
[INFO]    Loading odm_textured_model_geo_material0004_map_Kd.png
[INFO]    Loading odm_textured_model_geo_material0005_map_Kd.png
[INFO]    Loading odm_textured_model_geo_material0006_map_Kd.png
[INFO]    Loading odm_textured_model_geo_material0007_map_Kd.png
[INFO]    Loading odm_textured_model_geo_material0008_map_Kd.png
[INFO]    Loading odm_textured_model_geo_material0009_map_Kd.png
[INFO]    Loading odm_textured_model_geo_material0010_map_Kd.png
[INFO]    Loading odm_textured_model_geo_material0011_map_Kd.png
[INFO]    Loading odm_textured_model_geo_material0012_map_Kd.png
[INFO]    Loading odm_textured_model_geo_material0013_map_Kd.png
[INFO]    Loading odm_textured_model_geo_material0014_map_Kd.png
[INFO]    Loading odm_textured_model_geo_material0015_map_Kd.png
[INFO]    Loading odm_textured_model_geo_material0016_map_Kd.png
[INFO]    Loading odm_textured_model_geo_material0017_map_Kd.png
[INFO]    Loading odm_textured_model_geo_material0018_map_Kd.png
[INFO]    Loading odm_textured_model_geo_material0019_map_Kd.png
[INFO]    Loading odm_textured_model_geo_material0020_map_Kd.png
[INFO]    Loading odm_textured_model_geo_material0021_map_Kd.png
[INFO]    Loading odm_textured_model_geo_material0022_map_Kd.png
[INFO]    Loading odm_textured_model_geo_material0023_map_Kd.png
[INFO]    Loading odm_textured_model_geo_material0024_map_Kd.png
[INFO]    Loading odm_textured_model_geo_material0025_map_Kd.png
[INFO]    Loading odm_textured_model_geo_material0026_map_Kd.png
[INFO]    Loading odm_textured_model_geo_material0027_map_Kd.png
[INFO]    Loading odm_textured_model_geo_material0028_map_Kd.png
[INFO]    Loading odm_textured_model_geo_material0029_map_Kd.png
[INFO]    Loading odm_textured_model_geo_material0030_map_Kd.png
[INFO]    Loading odm_textured_model_geo_material0031_map_Kd.png
[INFO]    Loading odm_textured_model_geo_material0032_map_Kd.png
[INFO]    Loading odm_textured_model_geo_material0033_map_Kd.png
[INFO]    Loading odm_textured_model_geo_material0034_map_Kd.png
[INFO]    Loading odm_textured_model_geo_material0035_map_Kd.png
[INFO]    Loading odm_textured_model_geo_material0036_map_Kd.png
[INFO]    Loading odm_textured_model_geo_material0037_map_Kd.png
[INFO]    Loading odm_textured_model_geo_material0038_map_Kd.png
[INFO]    Loading odm_textured_model_geo_material0039_map_Kd.png
[INFO]    Loading odm_textured_model_geo_material0040_map_Kd.png
[INFO]    Loading odm_textured_model_geo_material0041_map_Kd.png
[INFO]    Loading odm_textured_model_geo_material0042_map_Kd.png
[INFO]    Loading odm_textured_model_geo_material0043_map_Kd.png
[INFO]    Loading odm_textured_model_geo_material0044_map_Kd.png
[INFO]    Loading odm_textured_model_geo_material0045_map_Kd.png
[INFO]    Loading odm_textured_model_geo_material0046_map_Kd.png
[INFO]    Loading odm_textured_model_geo_material0047_map_Kd.png
[INFO]    Loading odm_textured_model_geo_material0048_map_Kd.png
[INFO]    Loading odm_textured_model_geo_material0049_map_Kd.png
[INFO]    Loading odm_textured_model_geo_material0050_map_Kd.png
[INFO]    Loading odm_textured_model_geo_material0051_map_Kd.png
[INFO]    Loading odm_textured_model_geo_material0052_map_Kd.png
[INFO]    Loading odm_textured_model_geo_material0053_map_Kd.png
[INFO]    Loading odm_textured_model_geo_material0054_map_Kd.png
[INFO]    Loading odm_textured_model_geo_material0055_map_Kd.png
[INFO]    Loading odm_textured_model_geo_material0056_map_Kd.png
[INFO]    Loading odm_textured_model_geo_material0057_map_Kd.png
[INFO]    Loading odm_textured_model_geo_material0058_map_Kd.png
[INFO]    Loading odm_textured_model_geo_material0059_map_Kd.png
[INFO]    Loading odm_textured_model_geo_material0060_map_Kd.png
[INFO]    Loading odm_textured_model_geo_material0061_map_Kd.png
[INFO]    Loading odm_textured_model_geo_material0062_map_Kd.png
[INFO]    Loading odm_textured_model_geo_material0063_map_Kd.png
[INFO]    Loading odm_textured_model_geo_material0064_map_Kd.png
[INFO]    Loading odm_textured_model_geo_material0065_map_Kd.png
[INFO]    Loading odm_textured_model_geo_material0066_map_Kd.png
[INFO]    Loading odm_textured_model_geo_material0067_map_Kd.png
[INFO]    Loading odm_textured_model_geo_material0068_map_Kd.png
[INFO]    Loading odm_textured_model_geo_material0069_map_Kd.png
[INFO]    Loading odm_textured_model_geo_material0070_map_Kd.png
[INFO]    Loading odm_textured_model_geo_material0071_map_Kd.png
[INFO]    Loading odm_textured_model_geo_material0072_map_Kd.png
[INFO]    Loading odm_textured_model_geo_material0073_map_Kd.png
[INFO]    Loading odm_textured_model_geo_material0074_map_Kd.png
[INFO]    Writing...
[INFO]    Wrote /var/www/data/782885c2-b560-4bde-9689-8f08139ca019/odm_texturing/odm_textured_model_geo.glb
[INFO]    Compressing with draco
[INFO]    running draco_transcoder -i "/var/www/data/782885c2-b560-4bde-9689-8f08139ca019/odm_texturing/odm_textured_model_geo.glb" -o "/var/www/data/782885c2-b560-4bde-9689-8f08139ca019/odm_texturing/odm_textured_model_geo_compressed.glb" -qt 16 -qp 16
Transcode	/var/www/data/782885c2-b560-4bde-9689-8f08139ca019/odm_texturing/odm_textured_model_geo.glb	4096
[INFO]    Finished mvs_texturing stage
[INFO]    Running odm_georeferencing stage
[INFO]    Georeferencing point cloud
[INFO]    las scale calculated as the minimum of 1/10 estimated spacing or 0.001, which ever is less.
[INFO]    running pdal translate -i "/var/www/data/782885c2-b560-4bde-9689-8f08139ca019/odm_filterpoints/point_cloud.ply" -o "/var/www/data/782885c2-b560-4bde-9689-8f08139ca019/odm_georeferencing/odm_georeferenced_model.laz" ferry transformation --filters.ferry.dimensions="views => UserData" --filters.transformation.matrix="1 0 0 269825.0 0 1 0 3097756.0 0 0 1 0 0 0 0 1" --writers.las.offset_x=269825.0 --writers.las.offset_y=3097756.0 --writers.las.scale_x=0.001 --writers.las.scale_y=0.001 --writers.las.scale_z=0.001 --writers.las.offset_z=0 --writers.las.a_srs="+proj=utm +zone=51 +datum=WGS84 +units=m +no_defs +type=crs"
[INFO]    Calculating cropping area and generating bounds shapefile from point cloud
[INFO]    running pdal translate -i "/var/www/data/782885c2-b560-4bde-9689-8f08139ca019/odm_georeferencing/odm_georeferenced_model.laz" -o "/var/www/data/782885c2-b560-4bde-9689-8f08139ca019/odm_georeferencing/odm_georeferenced_model.decimated.las" decimation --filters.decimation.step=40
[INFO]    running pdal info --boundary --filters.hexbin.edge_size=1 --filters.hexbin.threshold=0 "/var/www/data/782885c2-b560-4bde-9689-8f08139ca019/odm_georeferencing/odm_georeferenced_model.decimated.las" > "/var/www/data/782885c2-b560-4bde-9689-8f08139ca019/odm_georeferencing/odm_georeferenced_model.boundary.json"
[INFO]    running pdal info --summary "/var/www/data/782885c2-b560-4bde-9689-8f08139ca019/odm_georeferencing/odm_georeferenced_model.laz" > "/var/www/data/782885c2-b560-4bde-9689-8f08139ca019/odm_georeferencing/odm_georeferenced_model.summary.json"
[INFO]    running ogr2ogr -overwrite -f GPKG -a_srs "+proj=utm +zone=51 +datum=WGS84 +units=m +no_defs" /var/www/data/782885c2-b560-4bde-9689-8f08139ca019/odm_georeferencing/odm_georeferenced_model.bounds.gpkg /var/www/data/782885c2-b560-4bde-9689-8f08139ca019/odm_georeferencing/odm_georeferenced_model.bounds.geojson
[INFO]    Creating Entwine Point Tile output
[INFO]    running entwine build --threads 32 --tmp "/var/www/data/782885c2-b560-4bde-9689-8f08139ca019/entwine_pointcloud-tmp" -i /var/www/data/782885c2-b560-4bde-9689-8f08139ca019/odm_georeferencing/odm_georeferenced_model.laz -o "/var/www/data/782885c2-b560-4bde-9689-8f08139ca019/entwine_pointcloud"
1/1: /var/www/data/782885c2-b560-4bde-9689-8f08139ca019/odm_georeferencing/odm_georeferenced_model.laz
Dimensions: [
X:int32, Y:int32, Z:int32, Intensity:uint16, ReturnNumber:uint8,
NumberOfReturns:uint8, ScanDirectionFlag:uint8, EdgeOfFlightLine:uint8,
Classification:uint8, ScanAngleRank:float32, UserData:uint8,
PointSourceId:uint16, GpsTime:float64, Red:uint16, Green:uint16, Blue:uint16
]
Points: 14,710,854
Bounds: [(269159, 3097277, -27), (270265, 3098368, 109)]
Scale: 0.001
SRS: EPSG:32651

Adding 0 - /var/www/data/782885c2-b560-4bde-9689-8f08139ca019/odm_georeferencing/odm_georeferenced_model.laz
Joining
00:10 - 57% - 8,364,032 - 3,011 (3,011) M/h - 0W - 0R - 186A
Done 0
Saving
Wrote 14,710,854 points.
[INFO]    Finished odm_georeferencing stage
[INFO]    Running odm_dem stage
[WARNING] Maximum resolution set to 1.0 * (GSD - 10.0%) (5.04 cm / pixel, requested resolution was 5.00 cm / pixel)
[INFO]    Create DSM: False
[INFO]    Create DTM: False
[INFO]    DEM input file /var/www/data/782885c2-b560-4bde-9689-8f08139ca019/odm_georeferencing/odm_georeferenced_model.laz found: True
[WARNING] DEM will not be generated
[INFO]    Finished odm_dem stage
[INFO]    Running odm_orthophoto stage
[WARNING] Maximum resolution set to 1.0 * (GSD - 10.0%) (5.04 cm / pixel, requested resolution was 5.00 cm / pixel)
[INFO]    Creating GeoTIFF
[INFO]    running "/code/SuperBuild/install/bin/odm_orthophoto" -inputFiles /var/www/data/782885c2-b560-4bde-9689-8f08139ca019/odm_texturing/odm_textured_model_geo.obj -logFile "/var/www/data/782885c2-b560-4bde-9689-8f08139ca019/odm_orthophoto/odm_orthophoto_log.txt" -outputFile "/var/www/data/782885c2-b560-4bde-9689-8f08139ca019/odm_orthophoto/odm_orthophoto.tif" -resolution 19.83629470202584 -verbose -outputCornerFile "/var/www/data/782885c2-b560-4bde-9689-8f08139ca019/odm_orthophoto/odm_orthophoto_corners.txt"   -inpaintThreshold 1.0 -utm_north_offset 3097756.0 -utm_east_offset 269825.0 -a_srs "+proj=utm +zone=51 +datum=WGS84 +units=m +no_defs +type=crs" -co TILED=YES -co COMPRESS=DEFLATE -co PREDICTOR=2 -co BIGTIFF=IF_SAFER -co BLOCKXSIZE=512 -co BLOCKYSIZE=512 -co NUM_THREADS=32 --config GDAL_CACHEMAX 5582589952.0
Inpaint threshold was set to: 1

Reading mesh file... /var/www/data/782885c2-b560-4bde-9689-8f08139ca019/odm_texturing/odm_textured_model_geo.obj
Found material0000
Loading odm_textured_model_geo_material0000_map_Kd.png
Found material0001
Loading odm_textured_model_geo_material0001_map_Kd.png
Found material0002
Loading odm_textured_model_geo_material0002_map_Kd.png
Found material0003
Loading odm_textured_model_geo_material0003_map_Kd.png
Found material0004
Loading odm_textured_model_geo_material0004_map_Kd.png
Found material0005
Loading odm_textured_model_geo_material0005_map_Kd.png
Found material0006
Loading odm_textured_model_geo_material0006_map_Kd.png
Found material0007
Loading odm_textured_model_geo_material0007_map_Kd.png
Found material0008
Loading odm_textured_model_geo_material0008_map_Kd.png
Found material0009
Loading odm_textured_model_geo_material0009_map_Kd.png
Found material0010
Loading odm_textured_model_geo_material0010_map_Kd.png
Found material0011
Loading odm_textured_model_geo_material0011_map_Kd.png
Found material0012
Loading odm_textured_model_geo_material0012_map_Kd.png
Found material0013
Loading odm_textured_model_geo_material0013_map_Kd.png
Found material0014
Loading odm_textured_model_geo_material0014_map_Kd.png
Found material0015
Loading odm_textured_model_geo_material0015_map_Kd.png
Found material0016
Loading odm_textured_model_geo_material0016_map_Kd.png
Found material0017
Loading odm_textured_model_geo_material0017_map_Kd.png
Found material0018
Loading odm_textured_model_geo_material0018_map_Kd.png
Found material0019
Loading odm_textured_model_geo_material0019_map_Kd.png
Found material0020
Loading odm_textured_model_geo_material0020_map_Kd.png
Found material0021
Loading odm_textured_model_geo_material0021_map_Kd.png
Found material0022
Loading odm_textured_model_geo_material0022_map_Kd.png
Found material0023
Loading odm_textured_model_geo_material0023_map_Kd.png
Found material0024
Loading odm_textured_model_geo_material0024_map_Kd.png
Found material0025
Loading odm_textured_model_geo_material0025_map_Kd.png
Found material0026
Loading odm_textured_model_geo_material0026_map_Kd.png
Found material0027
Loading odm_textured_model_geo_material0027_map_Kd.png
Found material0028
Loading odm_textured_model_geo_material0028_map_Kd.png
Found material0029
Loading odm_textured_model_geo_material0029_map_Kd.png
Found material0030
Loading odm_textured_model_geo_material0030_map_Kd.png
Found material0031
Loading odm_textured_model_geo_material0031_map_Kd.png
Found material0032
Loading odm_textured_model_geo_material0032_map_Kd.png
Found material0033
Loading odm_textured_model_geo_material0033_map_Kd.png
Found material0034
Loading odm_textured_model_geo_material0034_map_Kd.png
Found material0035
Loading odm_textured_model_geo_material0035_map_Kd.png
Found material0036
Loading odm_textured_model_geo_material0036_map_Kd.png
Found material0037
Loading odm_textured_model_geo_material0037_map_Kd.png
Found material0038
Loading odm_textured_model_geo_material0038_map_Kd.png
Found material0039
Loading odm_textured_model_geo_material0039_map_Kd.png
Found material0040
Loading odm_textured_model_geo_material0040_map_Kd.png
Found material0041
Loading odm_textured_model_geo_material0041_map_Kd.png
Found material0042
Loading odm_textured_model_geo_material0042_map_Kd.png
Found material0043
Loading odm_textured_model_geo_material0043_map_Kd.png
Found material0044
Loading odm_textured_model_geo_material0044_map_Kd.png
Found material0045
Loading odm_textured_model_geo_material0045_map_Kd.png
Found material0046
Loading odm_textured_model_geo_material0046_map_Kd.png
Found material0047
Loading odm_textured_model_geo_material0047_map_Kd.png
Found material0048
Loading odm_textured_model_geo_material0048_map_Kd.png
Found material0049
Loading odm_textured_model_geo_material0049_map_Kd.png
Found material0050
Loading odm_textured_model_geo_material0050_map_Kd.png
Found material0051
Loading odm_textured_model_geo_material0051_map_Kd.png
Found material0052
Loading odm_textured_model_geo_material0052_map_Kd.png
Found material0053
Loading odm_textured_model_geo_material0053_map_Kd.png
Found material0054
Loading odm_textured_model_geo_material0054_map_Kd.png
Found material0055
Loading odm_textured_model_geo_material0055_map_Kd.png
Found material0056
Loading odm_textured_model_geo_material0056_map_Kd.png
Found material0057
Loading odm_textured_model_geo_material0057_map_Kd.png
Found material0058
Loading odm_textured_model_geo_material0058_map_Kd.png
Found material0059
Loading odm_textured_model_geo_material0059_map_Kd.png
Found material0060
Loading odm_textured_model_geo_material0060_map_Kd.png
Found material0061
Loading odm_textured_model_geo_material0061_map_Kd.png
Found material0062
Loading odm_textured_model_geo_material0062_map_Kd.png
Found material0063
Loading odm_textured_model_geo_material0063_map_Kd.png
Found material0064
Loading odm_textured_model_geo_material0064_map_Kd.png
Found material0065
Loading odm_textured_model_geo_material0065_map_Kd.png
Found material0066
Loading odm_textured_model_geo_material0066_map_Kd.png
Found material0067
Loading odm_textured_model_geo_material0067_map_Kd.png
Found material0068
Loading odm_textured_model_geo_material0068_map_Kd.png
Found material0069
Loading odm_textured_model_geo_material0069_map_Kd.png
Found material0070
Loading odm_textured_model_geo_material0070_map_Kd.png
Found material0071
Loading odm_textured_model_geo_material0071_map_Kd.png
Found material0072
Loading odm_textured_model_geo_material0072_map_Kd.png
Found material0073
Loading odm_textured_model_geo_material0073_map_Kd.png
Found material0074
Loading odm_textured_model_geo_material0074_map_Kd.png
Mesh file read.

Boundary points:
Point 1: -645.228 -395.006
Point 2: -645.228 572.281
Point 3: 412.954 572.281
Point 4: 412.954 -395.006

Model bounds x : -645.228 -> 412.954
Model bounds y : -395.006 -> 572.281
Model area : 1.02357e+06m2
Model resolution, width x height : 20991x19188
Translating and scaling mesh...
Rendering the ortho photo...
Texture channels: 3
Texture depth: 8bit
Rendering material0000 ... done
Rendering material0001 ... done
Rendering material0002 ... done
Rendering material0003 ... done
Rendering material0004 ... done
Rendering material0005 ... done
Rendering material0006 ... done
Rendering material0007 ... done
Rendering material0008 ... done
Rendering material0009 ... done
Rendering material0010 ... done
Rendering material0011 ... done
Rendering material0012 ... done
Rendering material0013 ... done
Rendering material0014 ... done
Rendering material0015 ... done
Rendering material0016 ... done
Rendering material0017 ... done
Rendering material0018 ... done
Rendering material0019 ... done
Rendering material0020 ... done
Rendering material0021 ... done
Rendering material0022 ... done
Rendering material0023 ... done
Rendering material0024 ... done
Rendering material0025 ... done
Rendering material0026 ... done
Rendering material0027 ... done
Rendering material0028 ... done
Rendering material0029 ... done
Rendering material0030 ... done
Rendering material0031 ... done
Rendering material0032 ... done
Rendering material0033 ... done
Rendering material0034 ... done
Rendering material0035 ... done
Rendering material0036 ... done
Rendering material0037 ... done
Rendering material0038 ... done
Rendering material0039 ... done
Rendering material0040 ... done
Rendering material0041 ... done
Rendering material0042 ... done
Rendering material0043 ... done
Rendering material0044 ... done
Rendering material0045 ... done
Rendering material0046 ... done
Rendering material0047 ... done
Rendering material0048 ... done
Rendering material0049 ... done
Rendering material0050 ... done
Rendering material0051 ... done
Rendering material0052 ... done
Rendering material0053 ... done
Rendering material0054 ... done
Rendering material0055 ... done
Rendering material0056 ... done
Rendering material0057 ... done
Rendering material0058 ... done
Rendering material0059 ... done
Rendering material0060 ... done
Rendering material0061 ... done
Rendering material0062 ... done
Rendering material0063 ... done
Rendering material0064 ... done
Rendering material0065 ... done
Rendering material0066 ... done
Rendering material0067 ... done
Rendering material0068 ... done
Rendering material0069 ... done
Rendering material0070 ... done
Rendering material0071 ... done
Rendering material0072 ... done
Rendering material0073 ... done
Rendering material0074 ... done
... model rendered

Edge inpainting
Block [(7168, 8192), (16384, 17408)]
Block [(16384, 17408), (4096, 5120)]
Block [(10240, 11264), (12288, 13312)]
Block [(4096, 5120), (4096, 5120)]
Block [(12288, 13312), (0, 1024)]
Block [(0, 1024), (3072, 4096)]
Block [(2048, 3072), (8192, 9216)]
Block [(11264, 12288), (7168, 8192)]
Block [(12288, 13312), (3072, 4096)]
Block [(9216, 10240), (6144, 7168)]
Block [(19456, 20991), (11264, 12288)]
Block [(8192, 9216), (2048, 3072)]
Block [(17408, 18432), (10240, 11264)]
Block [(6144, 7168), (10240, 11264)]
Block [(4096, 5120), (1024, 2048)]
Block [(15360, 16384), (9216, 10240)]
Block [(1024, 2048), (13312, 14336)]
Block [(9216, 10240), (17408, 19188)]
Block [(18432, 19456), (5120, 6144)]
Block [(16384, 17408), (15360, 16384)]
Block [(7168, 8192), (5120, 6144)]
Block [(16384, 17408), (1024, 2048)]
Block [(14336, 15360), (14336, 15360)]
Block [(0, 1024), (0, 1024)]
Block [(3072, 4096), (14336, 15360)]
Block [(18432, 19456), (16384, 17408)]
Block [(13312, 14336), (8192, 9216)]
Block [(12288, 13312), (13312, 14336)]
Block [(8192, 9216), (11264, 12288)]
Block [(0, 1024), (7168, 8192)]
Block [(4096, 5120), (9216, 10240)]
Block [(5120, 6144), (15360, 16384)]
Block [(2048, 3072), (13312, 14336)]
Block [(13312, 14336), (0, 1024)]
Block [(6144, 7168), (15360, 16384)]
Block [(9216, 10240), (11264, 12288)]
Block [(1024, 2048), (0, 1024)]
Block [(1024, 2048), (3072, 4096)]
Block [(9216, 10240), (2048, 3072)]
Block [(15360, 16384), (14336, 15360)]
Block [(19456, 20991), (16384, 17408)]
Block [(5120, 6144), (1024, 2048)]
Block [(8192, 9216), (16384, 17408)]
Block [(2048, 3072), (3072, 4096)]
Block [(17408, 18432), (15360, 16384)]
Block [(14336, 15360), (0, 1024)]
Block [(2048, 3072), (0, 1024)]
Block [(7168, 8192), (10240, 11264)]
Block [(7168, 8192), (15360, 16384)]
Block [(16384, 17408), (14336, 15360)]
Block [(4096, 5120), (14336, 15360)]
Block [(10240, 11264), (6144, 7168)]
Block [(6144, 7168), (1024, 2048)]
Block [(1024, 2048), (7168, 8192)]
Block [(10240, 11264), (11264, 12288)]
Block [(18432, 19456), (10240, 11264)]
Block [(15360, 16384), (0, 1024)]
Block [(3072, 4096), (0, 1024)]
Block [(3072, 4096), (3072, 4096)]
Block [(9216, 10240), (16384, 17408)]
Block [(0, 1024), (12288, 13312)]
Block [(7168, 8192), (1024, 2048)]
Block [(8192, 9216), (5120, 6144)]
Block [(0, 1024), (17408, 19188)]
Block [(16384, 17408), (0, 1024)]
Block [(18432, 19456), (15360, 16384)]
Block [(4096, 5120), (0, 1024)]
Block [(5120, 6144), (9216, 10240)]
Block [(10240, 11264), (2048, 3072)]
Block [(11264, 12288), (6144, 7168)]
Block [(17408, 18432), (14336, 15360)]
Block [(17408, 18432), (0, 1024)]
Block [(8192, 9216), (1024, 2048)]
Block [(8192, 9216), (10240, 11264)]
Block [(1024, 2048), (12288, 13312)]
Block [(5120, 6144), (14336, 15360)]
Block [(16384, 17408), (9216, 10240)]
Block [(5120, 6144), (0, 1024)]
Block [(18432, 19456), (0, 1024)]
Block [(9216, 10240), (1024, 2048)]
Block [(11264, 12288), (11264, 12288)]
Block [(19456, 20991), (15360, 16384)]
Block [(4096, 5120), (3072, 4096)]
Block [(11264, 12288), (12288, 13312)]
Block [(1024, 2048), (17408, 19188)]
Block [(6144, 7168), (0, 1024)]
Block [(6144, 7168), (9216, 10240)]
Block [(12288, 13312), (6144, 7168)]
Block [(9216, 10240), (10240, 11264)]
Block [(3072, 4096), (8192, 9216)]
Block [(5120, 6144), (3072, 4096)]
Block [(19456, 20991), (0, 1024)]
Block [(10240, 11264), (1024, 2048)]
Block [(7168, 8192), (0, 1024)]
Block [(7168, 8192), (9216, 10240)]
Block [(9216, 10240), (5120, 6144)]
Block [(13312, 14336), (13312, 14336)]
Block [(18432, 19456), (14336, 15360)]
Block [(10240, 11264), (17408, 19188)]
Block [(0, 1024), (16384, 17408)]
Block [(6144, 7168), (3072, 4096)]
Block [(10240, 11264), (16384, 17408)]
Block [(3072, 4096), (13312, 14336)]
Block [(17408, 18432), (4096, 5120)]
Block [(2048, 3072), (17408, 19188)]
Block [(8192, 9216), (0, 1024)]
Block [(7168, 8192), (3072, 4096)]
Block [(1024, 2048), (16384, 17408)]
Block [(19456, 20991), (5120, 6144)]
Block [(2048, 3072), (12288, 13312)]
Block [(14336, 15360), (8192, 9216)]
Block [(0, 1024), (1024, 2048)]
Block [(11264, 12288), (16384, 17408)]
Block [(9216, 10240), (0, 1024)]
Block [(4096, 5120), (8192, 9216)]
Block [(10240, 11264), (10240, 11264)]
Block [(8192, 9216), (9216, 10240)]
Block [(12288, 13312), (11264, 12288)]
Block [(8192, 9216), (3072, 4096)]
Block [(2048, 3072), (7168, 8192)]
Block [(19456, 20991), (10240, 11264)]
Block [(12288, 13312), (7168, 8192)]
Block [(11264, 12288), (2048, 3072)]
Block [(5120, 6144), (4096, 5120)]
Block [(1024, 2048), (1024, 2048)]
Block [(11264, 12288), (1024, 2048)]
Block [(2048, 3072), (16384, 17408)]
Block [(17408, 18432), (1024, 2048)]
Block [(9216, 10240), (3072, 4096)]
Block [(3072, 4096), (17408, 19188)]
Block [(12288, 13312), (16384, 17408)]
Block [(5120, 6144), (8192, 9216)]
Block [(12288, 13312), (12288, 13312)]
Block [(0, 1024), (6144, 7168)]
Block [(12288, 13312), (1024, 2048)]
Block [(2048, 3072), (1024, 2048)]
Block [(3072, 4096), (16384, 17408)]
Block [(11264, 12288), (17408, 19188)]
Block [(13312, 14336), (16384, 17408)]
Block [(12288, 13312), (2048, 3072)]
Block [(1024, 2048), (6144, 7168)]
Block [(8192, 9216), (15360, 16384)]
Block [(3072, 4096), (1024, 2048)]
Block [(13312, 14336), (1024, 2048)]
Block [(3072, 4096), (7168, 8192)]
Block [(14336, 15360), (16384, 17408)]
Block [(4096, 5120), (16384, 17408)]
Block [(6144, 7168), (14336, 15360)]
Block [(10240, 11264), (0, 1024)]
Block [(10240, 11264), (3072, 4096)]
Block [(13312, 14336), (6144, 7168)]
Block [(13312, 14336), (2048, 3072)]
Block [(4096, 5120), (17408, 19188)]
Block [(9216, 10240), (9216, 10240)]
Block [(2048, 3072), (6144, 7168)]
Block [(5120, 6144), (16384, 17408)]
Block [(15360, 16384), (16384, 17408)]
Block [(10240, 11264), (5120, 6144)]
Block [(13312, 14336), (11264, 12288)]
Block [(12288, 13312), (17408, 19188)]
Block [(3072, 4096), (12288, 13312)]
Block [(4096, 5120), (13312, 14336)]
Block [(0, 1024), (11264, 12288)]
Block [(17408, 18432), (9216, 10240)]
Block [(6144, 7168), (16384, 17408)]
Block [(13312, 14336), (7168, 8192)]
Block [(5120, 6144), (17408, 19188)]
Block [(14336, 15360), (13312, 14336)]
Block [(6144, 7168), (4096, 5120)]
Block [(16384, 17408), (16384, 17408)]
Block [(5120, 6144), (13312, 14336)]
Block [(14336, 15360), (1024, 2048)]
Block [(19456, 20991), (14336, 15360)]
Block [(13312, 14336), (3072, 4096)]
Block [(9216, 10240), (15360, 16384)]
Block [(18432, 19456), (1024, 2048)]
Block [(13312, 14336), (17408, 19188)]
Block [(18432, 19456), (9216, 10240)]
Block [(6144, 7168), (17408, 19188)]
Block [(18432, 19456), (4096, 5120)]
Block [(6144, 7168), (8192, 9216)]
Block [(0, 1024), (15360, 16384)]
Block [(14336, 15360), (7168, 8192)]
Block [(13312, 14336), (12288, 13312)]
Block [(11264, 12288), (5120, 6144)]
Block [(1024, 2048), (15360, 16384)]
Block [(14336, 15360), (17408, 19188)]
Block [(7168, 8192), (8192, 9216)]
Block [(3072, 4096), (6144, 7168)]
Block [(14336, 15360), (2048, 3072)]
Block [(1024, 2048), (11264, 12288)]
Block [(17408, 18432), (16384, 17408)]
Block [(2048, 3072), (15360, 16384)]
Block [(10240, 11264), (15360, 16384)]
Block [(14336, 15360), (11264, 12288)]
Block [(15360, 16384), (13312, 14336)]
Block [(3072, 4096), (15360, 16384)]
Block [(11264, 12288), (0, 1024)]
Block [(11264, 12288), (15360, 16384)]
Block [(10240, 11264), (9216, 10240)]
Block [(6144, 7168), (13312, 14336)]
Block [(16384, 17408), (13312, 14336)]
Block [(4096, 5120), (15360, 16384)]
Block [(14336, 15360), (6144, 7168)]
Block [(7168, 8192), (4096, 5120)]
Block [(15360, 16384), (17408, 19188)]
Block [(15360, 16384), (1024, 2048)]
Block [(12288, 13312), (15360, 16384)]
Block [(8192, 9216), (8192, 9216)]
Block [(7168, 8192), (17408, 19188)]
Block [(2048, 3072), (11264, 12288)]
Block [(14336, 15360), (3072, 4096)]
Block [(4096, 5120), (6144, 7168)]
Block [(13312, 14336), (15360, 16384)]
Block [(17408, 18432), (13312, 14336)]
Block [(12288, 13312), (5120, 6144)]
Block [(4096, 5120), (7168, 8192)]
Block [(5120, 6144), (6144, 7168)]
Block [(14336, 15360), (15360, 16384)]
Block [(19456, 20991), (1024, 2048)]
Block [(9216, 10240), (8192, 9216)]
Block [(11264, 12288), (3072, 4096)]
Block [(15360, 16384), (8192, 9216)]
Block [(4096, 5120), (12288, 13312)]
Block [(15360, 16384), (2048, 3072)]
Block [(16384, 17408), (17408, 19188)]
Block [(15360, 16384), (15360, 16384)]
Block [(15360, 16384), (6144, 7168)]
Block [(10240, 11264), (8192, 9216)]
Block [(15360, 16384), (3072, 4096)]
Block [(19456, 20991), (9216, 10240)]
Block [(17408, 18432), (17408, 19188)]
Block [(18432, 19456), (13312, 14336)]
Block [(5120, 6144), (7168, 8192)]
Block [(8192, 9216), (4096, 5120)]
Block [(11264, 12288), (9216, 10240)]
Block [(13312, 14336), (5120, 6144)]
Block [(3072, 4096), (11264, 12288)]
Block [(16384, 17408), (6144, 7168)]
Block [(11264, 12288), (10240, 11264)]
Block [(15360, 16384), (11264, 12288)]
Block [(14336, 15360), (12288, 13312)]
Block [(6144, 7168), (6144, 7168)]
Block [(9216, 10240), (4096, 5120)]
Block [(19456, 20991), (4096, 5120)]
Block [(18432, 19456), (17408, 19188)]
Block [(4096, 5120), (11264, 12288)]
Block [(11264, 12288), (8192, 9216)]
Block [(7168, 8192), (14336, 15360)]
Block [(16384, 17408), (2048, 3072)]
Block [(19456, 20991), (17408, 19188)]
Block [(0, 1024), (5120, 6144)]
Block [(12288, 13312), (8192, 9216)]
Block [(0, 1024), (10240, 11264)]
Block [(12288, 13312), (9216, 10240)]
Block [(7168, 8192), (13312, 14336)]
Block [(1024, 2048), (5120, 6144)]
Block [(5120, 6144), (11264, 12288)]
Block [(10240, 11264), (4096, 5120)]
Block [(5120, 6144), (12288, 13312)]
Block [(2048, 3072), (5120, 6144)]
Block [(6144, 7168), (7168, 8192)]
Block [(11264, 12288), (4096, 5120)]
Block [(7168, 8192), (6144, 7168)]
Block [(15360, 16384), (7168, 8192)]
Block [(6144, 7168), (11264, 12288)]
Block [(17408, 18432), (6144, 7168)]
Block [(16384, 17408), (8192, 9216)]
Block [(3072, 4096), (5120, 6144)]
Block [(7168, 8192), (11264, 12288)]
Block [(1024, 2048), (10240, 11264)]
Block [(8192, 9216), (6144, 7168)]
Block [(7168, 8192), (7168, 8192)]
Block [(15360, 16384), (12288, 13312)]
Block [(8192, 9216), (7168, 8192)]
Block [(13312, 14336), (9216, 10240)]
Block [(6144, 7168), (12288, 13312)]
Block [(9216, 10240), (7168, 8192)]
Block [(12288, 13312), (4096, 5120)]
Block [(10240, 11264), (7168, 8192)]
Block [(8192, 9216), (14336, 15360)]
Block [(17408, 18432), (2048, 3072)]
Block [(8192, 9216), (13312, 14336)]
Block [(16384, 17408), (11264, 12288)]
Block [(16384, 17408), (3072, 4096)]
Block [(8192, 9216), (17408, 19188)]
Block [(13312, 14336), (4096, 5120)]
Block [(7168, 8192), (12288, 13312)]
Block [(14336, 15360), (5120, 6144)]
Block [(2048, 3072), (10240, 11264)]
Block [(18432, 19456), (2048, 3072)]
Block [(18432, 19456), (6144, 7168)]
Block [(16384, 17408), (12288, 13312)]
Block [(12288, 13312), (10240, 11264)]
Block [(19456, 20991), (13312, 14336)]
Block [(0, 1024), (14336, 15360)]
Block [(0, 1024), (2048, 3072)]
Block [(3072, 4096), (10240, 11264)]
Block [(1024, 2048), (14336, 15360)]
Block [(1024, 2048), (2048, 3072)]
Block [(16384, 17408), (7168, 8192)]
Block [(14336, 15360), (9216, 10240)]
Block [(2048, 3072), (2048, 3072)]
Block [(2048, 3072), (14336, 15360)]
Block [(13312, 14336), (10240, 11264)]
Block [(17408, 18432), (11264, 12288)]
Block [(8192, 9216), (12288, 13312)]
Block [(3072, 4096), (2048, 3072)]
Block [(19456, 20991), (6144, 7168)]
Block [(17408, 18432), (12288, 13312)]
Block [(4096, 5120), (10240, 11264)]
Block [(4096, 5120), (2048, 3072)]
Block [(19456, 20991), (2048, 3072)]
Block [(5120, 6144), (10240, 11264)]
Block [(18432, 19456), (12288, 13312)]
Block [(5120, 6144), (2048, 3072)]
Block [(9216, 10240), (12288, 13312)]
Block [(19456, 20991), (12288, 13312)]
Block [(6144, 7168), (2048, 3072)]
Block [(7168, 8192), (2048, 3072)]
Block [(9216, 10240), (13312, 14336)]
Block [(0, 1024), (13312, 14336)]
Block [(15360, 16384), (5120, 6144)]
Block [(18432, 19456), (11264, 12288)]
Block [(14336, 15360), (10240, 11264)]
Block [(17408, 18432), (8192, 9216)]
Block [(14336, 15360), (4096, 5120)]
Block [(9216, 10240), (14336, 15360)]
Block [(17408, 18432), (3072, 4096)]
Block [(15360, 16384), (10240, 11264)]
Block [(18432, 19456), (3072, 4096)]
Block [(10240, 11264), (13312, 14336)]
Block [(17408, 18432), (7168, 8192)]
Block [(19456, 20991), (3072, 4096)]
Block [(4096, 5120), (5120, 6144)]
Block [(0, 1024), (4096, 5120)]
Block [(1024, 2048), (4096, 5120)]
Block [(11264, 12288), (13312, 14336)]
Block [(2048, 3072), (4096, 5120)]
Block [(5120, 6144), (5120, 6144)]
Block [(3072, 4096), (4096, 5120)]
Block [(16384, 17408), (5120, 6144)]
Block [(18432, 19456), (7168, 8192)]
Block [(10240, 11264), (14336, 15360)]
Block [(19456, 20991), (7168, 8192)]
Block [(0, 1024), (8192, 9216)]
Block [(1024, 2048), (8192, 9216)]
Block [(16384, 17408), (10240, 11264)]
Block [(18432, 19456), (8192, 9216)]
Block [(17408, 18432), (5120, 6144)]
Block [(15360, 16384), (4096, 5120)]
Block [(6144, 7168), (5120, 6144)]
Block [(19456, 20991), (8192, 9216)]
Block [(0, 1024), (9216, 10240)]
Block [(11264, 12288), (14336, 15360)]
Block [(12288, 13312), (14336, 15360)]
Block [(13312, 14336), (14336, 15360)]
Block [(1024, 2048), (9216, 10240)]
Block [(2048, 3072), (9216, 10240)]
Block [(3072, 4096), (9216, 10240)]

Writing ortho photo to /var/www/data/782885c2-b560-4bde-9689-8f08139ca019/odm_orthophoto/odm_orthophoto.tif
Set GDAL_CACHEMAX to 5582589952
Set TILED=YES
Set COMPRESS=DEFLATE
Set PREDICTOR=2
Set BIGTIFF=IF_SAFER
Set BLOCKXSIZE=512
Set BLOCKYSIZE=512
Set NUM_THREADS=32
Writing corner coordinates to /var/www/data/782885c2-b560-4bde-9689-8f08139ca019/odm_orthophoto/odm_orthophoto_corners.txt
Orthophoto generation done.
[INFO]    Cropping /var/www/data/782885c2-b560-4bde-9689-8f08139ca019/odm_orthophoto/odm_orthophoto.tif
[INFO]    running gdalwarp -cutline /var/www/data/782885c2-b560-4bde-9689-8f08139ca019/odm_georeferencing/odm_georeferenced_model.bounds.gpkg -crop_to_cutline -co TILED=YES -co COMPRESS=DEFLATE -co PREDICTOR=2 -co BIGTIFF=IF_SAFER -co BLOCKXSIZE=512 -co BLOCKYSIZE=512 -co NUM_THREADS=32 -dstalpha /var/www/data/782885c2-b560-4bde-9689-8f08139ca019/odm_orthophoto/odm_orthophoto.original.tif /var/www/data/782885c2-b560-4bde-9689-8f08139ca019/odm_orthophoto/odm_orthophoto.tif --config GDAL_CACHEMAX 37.95%
Using band 4 of source image as alpha.
Creating output file that is 20554P x 17967L.
Processing /var/www/data/782885c2-b560-4bde-9689-8f08139ca019/odm_orthophoto/odm_orthophoto.original.tif [1/1] : 0...10...20...30...40...50...60...70...80...90...100 - done.
[INFO]    Optimizing /var/www/data/782885c2-b560-4bde-9689-8f08139ca019/odm_orthophoto/odm_orthophoto.tif as Cloud Optimized GeoTIFF
[INFO]    running gdal_translate -of COG -co NUM_THREADS=32 -co BLOCKSIZE=256 -co COMPRESS=DEFLATE -co PREDICTOR=2 -co BIGTIFF=IF_SAFER -co RESAMPLING=NEAREST --config GDAL_CACHEMAX 37.8% --config GDAL_NUM_THREADS 32 "/var/www/data/782885c2-b560-4bde-9689-8f08139ca019/odm_orthophoto/odm_orthophoto.tif" "/var/www/data/782885c2-b560-4bde-9689-8f08139ca019/odm_orthophoto/odm_orthophoto_cogeo.tif"
Input file size is 20554, 17967
0...10...20...30...40...50...60...70...80...90...100 - done.
[INFO]    Wrote /var/www/data/782885c2-b560-4bde-9689-8f08139ca019/odm_orthophoto/odm_orthophoto_extent.dxf
[INFO]    Wrote /var/www/data/782885c2-b560-4bde-9689-8f08139ca019/odm_orthophoto/odm_orthophoto.tfw
[INFO]    Finished odm_orthophoto stage
[INFO]    Running odm_report stage
[INFO]    Exporting shots.geojson
[INFO]    Wrote /var/www/data/782885c2-b560-4bde-9689-8f08139ca019/odm_report/shots.geojson
[INFO]    Copied /var/www/data/782885c2-b560-4bde-9689-8f08139ca019/opensfm/camera_mappings.npz --> /var/www/data/782885c2-b560-4bde-9689-8f08139ca019/odm_report/camera_mappings.npz
[INFO]    running pdal info --dimensions "X,Y,Z" "/var/www/data/782885c2-b560-4bde-9689-8f08139ca019/odm_georeferencing/odm_georeferenced_model.laz" > "/var/www/data/782885c2-b560-4bde-9689-8f08139ca019/odm_georeferencing/odm_georeferenced_model.info.json"
[INFO]    running pdal translate -i "/var/www/data/782885c2-b560-4bde-9689-8f08139ca019/odm_georeferencing/odm_georeferenced_model.laz" -o "/var/www/data/782885c2-b560-4bde-9689-8f08139ca019/opensfm/stats/overlap.tif" --writer gdal --writers.gdal.resolution=0.7890492857142817 --writers.gdal.data_type=uint8_t --writers.gdal.dimension=UserData --writers.gdal.output_type=max --writers.gdal.radius=1.1158842012379404
[INFO]    Cropping /var/www/data/782885c2-b560-4bde-9689-8f08139ca019/opensfm/stats/overlap.tif
[INFO]    running gdalwarp -cutline /var/www/data/782885c2-b560-4bde-9689-8f08139ca019/odm_georeferencing/odm_georeferenced_model.bounds.gpkg -crop_to_cutline -co TILED=YES -co COMPRESS=DEFLATE -co PREDICTOR=2 -co BIGTIFF=IF_SAFER -co BLOCKXSIZE=512 -co BLOCKYSIZE=512 -co NUM_THREADS=32  /var/www/data/782885c2-b560-4bde-9689-8f08139ca019/opensfm/stats/overlap.original.tif /var/www/data/782885c2-b560-4bde-9689-8f08139ca019/opensfm/stats/overlap.tif --config GDAL_CACHEMAX 37.55%
Creating output file that is 1312P x 1147L.
Processing /var/www/data/782885c2-b560-4bde-9689-8f08139ca019/opensfm/stats/overlap.original.tif [1/1] : 0Using internal nodata values (e.g. 255) for image /var/www/data/782885c2-b560-4bde-9689-8f08139ca019/opensfm/stats/overlap.original.tif.
Copying nodata values from source /var/www/data/782885c2-b560-4bde-9689-8f08139ca019/opensfm/stats/overlap.original.tif to destination /var/www/data/782885c2-b560-4bde-9689-8f08139ca019/opensfm/stats/overlap.tif.
...10...20...30...40...50...60...70...80...90...100 - done.
[INFO]    running gdaldem color-relief "/var/www/data/782885c2-b560-4bde-9689-8f08139ca019/opensfm/stats/overlap.tif" "/code/opendm/report/overlap_color_map.txt" "/var/www/data/782885c2-b560-4bde-9689-8f08139ca019/opensfm/stats/overlap.png" -of PNG -alpha
0...10...20...30...40...50...60...70...80...90...100 - done.
[INFO]    running gdal_translate -of png "/var/www/data/782885c2-b560-4bde-9689-8f08139ca019/odm_orthophoto/odm_orthophoto.tif" "/var/www/data/782885c2-b560-4bde-9689-8f08139ca019/opensfm/stats/ortho.png" -b 1 -b 2 -b 3 -b 4 -outsize 1400 0 -co WORLDFILE=YES --config GDAL_CACHEMAX 37.55%
Input file size is 20554, 17967
0...10...20...30...40...50...60...70...80...90...100 - done.
[INFO]    Exporting report to /var/www/data/782885c2-b560-4bde-9689-8f08139ca019/odm_report/report.pdf
[INFO]    Finished odm_report stage
[INFO]    Running odm_postprocess stage
[INFO]    Post Processing
[INFO]    Adding TIFFTAGs to /var/www/data/782885c2-b560-4bde-9689-8f08139ca019/odm_orthophoto/odm_orthophoto.tif
[INFO]    Finished odm_postprocess stage
[INFO]    No more stages to run
[INFO]    MMMMMMMMMMMNNNMMMMMMMMMMMMMMMMMMMMMMMMMMMMMMMMMMMMNNNMMMMMMMMMMM
[INFO]    MMMMMMdo:..---../sNMMMMMMMMMMMMMMMMMMMMMMMMMMNs/..---..:odMMMMMM
[INFO]    MMMMy-.odNMMMMMNy/`/mMMMMMMMMMMMMMMMMMMMMMMm/`/hNMMMMMNdo.-yMMMM
[INFO]    MMN/`sMMMMMMMMMNNMm/`yMMMMMMMMMMMMMMMMMMMMy`/mMNNMMMMMMMMNs`/MMM
[INFO]    MM/ hMMMMMMMMNs.+MMM/ dMMMMMMMMMMMMMMMMMMh +MMM+.sNMMMMMMMMh +MM
[INFO]    MN /MMMMMMNo/./mMMMMN :MMMMMMMMMMMMMMMMMM: NMMMMm/./oNMMMMMM: NM
[INFO]    Mm +MMMMMN+ `/MMMMMMM`-MMMMMMMMMMMMMMMMMM-`MMMMMMM:` oNMMMMM+ mM
[INFO]    MM..NMMNs./mNMMMMMMMy sMMMMMMMMMMMMMMMMMMo hMMMMMMMNm/.sNMMN`-MM
[INFO]    MMd`:mMNomMMMMMMMMMy`:MMMMMMMNmmmmNMMMMMMN:`hMMMMMMMMMdoNMm-`dMM
[INFO]    MMMm:.omMMMMMMMMNh/  sdmmho/.`..`-``-/sddh+  /hNMMMMMMMMdo.:mMMM
[INFO]    MMMMMd+--/osss+:-:/`  ```:- .ym+ hmo``:-`   `+:-:ossso/-:+dMMMMM
[INFO]    MMMMMMMNmhysosydmNMo   /ds`/NMM+ hMMd..dh.  sMNmdysosyhmNMMMMMMM
[INFO]    MMMMMMMMMMMMMMMMMMMs .:-:``hmmN+ yNmds -:.:`-NMMMMMMMMMMMMMMMMMM
[INFO]    MMMMMMMMMMMMMMMMMMN.-mNm- //:::. -:://: +mMd`-NMMMMMMMMMMMMMMMMM
[INFO]    MMMMMMMMMMMMMMMMMM+ dMMN -MMNNN+ yNNNMN :MMMs sMMMMMMMMMMMMMMMMM
[INFO]    MMMMMMMMMMMMMMMMMM`.mmmy /mmmmm/ smmmmm``mmmh :MMMMMMMMMMMMMMMMM
[INFO]    MMMMMMMMMMMMMMMMMM``:::- ./////. -:::::` :::: -MMMMMMMMMMMMMMMMM
[INFO]    MMMMMMMMMMMMMMMMMM:`mNNd /NNNNN+ hNNNNN .NNNy +MMMMMMMMMMMMMMMMM
[INFO]    MMMMMMMMMMMMMMMMMMd`/MMM.`ys+//. -/+oso +MMN.`mMMMMMMMMMMMMMMMMM
[INFO]    MMMMMMMMMMMMMMMMMMMy /o:- `oyhd/ shys+ `-:s-`hMMMMMMMMMMMMMMMMMM
[INFO]    MMMMMMMMNmdhhhdmNMMM`  +d+ sMMM+ hMMN:`hh-  sMMNmdhhhdmNMMMMMMMM
[INFO]    MMMMMms:::/++//::+ho    .+- /dM+ hNh- +/`   -h+:://++/::/smMMMMM
[INFO]    MMMN+./hmMMMMMMNds-  ./oso:.``:. :-``.:os+-  -sdNMMMMMMmy:.oNMMM
[INFO]    MMm-.hMNhNMMMMMMMMNo`/MMMMMNdhyyyyhhdNMMMM+`oNMMMMMMMMNhNMh.-mMM
[INFO]    MM:`mMMN/-sNNMMMMMMMo yMMMMMMMMMMMMMMMMMMy sMMMMMMMNNs-/NMMm`:MM
[INFO]    Mm /MMMMMd/.-oMMMMMMN :MMMMMMMMMMMMMMMMMM-`MMMMMMMo-./dMMMMM/ NM
[INFO]    Mm /MMMMMMm:-`sNMMMMN :MMMMMMMMMMMMMMMMMM-`MMMMMNs`-/NMMMMMM/ NM
[INFO]    MM:`mMMMMMMMMd/-sMMMo yMMMMMMMMMMMMMMMMMMy sMMMs-/dMMMMMMMMd`:MM
[INFO]    MMm-.hMMMMMMMMMdhMNo`+MMMMMMMMMMMMMMMMMMMM+`oNMhdMMMMMMMMMh.-mMM
[INFO]    MMMNo./hmNMMMMMNms--yMMMMMMMMMMMMMMMMMMMMMMy--smNMMMMMNmy/.oNMMM
[INFO]    MMMMMms:-:/+++/:-+hMMMMMMMMMMMMMMMMMMMMMMMMMNh+-:/+++/:-:smMMMMM
[INFO]    MMMMMMMMNdhhyhdmMMMMMMMMMMMMMMMMMMMMMMMMMMMMMMMMmdhyhhmNMMMMMMMM
[INFO]    MMMMMMMMMMMMMMMNNNNNMMMMMMNNNNNNMMMMMMMMNNMMMMMMMNNMMMMMMMMMMMMM
[INFO]    MMMMMMMMMMMMMh/-...-+dMMMm......:+hMMMMs../MMMMMo..sMMMMMMMMMMMM
[INFO]    MMMMMMMMMMMM/  /yhy-  sMMm  -hhy/  :NMM+   oMMMy   /MMMMMMMMMMMM
[INFO]    MMMMMMMMMMMy  /MMMMN`  NMm  /MMMMo  +MM: .` yMd``` :MMMMMMMMMMMM
[INFO]    MMMMMMMMMMM+  sMMMMM:  hMm  /MMMMd  -MM- /s `h.`d- -MMMMMMMMMMMM
[INFO]    MMMMMMMMMMMs  +MMMMM.  mMm  /MMMMy  /MM. +M/   yM: `MMMMMMMMMMMM
[INFO]    MMMMMMMMMMMN-  smNm/  +MMm  :NNdo` .mMM` oMM+/yMM/  MMMMMMMMMMMM
[INFO]    MMMMMMMMMMMMNo-    `:yMMMm      `:sNMMM` sMMMMMMM+  NMMMMMMMMMMM
[INFO]    MMMMMMMMMMMMMMMNmmNMMMMMMMNmmmmNMMMMMMMNNMMMMMMMMMNNMMMMMMMMMMMM
[INFO]    ODM app finished - Fri May 30 02:13:05  2025
Postprocessing: /var/www/data/782885c2-b560-4bde-9689-8f08139ca019

Found point cloud: odm_georeferencing/odm_georeferenced_model.laz

Entwine point cloud is already built.

Entwine point cloud is already built.

Postprocessing: done (•̀ᴗ•́)و!
