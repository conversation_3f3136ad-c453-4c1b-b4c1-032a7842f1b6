<template>
  <div id="map-controls" style="display: flex;">
    <div v-if="isSet" style="margin-left: 10px;">
      <el-button v-if="!isTakeoff && !isDrawing" style="width: 90px;" type="primary" @click="setTakeoffPoint">
        设置起飞点
      </el-button>
      <el-button v-if="isTakeoff" style="width: 114px;" type="primary" @click="sureTakeoffPoint">
        起飞点设置完成
      </el-button>
      <el-button v-if="!isTakeoff && !isDrawing" style="width: 110px;" type="primary" @click="startDrawing">
        开始/重新绘制
      </el-button>
      <el-button v-if="isDrawing" style="width: 80px;" type="primary" @click="clearMap">
        取消绘制
      </el-button>
      <el-button v-if="points.length > 1 && isDrawing" style="width: 80px;" type="primary" @click="sureMap">
        确认绘制
      </el-button>
    </div>
  </div>
</template>

<script>
import EventBus from '@/event-bus';
import {ElMessage} from "element-plus";
import {getRoot, getApp} from '@/root';
import {useGMapManage} from '@/hooks/use-g-map';

export default {
  data() {
    return {
      points: [], // 存储点位数据
      markers: [], // 存储起飞点数据
      polyline: null, // 存储折线数据
      polylines: null, // 存储起飞点折线数据
      isDrawing: false, // 绘制状态-true表示结束绘制 false表示开始绘制
      isTakeoff: false, // 设置起飞点状态
      surestatus: false, // 绘制航线状态
      map: null, // 初始化地图图层
      text: null,
      marker: null,
      texts: [],
      texts1: [],
      distance: [],
      overlay: null, // 起飞点图层
      iconPoint: null, // 航线标点图标
    };
  },
  props: {
    zoom: Number,
    center: Array,
    isSet: Boolean,
    placemark: Array,
    take0ffPoint: Array,
  },
  watch: {
    'isSet': {
      handler: function (newVal, oldVal) {
        if (!newVal) {
          this.iconPoint = null
          this.isDrawing = false
        }
      }, deep: true
    },
    'placemark': {
      handler: function (newVal, oldVal) {
        const root = getRoot()
        let map = root.$map
        map.clearMap()
        this.points = this.placemark
        this.detelePoint()
        //移出左侧列表点击上一条航线
        if (this.iconPoint) {
          map.remove(this.iconPoint)
          this.polyline.setMap(null)
          if (this.texts && this.texts.length > 0) {
            this.texts.forEach(i => {
              i.setMap(null);
            });
            this.texts = [];
          }
        }
        //进行航线打点连线展示距离&自适应缩放
        this.addMarkersAndLines(newVal, map)
        this.setZoom(newVal)
      }, deep: true
    },
    'take0ffPoint': {
      handler: function (newVal, oldVal) {
        this.addTakeoffPoint(newVal)
      }, deep: true
    },
  },
  mounted() {
    EventBus.on('lineClose', () => {
      const root = getRoot()
      let map = root.$map
      map.clearMap()
    });
  },
  beforeDestroy() {
    EventBus.off('lineClose');
  },
  methods: {
    // 动态设置地图缩放等级和中心点
    setZoom(val) {
      const root = getRoot()
      let map = root.$map
      let objectCoordinates = val.map(([lng, lat]) => ({
        lng: lng, lat: lat
      }));
      if (objectCoordinates.length > 0) {
        let bounds = this.getBoundingBox(objectCoordinates)
        let center = bounds.getCenter();
        map.setCenter(center);
      }
    },
    // 计算边界框
    getBoundingBox(points) {
      let minLng = Infinity;
      let maxLng = -Infinity;
      let minLat = Infinity;
      let maxLat = -Infinity;
      for (let point of points) {
        if (point.lng < minLng) minLng = point.lng;
        if (point.lng > maxLng) maxLng = point.lng;
        if (point.lat < minLat) minLat = point.lat;
        if (point.lat > maxLat) maxLat = point.lat;
      }
      return new AMap.Bounds([minLng, minLat], [maxLng, maxLat]);
    },
    // 追加飞行航线点位进行连线
    addPoint(position) {
      // console.log('addPoint', this.points)
      const root = getRoot()
      if (this.marker && this.marker.length > 0) {
        for (let i = 0; i < this.marker.length; i++) {
          this.marker[i].setMap(null);
        }
        this.marker = [];
      }
      this.points.push(position);
      // this.points = this.convertArray(this.points)
      this.points = [...new Map(this.points.map(item => [item.lat, item])).values()];
      const newArray = this.points.map(item => ({
        point: `${item.lng},${item.lat}`
      }));
      const arr = newArray.map(pointObj => {
        return pointObj.point.split(',').map(item => parseFloat(item));
      });
      // 添加标记
      this.marker = new AMap.Marker({
        position: position,
        content: `<div class="marker-icon" style="width: 0;height: 0;border-left: 20px solid transparent;border-right: 20px solid transparent;border-top: 40px solid #00D590;color: #fff;position: absolute;right: -20px;bottom: 0;display: flex;justify-content: center;"><div class="content" style="position: absolute;bottom: 14px;">${this.points.length}</div></div>`,
      });
      // 更新点数组和标记数组
      if (this.isDrawing) {
        this.marker.setMap(root.$map);
      }
      // 绘制折线
      this.updatePolyline();
      // 计算并显示各航段的距离
      let distances = [];
      for (let i = 0; i < arr.length - 1; i++) {
        let distance = AMap.GeometryUtil.distance(arr[i], arr[i + 1]);
        distances.push(distance);
      }
      // 在每个航段的中点放置距离标签
      distances.forEach((distance, i) => {
        this.text = new AMap.Text({
          text: distance.toFixed(2) + ' m',
          style: {
            'background-color': '#29b6f6',
            'font-size': '12px',
            'color': '#fff',
          },
          offset: new AMap.Pixel(-20, -20),
          direction: 'center',
          directionGap: 5,
        });
        let midPoint = this.getMidPoints(arr[i], arr[i + 1]);
        this.text.setPosition(midPoint);
        this.text.setMap(root.$map);
        this.texts.push(this.text);
      });
      this.$emit('allPoints', newArray);
    },
    // 修改飞行航线进行绘制
    updatePolyline() {
      const root = getRoot()
      if (this.polyline) {
        // console.log('this.points', this.points)
        let coords = this.points.map(i => new AMap.LngLat(i.lng, i.lat));
        this.polyline.setPath(coords);
      } else {
        this.polyline = new AMap.Polyline({
          path: this.points,
          strokeColor: '#ff0000',
          strokeOpacity: 1,
          strokeWeight: 2,
          strokeStyle: 'solid'
        });
        this.polyline.setMap(root.$map);
      }
    },
    // 开始/重新绘制
    startDrawing() {
      const root = getRoot()
      let map = root.$map
      map.clearMap()
      map.off('click');
      // 绘制权限更换
      this.isDrawing = !this.isDrawing;
      this.surestatus = false
      this.$emit('allPoints', []);
      this.$emit('takeoffPoint', []);
      this.points = [];
      // 清除折线
      if (this.polyline) {
        this.polyline.setMap(null);
        this.polyline = null;
      }
      if (this.isDrawing) {
        map.on('click', (e) => {
          this.addPoint(e.lnglat);
        });
      }
      // 清空地图上所有的图层
      map.remove(this.iconPoint)
      if (this.overlay) map.remove(this.overlay)
      if (this.polylines) this.polylines.setMap(null)
      if (this.texts && this.texts.length > 0) {
        this.texts.forEach(i => {
          i.setMap(null);
        });
        this.texts = [];
      }
      if (this.texts1 && this.texts1.length > 0) {
        this.texts1.forEach(i => {
          i.setMap(null);
        });
        this.texts1 = [];
      }
    },
    // 取消绘制
    clearMap() {
      const root = getRoot()
      let map = root.$map
      map.clearMap()
      // 清除折线
      if (this.polyline) {
        this.polyline.setMap(null);
        this.polyline = null;
      }
      // 清除点数组
      this.points = [];
      this.markers = [];
      this.isTakeoff = false;
      // 清空地图上所有的图层
      if (this.texts && this.texts.length > 0) {
        this.texts.forEach(i => {
          i.setMap(null);
        });
        this.texts = [];
      }
      if (this.marker) {
        this.marker.setMap(null)
        map.remove(this.marker)
        this.marker = null
      }
      ElMessage({
        type: 'success',
        message: '清除成功！',
      })
      this.isDrawing = false;
      this.$emit('allPoints', []);
      this.$emit('takeoffPoint', []);
      // 地图初始化
      const app = getApp();
      useGMapManage().globalPropertiesConfig(app);
    },
    // 确认绘制
    sureMap() {
      this.isDrawing = false
      this.surestatus = false
      this.polyline = new AMap.Polyline({
        path: this.points,
        strokeColor: '#ff0000',
        strokeOpacity: 1,
        strokeWeight: 2,
        strokeStyle: 'solid'
      });
      this.polyline.off("sureDraw");
      this.isTakeoff = false;
    },
    // 设置起飞点
    setTakeoffPoint() {
      const root = getRoot()
      let map = root.$map
      this.isTakeoff = true;
      map.on('click', (e) => {
        if (this.isTakeoff) {
          this.detelePoint();
          this.addTakeoffPoint(e.lnglat);
        }
      });
    },
    // 删除上一个起飞点只保留最新当前
    detelePoint() {
      const root = getRoot()
      let map = root.$map
      if (this.overlay) map.remove(this.overlay);
      if (this.markers.length > 0) {
        this.polylines.setMap(null);
        if (this.texts1 && this.texts1.length > 0) {
          this.texts1.forEach(i => {
            i.setMap(null);
          });
          this.texts1 = [];
        }
      }
    },
    // 新增起飞点
    addTakeoffPoint(position) {
      if (Number.isNaN(position[0])) return
      const root = getRoot()
      let map = root.$map
      this.overlay = new AMap.Marker({
        position: position,
        title: '起飞点',
      })
      this.overlay.setMap(map);
      this.markers = [position.lng, position.lat];
      this.$emit('takeoffPoint', this.markers);
      // 将起飞点与航线第一个点进行连线
      const combinedPoints = [this.points[0], position];
      this.polylines = new AMap.Polyline({
        path: combinedPoints,
        strokeColor: '#48de0d',
        strokeOpacity: 1,
        strokeWeight: 2,
        strokeStyle: 'solid'
      });
      this.polylines.setMap(map);
      let distance = AMap.GeometryUtil.distance(combinedPoints[0], combinedPoints[1]);
      this.text = new AMap.Text({
        text: distance.toFixed(2) + ' m',
        style: {
          'background-color': '#29b6f6',
          'font-size': '12px',
          'color': '#fff',
        },
        offset: new AMap.Pixel(0, 0),
        direction: 'center',
        directionGap: 5,
      });
      let midPoint = this.getMidPoints(combinedPoints[0], combinedPoints[1]);
      this.text.setPosition(midPoint);
      this.texts1.push(this.text);
    },
    // 起飞点设置完成事件
    sureTakeoffPoint() {
      const root = getRoot()
      this.isTakeoff = false;
      root.$map.off("setTakeoffPoint");
    },
    // 飞行航线回显地图
    addMarkersAndLines(val, map) {
      // console.log('val', val)
      const root = getRoot()
      // 创建图标
      this.iconPoint = val.map((item, index) => {
        return new AMap.Marker({
          position: item,
          content: `<div class="marker-icon" style="width: 0;height: 0;border-left: 20px solid transparent;border-right: 20px solid transparent;border-top: 40px solid #00D590;color: #fff;position: absolute;right: -20px;bottom: 0;display: flex;justify-content: center;"><div class="content" style="position: absolute;bottom: 14px;">${index + 1}</div></div>`,
        })
      })
      // 添加标记点到地图
      this.iconPoint.forEach(marker => marker.setMap(map))
      // 计算并显示各航段的距离
      this.distances = [];
      for (let i = 0; i < val.length - 1; i++) {
        let distance = AMap.GeometryUtil.distance(
            val[i],
            val[i + 1]
        );
        this.distances.push(distance);
      }
      root.$map.add(this.iconPoint)
      // 连线各标点
      this.polyline = new AMap.Polyline({
        path: val,
        strokeColor: '#ff0000',
        strokeOpacity: 1,
        strokeWeight: 2,
        strokeStyle: 'solid'
      });
      this.polyline.setMap(map);
      // 在每个航段的中点放置距离标签
      this.distances.forEach((distance, i) => {
        this.text = new AMap.Text({
          text: distance.toFixed(2) + ' m',
          style: {
            'background-color': '#29b6f6',
            'font-size': '12px',
            'color': '#fff',
          },
          offset: new AMap.Pixel(-20, -20),
          direction: 'center',
          directionGap: 5,
        });
        let midPoint = this.getMidPoints(val[i], val[i + 1]);
        this.text.setPosition(midPoint);
        this.text.setMap(root.$map);
        this.texts.push(this.text);
      });
    },
    // 计算航线两点之间中心点
    getMidPoints(pointA, pointB) {
      // 点A和点B的经纬度坐标
      let lat1 = pointA[1];
      let lon1 = pointA[0];
      let lat2 = pointB[1];
      let lon2 = pointB[0];
      // 将经纬度从十进制转换为弧度
      lat1 = lat1 * Math.PI / 180;
      lon1 = lon1 * Math.PI / 180;
      lat2 = lat2 * Math.PI / 180;
      lon2 = lon2 * Math.PI / 180;
      // 计算中心点的经纬度（弧度）
      let midLat = (lat1 + lat2) / 2;
      let midLon = (lon1 + lon2) / 2;
      // 将结果从弧度转换回十进制
      midLat = midLat * 180 / Math.PI;
      midLon = midLon * 180 / Math.PI;
      return [midLon, midLat];
    },
    convertArray(arr) {
      return arr.map(item => {
        if (Array.isArray(item)) {
          if (Array.isArray(item[0])) {
          }
          if (item.length === 2 && typeof item[0] === 'number' && typeof item[1] === 'number') {
            return item;
          }
        }
        if (typeof item === 'object' && item !== null && 'lat' in item && 'lng' in item) {
          return [item.lat, item.lng];
        }
      })
    },
  }
}
</script>

<style scoped lang="scss">
#map-controls {
  position: fixed;
  top: 10px;
  left: 350px;
  z-index: 1;
}

button {
  font-size: 14px;
  cursor: pointer;
}
</style>