<template>
  <div id="app" style="width: 100%">
    <div class="page-header">
      <!-- <div class="page-title">
        <div>云端录像</div>
      </div> -->

      <div class="page-header-btn">
        设备名称:
        <el-select
            size="mini"
            @change="getRecordList"
            style="margin-right: 1rem;width: 240px;"
            v-model="search"
            clearable
            placeholder="请选择设备名称"
        >
          <el-option v-for="(item, index) in queryData" :key="index"
                     :label="item.name" :value="item.deviceId">
          </el-option>
        </el-select>
        开始时间:
        <el-date-picker
            v-model="startTime"
            type="datetime"
            value-format='YYYY-MM-DD HH:mm:ss'
            @change="getRecordList"
            placeholder="选择日期时间"
        >
        </el-date-picker>
        结束时间:
        <el-date-picker
            v-model="endTime"
            type="datetime"
            value-format='YYYY-MM-DD HH:mm:ss'
            @change="getRecordList"
            placeholder="选择日期时间"
        >
        </el-date-picker>
        节点选择:
        <el-select
            size="mini"
            @change="getRecordList"
            style="width: 16rem; margin-right: 1rem"
            v-model="mediaServerId"
            placeholder="请选择"
        >
          <el-option label="全部" value=""></el-option>
          <el-option
              v-for="item in mediaServerList"
              :key="item.id"
              :label="item.id"
              :value="item.id"
          >
          </el-option>
        </el-select>
        <el-button size="mini" icon="el-icon-delete" type="danger" @click="deleteRecord()" v-permission>批量删除</el-button>
        <el-button
            icon="el-icon-refresh-right"
            circle
            size="mini"
            :loading="loading"
            @click="getRecordList()"
        ></el-button>
      </div>
    </div>
    <!--设备列表-->
    <el-table :data="recordList" style="width: 100%" :height="winHeight" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55"></el-table-column>
      <el-table-column prop="app" label="应用名"></el-table-column>
      <el-table-column prop="deviceName" label="设备名称"></el-table-column>
      <el-table-column prop="fileSizeStr" label="文件大小"></el-table-column>
      <el-table-column prop="stream" label="流ID" width="380"></el-table-column>
      <el-table-column label="开始时间">
        <template #default="scope">
          {{ formatTimeStamp(scope.row.startTime) }}
        </template>
      </el-table-column>
      <el-table-column label="结束时间">
        <template #default="scope">
          {{ formatTimeStamp(scope.row.endTime) }}
        </template>
      </el-table-column>
      <el-table-column label="时长">
        <template #default="scope">
          <el-tag>{{ formatTime(scope.row.timeLen) }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="fileName" label="文件名称"></el-table-column>
      <el-table-column prop="mediaServerId" label="流媒体"></el-table-column>
      <el-table-column label="操作" width="200" fixed="right">
        <template #default="scope">
          <el-button size="medium" icon="el-icon-video-play" type="text" @click="play(scope.row)">播放
          </el-button>
          <el-button size="medium" icon="el-icon-download" type="text" @click="downloadFile(scope.row)">下载
          </el-button>
          <el-button size="medium" icon="el-icon-delete" type="text" style="color: #f56c6c" @click="deleteRecord(scope.row)" v-permission>删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-pagination style="float: right"
                   @size-change="handleSizeChange"
                   @current-change="currentChange"
                   :current-page="currentPage"
                   :page-size="count"
                   :page-sizes="[15, 25, 35, 50]"
                   layout="total, sizes, prev, pager, next"
                   :total="total"
    >
    </el-pagination>

    <el-dialog :title="playerTitle" v-model="showPlayer" width="50%" :close-on-click-modal="false" :before-close="handleCancel">
      <!--<easyPlayer ref="recordVideoPlayer" :videoUrl="videoUrl" :height="false"></easyPlayer>-->
      <video v-if="videoUrl" width="100%" height="500" controls preload="auto" ref="videoPlayer" @loadedmetadata="playVideo">
        <source :src="videoUrl" type="video/mp4">
      </video>
      <div v-if="videoUrl" style="display: flex;justify-content: space-between;width: 150px;">
        <button @click="changePlaybackRate(1)">1x</button>
        <button @click="changePlaybackRate(2)">2x</button>
        <button @click="changePlaybackRate(3)">3x</button>
        <button @click="changePlaybackRate(5)">5x</button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import axios from 'axios';
import moment from 'moment';
import {Delete, getList} from "@/api/video";
import {ElMessage} from "element-plus";
import uiHeader from './layout/UiHeader.vue';
import MediaServer from './service/MediaServer.js';
import easyPlayer from './common/easyPlayer.vue';

export default {
  components: {
    uiHeader,
    easyPlayer,
  },
  data() {
    return {
      search: '',
      startTime: '',
      endTime: '',
      showPlayer: false,
      playerTitle: '',
      videoUrl: '',
      playerStyle: {
        margin: 'auto',
        'margin-bottom': '20px',
        width: window.innerWidth / 2 + 'px',
        height: this.winHeight / 2 + 'px',
      },
      ids: [], // 选中的id
      mediaServerList: [], // 滅体节点列表
      mediaServerId: '', // 媒体服务
      mediaServerPath: null, // 媒体服务地址
      recordList: [], // 设备列表
      chooseRecord: null, // 媒体服务
      updateLooper: 0, //数据刷新轮训标志
      winHeight: window.innerHeight - 250,
      currentPage: 1,
      count: 15,
      total: 0,
      loading: false,
      mediaServerObj: new MediaServer(),
      queryData: [], // 关键字列表
    };
  },
  mounted() {
    this.initData();
  },
  destroyed() {
    this.$destroy('recordVideoPlayer');
  },
  methods: {
    playVideo(event) {
      const video = event.target;
      video.play();
    },
    changePlaybackRate(rate) {
      this.$refs.videoPlayer.playbackRate = rate;
    },
    initData: function () {
      // 获取媒体节点列表
      this.getMediaServerList();
      this.getRecordList();
      this.$axios({
        method: 'get',
        url: `/api/hztech-mediaServer/api/device/query/devices`,
        params: {
          page: 1,
          count: 100,
        },
      }).then(res => {
        this.queryData = res.data.data.list
      })
    },
    currentChange: function (val) {
      this.currentPage = val;
      this.getRecordList();
    },
    handleSizeChange: function (val) {
      this.count = val;
      this.getRecordList();
    },
    getMediaServerList: function () {
      let that = this;
      that.mediaServerObj.getOnlineMediaServerList(data => {
        that.mediaServerList = data.data;
      });
    },
    handleCancel: function () {
      this.resetVideo();
      this.videoUrl = '';
      this.showPlayer = false;
    },
    // 重置播放倍速和进度
    resetVideo: function () {
      this.$refs.videoPlayer.currentTime = 0;
      this.$refs.videoPlayer.playbackRate = 1.0;
    },
    setMediaServerPath: function (serverId) {
      let that = this;
      let i;
      for (i = 0; i < that.mediaServerList.length; i++) {
        if (serverId === that.mediaServerList[i].id) {
          break;
        }
      }
      let port = that.mediaServerList[i].httpPort;
      if (location.protocol === 'https:' && that.mediaServerList[i].httpSSlPort) {
        port = that.mediaServerList[i].httpSSlPort;
      }
      that.mediaServerPath = location.protocol + '//' + that.mediaServerList[i].streamIp + ':' + port;
    },
    getRecordList() {
      let params = {
        app: '',
        stream: '',
        query: this.search,
        startTime: this.startTime,
        endTime: this.endTime,
        mediaServerId: this.mediaServerId,
        page: this.currentPage,
        count: this.count,
      }
      getList(params).then(res => {
        if (res.data.code === 0) {
          this.total = res.data.data.total;
          this.recordList = res.data.data.list;
        }
        this.loading = false;
      })
    },
    play(row) {
      this.videoUrl = '';
      this.chooseRecord = row;
      this.$axios({
        method: 'get',
        url: `/api/hztech-mediaServer/api/cloud/record/play/path`,
        params: {
          recordId: row.id,
        },
      }).then(res => {
        if (res.data.code === 0) {
          this.showPlayer = true;
          if (document.location.href.includes('https')) {
            // 正式
            this.videoUrl = res.data.data.httpsPath;
          } else {
            // 本地
            this.videoUrl = res.data.data.httpPath;
          }
        }
      })
    },
    getFileBasePath(item) {
      let basePath = '';
      if (axios.defaults.baseURL.startsWith('http')) {
        basePath = `${axios.defaults.baseURL}/record_proxy/${item.mediaServerId}`;
      } else {
        basePath = `${window.location.origin}${axios.defaults.baseURL}/record_proxy/${item.mediaServerId}`;
      }
      return basePath;
    },
    handleSelectionChange(val) {
      this.ids = val.map(item => item.id); // 获取选中行的ID
    },
    deleteRecord(row) {
      let that = this;
      let ids = null
      if (row) {
        ids = row.id
      } else {
        if (that.ids.length > 0) {
          ids = that.ids.join(',')
        } else {
          ElMessage({
            message: '请选择要删除的数据！',
            type: 'error',
          });
        }
      }
      this.$confirm('是否确定删除数据?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        Delete(ids).then(res => {
          if (res.data.code === 0) {
            ElMessage({
              message: '删除成功！',
              type: 'success',
            });
            this.initData();
          }
        })
      })
    },
    formatTime(time) {
      const h = parseInt(time / 3600 / 1000);
      const minute = parseInt((time - h * 3600 * 1000) / 60 / 1000);
      let second = Math.ceil((time - h * 3600 * 1000 - minute * 60 * 1000) / 1000);
      if (second < 0) {
        second = 0;
      }
      return (
          (h > 0 ? h + `小时` : '') +
          (minute > 0 ? minute + '分' : '') +
          (second > 0 ? second + '秒' : '')
      );
    },
    formatTimeStamp(time) {
      return moment.unix(time / 1000).format('yyyy-MM-DD HH:mm:ss');
    },
    downloadFile(file) {
      this.$axios({
        method: 'get',
        url: `/api/hztech-mediaServer/api/cloud/record/play/path`,
        params: {
          recordId: file.id,
        }
      }).then((res) => {
        const link = document.createElement('a');
        link.target = "_blank";
        if (res.data.code === 0) {
          if (location.protocol === "https:") {
            link.href = res.data.data.httpsPath + "&save_name=" + file.fileName;
          } else {
            link.href = res.data.data.httpPath + "&save_name=" + file.fileName;
          }
          link.click();
        }
      })
    }
  }
}
</script>