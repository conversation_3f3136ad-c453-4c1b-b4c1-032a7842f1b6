<template>
  <basic-container>
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      :permission="permissionList"
      :before-open="beforeOpen"
      v-model="form"
      ref="crud"
      :search.sync="search"
      @row-update="rowUpdate"
      @row-save="rowSave"
      @row-del="rowDel"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
    >
      <template #menuLeft>
        <el-button
          type="warning"
          size="small"
          plain
          icon="el-icon-download"
          @click="handleExport"
        >
          导出
        </el-button>
        <el-button
          type="danger"
          size="small"
          icon="el-icon-delete"
          plain
          v-if="permission.passwordLog_delete"
          @click="handleDelete"
          >删 除
        </el-button>
      </template>
      <template #problemImage="{ row }">
        <el-button
          v-if="row.problemImage"
          type="primary"
          size="small"
          plain
          @click="showImageFun(row.problemImage)"
          >查看图片
        </el-button>
      </template>
    </avue-crud>
    <!-- 图片对话框 -->
    <el-dialog
      title="图片预览"
      v-model="ImagedialogVisible"
      width="50%"
      :append-to-body="true"
    >
      <div>
        <el-image
          v-for="(item, index) in imgList"
          :key="index"
          style="width: 150px; height: 100px; margin-right: 10px"
          :src="item"
          :preview-src-list="imgList"
        >
        </el-image>
      </div>

      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="ImagedialogVisible = false"
          >关闭</el-button
        >
      </span>
    </el-dialog>
  </basic-container>
</template>

<script>
import { getList, add, update, remove } from "@/api/repair/repireRecords";
import NProgress from "nprogress";
import "nprogress/nprogress.css";
import { exportBlob } from "@/api/common";
import { mapGetters } from "vuex";
import { downloadXls } from '@/utils/util';
export default {
  data() {
    return {
      ImagedialogVisible: false,
      imgList: [
        // "https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg",
        // "https://fuss10.elemecdn.com/1/8e/aeffeb4de74e2fde4bd74fc7b4486jpeg.jpeg",
        // "https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg"
      ],
      form: {},
      query: {},
      search: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      option: {
        height: "auto",
        calcHeight: 30,
        tip: false,
        searchShow: true,
        searchMenuSpan: 8,
        border: true,
        index: true,
        viewBtn: true,
        selection: false,
        dialogClickModal: false,
        // menu: false,
        align: "center",
        headerAlign: "center",
        dialogWidth: "30%",
        // searchSpan:5,
        // searchLabelWidth:80,
        column: [
          {
            label: "工单编号",
            span: 24,
            prop: "id",
            type: "input",
            labelWidth: 120,
          },
          {
            label: "工单派发人",
            prop: "sendUser",
            type: "input",
            span: 24,
            labelWidth: 120,
          },
          {
            label: "处理人",
            prop: "handleUser",
            type: "input",
            span: 24,
            labelWidth: 120,
          },
          {
            label: "工单派发时间",
            prop: "createTime",
            type: "input",
            span: 24,
            labelWidth: 120,
          },
          {
            label: "处理时间",
            prop: "handleTime",
            type: "input",
            span: 24,
            labelWidth: 120,
          },
          {
            label: "处理状态",
            prop: "handleStatus",
            type: "select",
            span: 24,
            labelWidth: 120,
            search: true,
            dicUrl: "/api/hztech-system/dict/dictionary?code=handleStatus",
            dataType: "number",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
          },
          {
            label: "说明",
            prop: "problemContent",
            type: "input",
            span: 24,
            labelWidth: 120,
            overHidden: true,
          },
          {
            label: "图片",
            prop: "problemImage",
            width: 160,
            type: "input",
            display: false,
            span: 24,
            labelWidth: 120,
          },
          {
            label: "时间",

            prop: "searchTime",
            search: true,
            type: "datetime",
            searchRange: true,
            searchType: "datetimerange",
            format: "YYYY-MM-DD HH:mm:ss",
            startPlaceholder: "开始时间",
            endPlaceholder: "结束时间",
            valueFormat: "YYYY-MM-DD HH:mm:ss",
            hide: true,
            display: false,
            span: 24,
            labelWidth: 120,
          },
          // {
          //   label: "结束时间",
          //   prop: "handleEndTime",
          //   search: true,
          //   type: "datetime",
          //   format: "yyyy年MM月dd日 HH时mm分ss秒",
          //   valueFormat: "YYYY-MM-DD HH:mm:ss",
          //   hide: true,
          // },
        ],
      },
      data: [
        // {
        //   createTime: "1",
        // },
      ],
    };
  },
  computed: {
    ...mapGetters(["permission"]),
    permissionList() {
      return {
        addBtn: this.validData(this.permission.passwordLog_add, false),
        viewBtn: this.validData(this.permission.passwordLog_view, true),
        delBtn: this.validData(this.permission.passwordLog_delete, false),
        editBtn: this.validData(this.permission.passwordLog_edit, false),
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach((ele) => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
  },
  methods: {
    handleExport() {
      //handleStatus
      //handleStartTime
      //handleEndTime
      console.log("form", this.search);
      var query = this.search;
      var str = "";
      // str = "handleStatus=" + "1";
      if (query.handleStatus) {
        if (!str) {
          str += "handleStatus=" + query.handleStatus;
        } else {
          str += "&handleStatus=" + query.handleStatus;
        }
      }
      if (query.searchTime.length) {
        if (!str) {
          str += "handleStartTime=" + query.searchTime[0];
          str += "&handleEndTime=" + query.searchTime[1];
        } else {
          str += "&handleStartTime=" + query.searchTime[0];
          str += "&handleEndTime=" + query.searchTime[1];
        }
      }
      // if (this.query.handleEndTime) {
      //   if (!str) {
      //     str += "handleEndTime=" + this.query.handleEndTime;
      //   } else {
      //     str += "&handleEndTime=" + this.query.handleEndTime;
      //   }
      // }
      console.log("str", str);
      this.$confirm("是否导出维保工单数据?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        // this.$Export.excel({
        //   title: "工单处理记录",
        //   columns: this.option.column,
        //   data: .data,
        // });
        NProgress.start();
        exportBlob(
          `/api/hztech-park-iot/fireworkproblemreporthandle/export?${str}`
        ).then((res) => {
          downloadXls(res.data, `故障工单数据表.xlsx`);
          NProgress.done();
        });
      });
    },
    showImageFun(res) {
      this.imgList = [];
      if (res) {
        this.imgList = res.split(",");
      }
      setTimeout(() => {
        this.ImagedialogVisible = true;
      }, 10);
    },
    rowSave(row, done, loading) {
      add(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          window.console.log(error);
        }
      );
    },
    rowUpdate(row, index, done, loading) {
      update(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          console.log(error);
        }
      );
    },
    rowDel(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(this.ids);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.$refs.crud.toggleSelection();
        });
    },
    beforeOpen(done, type) {
      // if (["edit", "view"].includes(type)) {
      //   getDetail(this.form.id).then((res) => {
      //     this.form = res.data.data;
      //   });
      // }
      done();
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      var obj = params;
      if (params.searchTime) {
        params.handleStartTime = params.searchTime[0];
        params.handleEndTime = params.searchTime[1];
        delete params.searchTime;
      }
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      this.loading = true;
      getList(
        page.currentPage,
        page.pageSize,
        Object.assign(params, this.query)
      ).then((res) => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    },
  },
};
</script>

<style>
</style>
