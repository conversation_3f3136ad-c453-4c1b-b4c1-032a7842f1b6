<template>
    <el-row style="margin-top: 10px;">
        <el-col :span="1"></el-col>
        <el-col :span="23">航线名称</el-col>
    </el-row>
    <el-row>
        <el-col :span="1"></el-col>
        <el-col :span="23">
            <el-form :model="formData.file_name" :rules="Rules" ref="formRef">
                <el-form-item prop="file_name">
                    <el-input v-model="formData.file_name" size="small" clearable style="width: 260px;"
                        placeholder="请输入航线名称" />
                </el-form-item>
            </el-form>
        </el-col>
    </el-row>
    <el-row>
        <el-col :span="1"></el-col>
        <el-col :span="23">选择镜头</el-col>
    </el-row>
    <el-row>
        <el-col :span="1"></el-col>
        <el-col :span="23">
            <el-checkbox-group size="small" v-model="formData.image_format" @change="image_formatChange">
                <el-checkbox-button v-for="(item, index) in cameraOptions" :key="index" :label="item"
                    :value="item" />
            </el-checkbox-group>
        </el-col>
    </el-row>
    <el-row>
        <el-col :span="1"></el-col>
        <el-col :span="23">采集方式</el-col>
    </el-row>
    <el-row>
        <el-col :span="1"></el-col>
        <el-col :span="23">
            <el-radio-group v-model="formData.template_type" is-button size="small">
                <el-radio-button label="正射采集" value="mapping2d" />
                <el-radio-button label="倾斜采集" value="mapping3d" />
            </el-radio-group>
        </el-col>
    </el-row>
    <el-row>
        <el-col :span="1"></el-col>
        <el-col :span="11">GSD</el-col>
    </el-row>
    <el-row>
        <el-col :span="1"></el-col>
        <el-col :span="22">
            <el-input-number v-model="formData.GSD" size="small" :precision="1" :step="1" :min="1" :max="100" />
            <span style="font-size: 16px;">cm/pixel</span>
        </el-col>
    </el-row>
    <span v-if="formData.template_type === 'mapping3d'">
        <el-row>
            <el-col :span="1"></el-col>
            <el-col :span="11">倾斜 GSD</el-col>
        </el-row>
        <el-row>
            <el-col :span="1"></el-col>
            <el-col :span="22">
                <el-input-number v-model="formData.oblique_gsd" size="small" :precision="1" :step="1" :min="1"
                    :max="100" />
                <span style="font-size: 16px;">cm/pixel</span>
            </el-col>
        </el-row>
    </span>
    <el-row>
        <el-col :span="1"></el-col>
        <el-col :span="11">航线高度模式</el-col>
    </el-row>
    <el-row style="margin-top: 10px;">
        <el-col :span="1"></el-col>
        <el-col :span="23">
            <el-radio-group v-model="formData.height_mode" is-button size="small">
                <el-radio-button label="绝对高度" value="EGM96" />
                <el-radio-button label="相对起飞点高度" value="relativeToStartPoint" />
                <el-radio-button label="相对地形高度" value="aboveGroundLevel" />
            </el-radio-group>

            <el-tooltip effect="dark" placement="top">
                <template #content>
                    绝对高度：航点高度值相对于海平面高度保持不变。<br /><br />
                    相对起飞点高度（ALT）：航点高度值相对起飞点的高度保持不变。<br /><br />
                    相对地形的高度（AGL）：航点高度值相对地形/模型高度保持不变。
                </template>
                <el-icon>
                    <Warning />
                </el-icon>
            </el-tooltip>
        </el-col>
    </el-row>
    <el-row>
        <el-col :span="1"></el-col>
        <el-col :span="22">
            <el-input-number v-model="formData.global_height" size="small" :precision="1" :step="10" :min="25"
                :max="1500" />
            <span style="font-size: 16px;">m</span>
        </el-col>
    </el-row>
    <el-row>
        <el-col :span="1"></el-col>
        <el-col :span="11">安全起飞高度</el-col>
    </el-row>
    <el-row>
        <el-col :span="1"></el-col>
        <el-col :span="22">
            <el-input-number v-model="formData.safe_takeoff_height" size="small" :precision="1" :step="1" :min="2"
                :max="1500" />
            <span style="font-size: 16px;">m</span>
        </el-col>
    </el-row>
    <el-row>
        <el-col :span="1"></el-col>
        <el-col :span="11">全局航线速度</el-col>
    </el-row>
    <el-row>
        <el-col :span="1"></el-col>
        <el-col :span="22">
            <el-input-number v-model="formData.auto_flight_speed" size="small" :precision="1" :step="1" :min="0.1"
                :max="15" />
            <span style="font-size: 16px;">m/s</span>
        </el-col>
    </el-row>
    <el-row>
        <el-col :span="1"></el-col>
        <el-col :span="11">主航线角度</el-col>
        <el-col :span="11">{{ formData.direction }}°</el-col>
    </el-row>
    <el-row>
        <el-col :span="1"></el-col>
        <el-col :span="22">
            <el-slider v-model="formData.direction" :min="0" :max="359" />
        </el-col>
    </el-row>
    <span v-if="formData.template_type === 'mapping3d'">
        <el-row>
            <el-col :span="1"></el-col>
            <el-col :span="11">云台俯仰角度</el-col>
            <el-col :span="11">{{ formData.inclined_gimbal_pitch }}°</el-col>
        </el-row>
        <el-row>
            <el-col :span="1"></el-col>
            <el-col :span="22">
                <el-slider v-model="formData.inclined_gimbal_pitch" :min="-85" :max="-40" />
            </el-col>
        </el-row>
    </span>
    <el-row>
        <el-col :span="1"></el-col>
        <el-col :span="11">高程优化</el-col>
    </el-row>
    <el-row>
        <el-col :span="1"></el-col>
        <el-col :span="22">
            <el-switch v-model="formData.elevation_optimize_enable" :active-value="1" :inactive-value="0" />
        </el-col>
    </el-row>
    <el-row>
        <el-col :span="1"></el-col>
        <el-col :span="4.5">完成动作</el-col>
        <el-tooltip effect="dark" placement="top">
            <template #content>
                自动返航：飞行器航线任务完成后，立即飞向起飞点。若飞行器此时处于失联状态或<br />飞向起飞点的过程中飞行器失联，则立即执行失联行为。<br /><br />
                返回航线起始点悬停：飞行器航线任务完成后，立即飞向起始点（S点），若飞行器<br />此时处于失联状态或飞向起始点（S点）的过程中飞行器失联，则立即执行失联行为。<br /><br />
                退出航线模式：飞行器航线任务完成后，立即退出航线模式，并悬停在原点。若飞行<br />器此时处于失联状态，则立即执行失联行为。
                <!--原地降落：飞行器航线任务完成后，立即开始降落。若飞行器此时处于失联状态或在<br/>降落过程中失联，则立即执行失联行为。-->
            </template>
            <el-icon>
                <Warning />
            </el-icon>
        </el-tooltip>
    </el-row>
    <el-row>
        <el-col :span="1"></el-col>
        <el-col :span="22">
            <el-select v-model="formData.completeTheAction" style="width: 250px;">
                <el-option v-for="(item, index) in completeTheActionOptions" :key="index" :label="item.label"
                    :value="item.value" />
            </el-select>
        </el-col>
    </el-row>
    <el-row>
        <el-col :span="1"></el-col>
        <el-col :span="11">起飞速度</el-col>
    </el-row>
    <el-row>
        <el-col :span="1"></el-col>
        <el-col :span="22">
            <el-input-number v-model="formData.takeoff_speed" size="small" :precision="1" :step="1" :min="1"
                :max="15" />
            <span style="font-size: 16px;">m/s</span>
        </el-col>
    </el-row>
    <el-row>
        <el-col :span="1"></el-col>
        <el-col :span="11">旁向重叠率</el-col>
    </el-row>
    <el-row>
        <el-col :span="1"></el-col>
        <el-col :span="22">
            <el-input-number v-model="formData.overlap.ortho_camera_overlap_w" size="small" :precision="1" :step="1"
                :min="10" :max="90" />
            <span style="font-size: 16px;">%</span>
        </el-col>
    </el-row>
    <el-row v-if="formData.template_type === 'mapping3d'">
        <el-col :span="1"></el-col>
        <el-col :span="11">旁向重叠率（倾斜）</el-col>
    </el-row>
    <el-row v-if="formData.template_type === 'mapping3d'">
        <el-col :span="1"></el-col>
        <el-col :span="22">
            <el-input-number v-model="formData.overlap.inclined_camera_overlap_w" size="small" :precision="1" :step="1"
                :min="10" :max="90" />
            <span style="font-size: 16px;">%</span>
        </el-col>
    </el-row>
    <el-row>
        <el-col :span="1"></el-col>
        <el-col :span="11">航向重叠率</el-col>
    </el-row>
    <el-row>
        <el-col :span="1"></el-col>
        <el-col :span="22">
            <el-input-number v-model="formData.overlap.ortho_camera_overlap_h" size="small" :precision="1" :step="1"
                :min="10" :max="90" />
            <span style="font-size: 16px;">%</span>
        </el-col>
    </el-row>
    <el-row v-if="formData.template_type === 'mapping3d'">
        <el-col :span="1"></el-col>
        <el-col :span="11">航向重叠率（倾斜）</el-col>
    </el-row>
    <el-row v-if="formData.template_type === 'mapping3d'">
        <el-col :span="1"></el-col>
        <el-col :span="22">
            <el-input-number v-model="formData.overlap.inclined_camera_overlap_h" size="small" :precision="1" :step="1"
                :min="10" :max="90" />
            <span style="font-size: 16px;">%</span>
        </el-col>
    </el-row>
    <el-row>
        <el-col :span="1"></el-col>
        <el-col :span="11">边距</el-col>
    </el-row>
    <el-row>
        <el-col :span="1"></el-col>
        <el-col :span="22">
            <el-input-number v-model="formData.margin" size="small" :precision="1" :step="1" :min="0" :max="100" />
            <span style="font-size: 16px;">m</span>
        </el-col>
    </el-row>
    <el-row>
        <el-col :span="1"></el-col>
        <el-col :span="11">拍照模式</el-col>
    </el-row>
    <el-row>
        <el-col :span="1"></el-col>
        <el-col :span="22">
            <el-select v-model="formData.shoot_type" style="width: 250px;">
                <el-option v-for="(item, index) in shootTypeOptions" :key="index" :label="item.label"
                    :value="item.value" />
            </el-select>
        </el-col>
    </el-row>
    <!-- <el-row v-if="formData.template_type === 'mapping3d'">
        <el-col :span="1"></el-col>
        <el-col :span="11">云台摆动角度</el-col>
        <el-col :span="11">{{ formData.gimbal_yaw_angle }}°</el-col>
    </el-row>
    <el-row v-if="formData.template_type === 'mapping3d'">
        <el-col :span="1"></el-col>
        <el-col :span="22">
            <el-slider v-model="formData.gimbal_yaw_angle" :min="10" :max="45" />
        </el-col>
    </el-row> -->
    <span v-if="formData.template_type === 'mapping3d'">
        <el-row>
            <el-col :span="1"></el-col>
            <el-col :span="11">航线速度(倾斜)</el-col>
        </el-row>
        <el-row>
            <el-col :span="1"></el-col>
            <el-col :span="22">
                <el-input-number v-model="formData.inclined_flight_speed" size="small" :precision="1" :step="1" :min="0.1"
                    :max="12" />
                <span style="font-size: 16px;">m/s</span>
            </el-col>
        </el-row>
    </span>
</template>

<script setup>
import { ref, reactive, watch } from 'vue';
import {
    matching, convert, converts, destinationTypeOptions, yawAngleModeOptions, haywireTypeOptions,
    pitchAngleModeOptions, completeTheActionOptions, isImplementRouteOptions, photoTypeSelect,
} from '@/utils/common';

// 拍照模式选项
const shootTypeOptions = [
    { label: '等距间隔拍照', value: 'distance' },
    { label: '等时间隔拍照', value: 'time' },
];

const props = defineProps({
    formData: {
        type: Object,
        required: false,
        default: () => ({})
    },
    cameraOptions: {
        type: Array,
        required: false,
        default: () => []
    }
});
console.log(props.cameraOptions);

const formData = ref(props.formData);

const Rules = ref({
    file_name: [
        { required: true, message: '请输入航线名称', trigger: 'blur' }
    ]
});

// 镜头设置变更处理函数
const image_formatChange = (val) => {
    console.log('镜头设置已更改:', val);
};

const emit = defineEmits(['update:formData']);

watch(() => props.formData, (newVal) => {
    emit('update:formData', newVal);
}, { deep: true });
</script>

<style lang="scss" scoped></style>