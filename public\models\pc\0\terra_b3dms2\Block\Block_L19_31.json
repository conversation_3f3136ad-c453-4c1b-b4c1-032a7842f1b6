{"asset": {"gltfUpAxis": "Z", "version": "0.0"}, "geometricError": 0.14637410640716553, "root": {"boundingVolume": {"box": [-20.80292510986328, -1.1013507843017578, -35.009681701660156, 7.0615997314453125, 0.0, 0.0, 0.0, 7.301668167114258, 0.0, 0.0, 0.0, 2.893239974975586]}, "children": [{"boundingVolume": {"box": [-20.80292510986328, -4.752184867858887, -33.90602111816406, 7.0615997314453125, 0.0, 0.0, 0.0, 3.650834083557129, 0.0, 0.0, 0.0, 1.7895755767822266]}, "children": [{"boundingVolume": {"box": [-24.333724975585938, -4.752184867858887, -34.202178955078125, 3.5307998657226562, 0.0, 0.0, 0.0, 3.650834083557129, 0.0, 0.0, 0.0, 1.4934139251708984]}, "children": [{"boundingVolume": {"box": [-24.333724975585938, -6.577601909637451, -34.227256774902344, 3.5307998657226562, 0.0, 0.0, 0.0, 1.8254170417785645, 0.0, 0.0, 0.0, 1.4678630828857422]}, "children": [{"boundingVolume": {"box": [-24.333724975585938, -6.577601909637451, -34.2273063659668, 3.5307998657226562, 0.0, 0.0, 0.0, 1.8254170417785645, 0.0, 0.0, 0.0, 1.4586296081542969]}, "content": {"uri": "Block_L23_188.b3dm"}, "geometricError": 0.008959338068962097, "refine": "REPLACE"}], "content": {"uri": "Block_L22_202.b3dm"}, "geometricError": 0.01791756972670555, "refine": "REPLACE"}, {"boundingVolume": {"box": [-24.333724975585938, -2.9267678260803223, -34.202178955078125, 3.5307998657226562, 0.0, 0.0, 0.0, 1.8254170417785645, 0.0, 0.0, 0.0, 1.4934139251708984]}, "children": [{"boundingVolume": {"box": [-24.333724975585938, -2.9267678260803223, -34.20295333862305, 3.5307998657226562, 0.0, 0.0, 0.0, 1.8254170417785645, 0.0, 0.0, 0.0, 1.4926414489746094]}, "content": {"uri": "Block_L23_187.b3dm"}, "geometricError": 0.00906546600162983, "refine": "REPLACE"}], "content": {"uri": "Block_L22_201.b3dm"}, "geometricError": 0.018130850046873093, "refine": "REPLACE"}], "content": {"uri": "Block_L21_129.b3dm"}, "geometricError": 0.036073584109544754, "refine": "REPLACE"}, {"boundingVolume": {"box": [-17.272125244140625, -4.752184867858887, -33.892608642578125, 3.5307998657226562, 0.0, 0.0, 0.0, 3.650834083557129, 0.0, 0.0, 0.0, 1.7761650085449219]}, "children": [{"boundingVolume": {"box": [-17.272125244140625, -6.577601909637451, -33.889217376708984, 3.5307998657226562, 0.0, 0.0, 0.0, 1.8254170417785645, 0.0, 0.0, 0.0, 1.7727737426757812]}, "children": [{"boundingVolume": {"box": [-17.272125244140625, -6.577601909637451, -33.889217376708984, 3.5307998657226562, 0.0, 0.0, 0.0, 1.8254170417785645, 0.0, 0.0, 0.0, 1.7727737426757812]}, "content": {"uri": "Block_L23_186.b3dm"}, "geometricError": 0.009005235508084297, "refine": "REPLACE"}], "content": {"uri": "Block_L22_200.b3dm"}, "geometricError": 0.018013209104537964, "refine": "REPLACE"}, {"boundingVolume": {"box": [-17.272125244140625, -2.9267678260803223, -34.90300750732422, 3.5307998657226562, 0.0, 0.0, 0.0, 1.8254170417785645, 0.0, 0.0, 0.0, 0.7534713745117188]}, "children": [{"boundingVolume": {"box": [-17.272125244140625, -2.9267678260803223, -34.90300750732422, 3.5307998657226562, 0.0, 0.0, 0.0, 1.8254170417785645, 0.0, 0.0, 0.0, 0.7534713745117188]}, "content": {"uri": "Block_L23_185.b3dm"}, "geometricError": 0.009080607444047928, "refine": "REPLACE"}], "content": {"uri": "Block_L22_199.b3dm"}, "geometricError": 0.018165186047554016, "refine": "REPLACE"}], "content": {"uri": "Block_L21_128.b3dm"}, "geometricError": 0.036181315779685974, "refine": "REPLACE"}], "content": {"uri": "Block_L20_66.b3dm"}, "geometricError": 0.07226407527923584, "refine": "REPLACE"}, {"boundingVolume": {"box": [-20.80292510986328, 2.549483299255371, -36.0208625793457, 7.0615997314453125, 0.0, 0.0, 0.0, 3.650834083557129, 0.0, 0.0, 0.0, 1.8820610046386719]}, "children": [{"boundingVolume": {"box": [-24.333724975585938, 2.549483299255371, -36.0208625793457, 3.5307998657226562, 0.0, 0.0, 0.0, 3.650834083557129, 0.0, 0.0, 0.0, 1.8820610046386719]}, "children": [{"boundingVolume": {"box": [-24.333724975585938, 2.549483299255371, -36.02238464355469, 3.5307998657226562, 0.0, 0.0, 0.0, 3.650834083557129, 0.0, 0.0, 0.0, 1.8805389404296875]}, "children": [{"boundingVolume": {"box": [-24.333724975585938, 0.4198300838470459, -34.93778991699219, 3.5307998657226562, 0.0, 0.0, 0.0, 1.5211808681488037, 0.0, 0.0, 0.0, 0.7914257049560547]}, "content": {"uri": "Block_L23_184.b3dm"}, "geometricError": 0.009076523594558239, "refine": "REPLACE"}, {"boundingVolume": {"box": [-24.333724975585938, 4.070664405822754, -36.36149597167969, 3.5307998657226562, 0.0, 0.0, 0.0, 2.129653215408325, 0.0, 0.0, 0.0, 1.5414257049560547]}, "content": {"uri": "Block_L23_183.b3dm"}, "geometricError": 0.009606875479221344, "refine": "REPLACE"}], "content": {"uri": "Block_L22_198.b3dm"}, "geometricError": 0.018640900030732155, "refine": "REPLACE"}], "content": {"uri": "Block_L21_127.b3dm"}, "geometricError": 0.037300121039152145, "refine": "REPLACE"}, {"boundingVolume": {"box": [-17.272125244140625, 2.549483299255371, -36.02041244506836, 3.5307998657226562, 0.0, 0.0, 0.0, 3.650834083557129, 0.0, 0.0, 0.0, 1.8070182800292969]}, "children": [{"boundingVolume": {"box": [-17.272125244140625, 2.549483299255371, -36.020355224609375, 3.5307998657226562, 0.0, 0.0, 0.0, 3.650834083557129, 0.0, 0.0, 0.0, 1.8069591522216797]}, "children": [{"boundingVolume": {"box": [-17.272125244140625, 0.4198300838470459, -35.42439270019531, 3.5307998657226562, 0.0, 0.0, 0.0, 1.5211808681488037, 0.0, 0.0, 0.0, 1.2109966278076172]}, "content": {"uri": "Block_L23_182.b3dm"}, "geometricError": 0.009093292988836765, "refine": "REPLACE"}, {"boundingVolume": {"box": [-17.272125244140625, 4.070664405822754, -36.36335754394531, 3.5307998657226562, 0.0, 0.0, 0.0, 2.129653215408325, 0.0, 0.0, 0.0, 1.4639530181884766]}, "content": {"uri": "Block_L23_181.b3dm"}, "geometricError": 0.009678930975496769, "refine": "REPLACE"}], "content": {"uri": "Block_L22_197.b3dm"}, "geometricError": 0.018704598769545555, "refine": "REPLACE"}], "content": {"uri": "Block_L21_126.b3dm"}, "geometricError": 0.03741615638136864, "refine": "REPLACE"}], "content": {"uri": "Block_L20_65.b3dm"}, "geometricError": 0.07467731833457947, "refine": "REPLACE"}], "content": {"uri": "Block_L19_31.b3dm"}, "geometricError": 0.14637410640716553, "refine": "REPLACE"}}