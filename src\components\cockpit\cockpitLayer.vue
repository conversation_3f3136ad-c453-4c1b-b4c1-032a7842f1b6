<template>
    <div class="layer-content">
        <!-- 组件模板内容 -->
        <div class="d-flex">
            <!-- <a-tooltip title="卫星图">
                <div class="block Satellite" :class="{ active: activedLayer === 'Satellite' }"
                    @click="setLayers('Satellite')"></div>
            </a-tooltip>
            <a-tooltip title="标准图">
                <div class="block normal" :class="{ active: activedLayer === 'Standard' }"
                    @click="setLayers('Standard')"></div>
            </a-tooltip> -->
            <a-tooltip title="测距工具">
                <a-checkbox v-model:checked="isRuler" @change="showRuler">测距工具</a-checkbox>
            </a-tooltip>
            <a-tooltip title="镜头跟随">
                <a-checkbox v-model:checked="isCameraFollow" @change="toggleCameraFollow">镜头跟随</a-checkbox>
            </a-tooltip>
            <!-- <a-tooltip title="是否显示方向控制器">
                <a-checkbox v-model:checked="isDirection" @change="showDirection">是否显示方向控制器</a-checkbox>
            </a-tooltip> -->
        </div>
    </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount } from 'vue'
import { getRoot } from '@/root';
import EventBus from '@/event-bus';
import * as Cesium from 'cesium';

// 测距工具相关变量
const rulerEntities = ref([]);
const activeShapePoints = ref([]);
const activeShape = ref(null);
const drawingMode = ref('');
const handler = ref(null);
const mousePosition = ref(null); // 添加鼠标位置变量

onMounted(() => {
    setTimeout(() => {
        const root = getRoot();
        let viewer = root.$cockpitMap; // 使用Cesium实例
        
        // 初始化事件处理器
        handler.value = new Cesium.ScreenSpaceEventHandler(viewer.scene.canvas);
    }, 3000);
});

onBeforeUnmount(() => {
    // 清理事件处理器
    if (handler.value) {
        handler.value.destroy();
    }
    
    // 清理测距实体
    cleanupRulerEntities();
});

// 清理测距实体
const cleanupRulerEntities = () => {
    const root = getRoot();
    let viewer = root.$cockpitMap;
    
    if (!viewer) return;
    
    rulerEntities.value.forEach(entity => {
        viewer.entities.remove(entity);
    });
    rulerEntities.value = [];
    
    if (activeShape.value) {
        viewer.entities.remove(activeShape.value);
        activeShape.value = null;
    }
};

let activedLayer = ref('Satellite');

/**
 * 切换地图图层
 */
const setLayers = (layer) => {
    activedLayer.value = layer;
    const root = getRoot();
    let viewer = root.$cockpitMap;
    
    if (!viewer || !viewer.imageryLayers) return;
    
    // 清除所有图层
    viewer.imageryLayers.removeAll();
    
    // 卫星图层
    if (layer === 'Satellite') {
        // 添加高德卫星图层
        const satelliteLayer = new Cesium.ImageryLayer(new Cesium.UrlTemplateImageryProvider({
            url: 'https://webst02.is.autonavi.com/appmaptile?style=6&x={x}&y={y}&z={z}',
            minimumLevel: 1,
            maximumLevel: 18,
        }));
        
        // 添加标签图层
        const labelLayer = new Cesium.ImageryLayer(new Cesium.UrlTemplateImageryProvider({
            url: 'http://webst02.is.autonavi.com/appmaptile?x={x}&y={y}&z={z}&lang=zh_cn&size=1&scale=1&style=8',
            minimumLevel: 1,
            maximumLevel: 18,
        }));
        
        viewer.imageryLayers.add(satelliteLayer);
        viewer.imageryLayers.add(labelLayer);
    } else {
        // 标准地图图层
        const standardLayer = new Cesium.ImageryLayer(new Cesium.UrlTemplateImageryProvider({
            url: 'https://webrd02.is.autonavi.com/appmaptile?lang=zh_cn&size=1&scale=1&style=8&x={x}&y={y}&z={z}',
            minimumLevel: 1,
            maximumLevel: 18,
        }));
        
        viewer.imageryLayers.add(standardLayer);
    }
};

const isRuler = ref(false);
const isCameraFollow = ref(true); // 默认开启镜头跟随

/**
 * 切换镜头跟随状态
 */
const toggleCameraFollow = () => {
    EventBus.emit('toggle_camera_follow', isCameraFollow.value);
};

// 创建测距点实体
const createPoint = (position, pointType = 'mid') => {
    const root = getRoot();
    let viewer = root.$cockpitMap;
    
    // 根据点的类型设置不同的图片
    let imageUrl;
    switch (pointType) {
        case 'start':
            imageUrl = '/img/start.png';
            break;
        case 'end':
            imageUrl = '/img/end.png';
            break;
        default:
            imageUrl = '/img/mid.png';
            break;
    }
    
    const entity = viewer.entities.add({
        position: position,
        billboard: {
            image: imageUrl,
            scale: 0.7,
            width: 38,
            height: 60,
            verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
            horizontalOrigin: Cesium.HorizontalOrigin.CENTER
        }
    });
    
    rulerEntities.value.push(entity);
    return entity;
};

// 创建测距线实体
const createPolyline = (positions) => {
    const root = getRoot();
    let viewer = root.$cockpitMap;
    
    const entity = viewer.entities.add({
        polyline: {
            positions: positions,
            width: 3,
            material: new Cesium.ColorMaterialProperty(Cesium.Color.BLUE),
        }
    });
    
    rulerEntities.value.push(entity);
    return entity;
};

// 创建距离标签实体
const createDistanceLabel = (position, distance, isLastPoint = false) => {
    const root = getRoot();
    let viewer = root.$cockpitMap;
    
    let labelText = `${(distance / 1000).toFixed(2)} 公里`;
    
    const entity = viewer.entities.add({
        position: position,
        label: {
            text: labelText,
            font: '14px sans-serif',
            fillColor: Cesium.Color.WHITE,
            backgroundColor: Cesium.Color.BLACK,
            showBackground: true,
            style: Cesium.LabelStyle.FILL_AND_OUTLINE,
            outlineWidth: 2,
            verticalOrigin: Cesium.VerticalOrigin.TOP,
            pixelOffset: new Cesium.Cartesian2(0, 5),
        }
    });
    
    // 如果是最后一个点，添加删除按钮
    if (isLastPoint) {
        // 创建一个单独的删除按钮实体
        const deleteButton = viewer.entities.add({
            position: position,
            billboard: {
                image: '/img/delete.png', // 需要准备一个删除图标图片
                scale: 0.8,
                width: 24,
                height: 24,
                verticalOrigin: Cesium.VerticalOrigin.TOP,
                horizontalOrigin: Cesium.HorizontalOrigin.LEFT,
                pixelOffset: new Cesium.Cartesian2(35, 5) // 调整偏移量与标签保持一致
            }
        });
        
        // 标记这是一个删除按钮实体
        deleteButton.isDeleteButton = true;
        rulerEntities.value.push(deleteButton);
    }
    
    rulerEntities.value.push(entity);
    return entity;
};

// 计算两点之间的距离
const computeDistance = (point1, point2) => {
    return Cesium.Cartesian3.distance(point1, point2);
};

// 从屏幕位置获取笛卡尔坐标
const getCartesianFromScreenPosition = (position) => {
    const root = getRoot();
    let viewer = root.$cockpitMap;
    
    let cartesian;
    // 先尝试使用深度拾取
    cartesian = viewer.scene.pickPosition(position);
    
    // 如果深度拾取失败，尝试使用椭球体拾取
    if (!Cesium.defined(cartesian)) {
        const ray = viewer.camera.getPickRay(position);
        cartesian = viewer.scene.globe.pick(ray, viewer.scene);
    }
    
    // 如果椭球体拾取也失败，尝试使用椭球体交点
    if (!Cesium.defined(cartesian)) {
        cartesian = viewer.camera.pickEllipsoid(position, viewer.scene.globe.ellipsoid);
    }
    
    return cartesian;
};

// 在测距模式下更新形状
const updateRulerShape = () => {
    const root = getRoot();
    let viewer = root.$cockpitMap;
    
    if (activeShapePoints.value.length === 0) {
        return;
    }
    
    if (drawingMode.value === 'line') {
        if (activeShape.value) {
            viewer.entities.remove(activeShape.value);
        }
        
        // 使用CallbackProperty动态更新线段位置
        const positionsCallback = new Cesium.CallbackProperty(() => {
            const positions = [...activeShapePoints.value];
            if (mousePosition.value && positions.length > 0) {
                positions.push(mousePosition.value);
            }
            return positions;
        }, false);
        
        activeShape.value = viewer.entities.add({
            polyline: {
                positions: positionsCallback,
                width: 3,
                material: new Cesium.ColorMaterialProperty(Cesium.Color.BLUE.withAlpha(0.7)),
            }
        });
    }
};

// 终止测距绘制操作
const terminateRulerShape = () => {
    const root = getRoot();
    let viewer = root.$cockpitMap;
    
    activeShapePoints.value = [];
    
    if (activeShape.value) {
        viewer.entities.remove(activeShape.value);
        activeShape.value = null;
    }
};

/**
 * 是否显示测距工具
 */
const showRuler = () => {
    const root = getRoot();
    let viewer = root.$cockpitMap;
    
    if (!viewer) return;
    
    // 确保handler已经初始化
    if (!handler.value && viewer.scene && viewer.scene.canvas) {
        handler.value = new Cesium.ScreenSpaceEventHandler(viewer.scene.canvas);
    }
    
    // 再次检查handler是否已初始化
    if (!handler.value) {
        console.warn('测距工具初始化失败，请稍后再试');
        isRuler.value = false; // 重置复选框状态
        return;
    }
    
    if (isRuler.value) {
        // 开启测距模式
        drawingMode.value = 'line';
        
        // 清除现有的测距实体
        cleanupRulerEntities();
        
        // 左键点击事件 - 添加测距点或触发删除按钮
        handler.value.setInputAction((click) => {
            // 先检查是否点击了删除按钮
            const pickedObject = viewer.scene.pick(click.position);
            if (Cesium.defined(pickedObject) && pickedObject.id && pickedObject.id.isDeleteButton) {
                // 点击了删除按钮，清除测距实体
                cleanupRulerEntities();
                return;
            }
            
            // 获取点击位置的坐标
            const cartesian = getCartesianFromScreenPosition(click.position);
            
            // 如果所有尝试都失败，退出
            if (!Cesium.defined(cartesian)) {
                console.log('无法获取点击位置');
                return;
            }
            
            // 检查点是否在地球上
            const cartographic = Cesium.Cartographic.fromCartesian(cartesian);
            
            if (activeShapePoints.value.length === 0) {
                // 第一个点使用start图标
                activeShapePoints.value.push(cartesian);
                createPoint(cartesian, 'start');
                // 在起点位置创建距离标签，显示0公里
                createDistanceLabel(cartesian, 0);
                // 初始化预览线
                updateRulerShape();
            } else {
                // 添加中间点
                activeShapePoints.value.push(cartesian);
                createPoint(cartesian, 'mid');
                
                // 计算从起点到当前点的累计距离
                let totalDistance = 0;
                for (let i = 1; i < activeShapePoints.value.length; i++) {
                    const p1 = activeShapePoints.value[i - 1];
                    const p2 = activeShapePoints.value[i];
                    totalDistance += computeDistance(p1, p2);
                }
                
                // 创建线段
                const p1 = activeShapePoints.value[activeShapePoints.value.length - 2];
                const p2 = activeShapePoints.value[activeShapePoints.value.length - 1];
                createPolyline([p1, p2]);
                
                // 在当前点位位置创建距离标签，显示累计距离
                createDistanceLabel(cartesian, totalDistance);
            }
        }, Cesium.ScreenSpaceEventType.LEFT_CLICK);
        
        // 添加鼠标移动事件 - 实时预览线段
        handler.value.setInputAction((movement) => {
            if (activeShapePoints.value.length === 0) {
                return;
            }
            
            // 获取鼠标当前位置
            const cartesian = getCartesianFromScreenPosition(movement.endPosition);
            
            // 如果所有尝试都失败，退出
            if (!Cesium.defined(cartesian)) {
                return;
            }
            
            // 更新鼠标位置引用，不重建实体
            mousePosition.value = cartesian;
            
        }, Cesium.ScreenSpaceEventType.MOUSE_MOVE);
        
        // 右键点击完成测距
        handler.value.setInputAction(() => {
            // 检查是否只有一个点
            if (activeShapePoints.value.length === 1) {
                // 如果只有一个点，清除所有实体并取消绘制
                cleanupRulerEntities();
            } else if (activeShapePoints.value.length > 1) {
                // 如果有多个点，则将最后一个点替换为end图标
                // 获取最后一个点的位置
                const lastPosition = activeShapePoints.value[activeShapePoints.value.length - 1];
                
                // 遍历所有已创建的实体，找到并移除最后一个点的实体
                for (let i = rulerEntities.value.length - 1; i >= 0; i--) {
                    const entity = rulerEntities.value[i];
                    // 检查实体是否是点（使用billboard）并且位置与最后一个点匹配
                    if (entity.billboard && Cesium.Cartesian3.equals(entity.position._value, lastPosition)) {
                        viewer.entities.remove(entity);
                        rulerEntities.value.splice(i, 1);
                        break; // 找到并移除后就退出循环
                    }
                }
                
                // 创建新的end图标点，并确保它在最上层显示
                createPoint(lastPosition, 'end');
                
                // 计算从起点到当前点的累计距离
                let totalDistance = 0;
                for (let i = 1; i < activeShapePoints.value.length; i++) {
                    const p1 = activeShapePoints.value[i - 1];
                    const p2 = activeShapePoints.value[i];
                    totalDistance += computeDistance(p1, p2);
                }
                
                // 移除上一个距离标签（如果存在）
                for (let i = rulerEntities.value.length - 1; i >= 0; i--) {
                    const entity = rulerEntities.value[i];
                    // 检查实体是否是标签且位置与最后一个点匹配
                    if (entity.label && Cesium.Cartesian3.equals(entity.position._value, lastPosition)) {
                        viewer.entities.remove(entity);
                        rulerEntities.value.splice(i, 1);
                        break;
                    }
                }
                
                // 在最后一个点位置创建带删除按钮的距离标签
                createDistanceLabel(lastPosition, totalDistance, true);
            }
            
            terminateRulerShape();
            mousePosition.value = null;
        }, Cesium.ScreenSpaceEventType.RIGHT_CLICK);
        
    } else {
        // 关闭测距模式
        drawingMode.value = '';
        mousePosition.value = null;
        
        // 移除事件处理器
        handler.value.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_CLICK);
        handler.value.removeInputAction(Cesium.ScreenSpaceEventType.MOUSE_MOVE);
        handler.value.removeInputAction(Cesium.ScreenSpaceEventType.RIGHT_CLICK);
        
        // 清除测距实体
        cleanupRulerEntities();
    }
};

const isDirection = ref(false);

/**
 * 是否显示方向控制器
 */
const showDirection = () => {
    // 在Cesium中，可以通过视角控制器实现类似功能
    // 不过这里我们不实现，只保留接口
    isDirection.value = !isDirection.value;
};

</script>
<style lang="scss" scoped>
.layer-content {
    position: absolute;
    bottom: 5vw;
    left: 1vw;
    z-index: 1;

    div {
        margin-right: 10px;
    }
}

.d-flex {
    display: flex;
}

.block {
    width: 30px;
    height: 30px;
    cursor: pointer;
}

.Satellite {
    background: url('@/assets/satellite.png');
    background-size: 100% 100%;
    width: 30px;
    height: 30px;
}

.normal {
    background: url('@/assets/normal.png');
    background-size: 100% 100%;
    width: 30px;
    height: 30px;
}

.active {
    outline: 2px solid #12be0a;
}

.font20 {
    font-size: 20px;
}
</style>