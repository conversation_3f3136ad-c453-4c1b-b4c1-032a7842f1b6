import {ref} from 'vue'
import {message} from 'ant-design-vue'
import {ControlSource} from '@/types/device'
import {
  postPayloadAuth,
  postPayloadCommands,
  PayloadCommandsEnum,
  PostCameraModeBody,
  PostCameraFocalLengthBody,
  PostGimbalResetBody,
  PostCameraAimBody,
  PostCameraExposureBody,
  PostCameraExposureValueBody,
  PostCameraFocusBody,
  PostCameraFocusValueBody,
  PostCameraThermometricBody,
  PostCameraSplitscreenBody,
  PostCameraPhotostorageBody,
  PostCameraVideostorageBody,
  PostCameraThermometricPointBody,
  PostCameraThermometricAreaBody,
} from '@/api/drone-control/payload'

export function usePayloadControl() {
  function checkPayloadAuth(controlSource?: ControlSource) {
    if (controlSource !== ControlSource.A) {
      message.error('未获取负载控制权或负载控制权已失效，请重新获取。')
      return false
    }
    return true
  }

  async function authPayload(sn: string, payloadIndx: string) {
    const {code} = await postPayloadAuth(sn, {
      payload_index: payloadIndx
    })
    if (code === 0) {
      message.success('获取负载控制权成功')
      return true
    }
    return false
  }

  async function resetGimbal(sn: string, data: PostGimbalResetBody) {
    const {code} = await postPayloadCommands(sn, {
      cmd: PayloadCommandsEnum.GimbalReset,
      data: data
    })
    if (code === 0) {
      message.success('云台重置成功')
    }
  }

  async function switchCameraMode(sn: string, data: PostCameraModeBody) {
    const {code} = await postPayloadCommands(sn, {
      cmd: PayloadCommandsEnum.CameraModeSwitch,
      data: data
    })
    if (code === 0) {
      message.success('相机模式切换成功')
    }
  }

  let photoCount = ref(0);

  async function takeCameraPhoto(sn: string, payloadIndx: string) {
    const {code} = await postPayloadCommands(sn, {
      cmd: PayloadCommandsEnum.CameraPhotoTake,
      data: {
        payload_index: payloadIndx
      }
    })
    if (code === 0) {
      photoCount.value++;
      message.success('拍照成功')
    }
  }

  async function startCameraRecording(sn: string, payloadIndx: string) {
    const {code} = await postPayloadCommands(sn, {
      cmd: PayloadCommandsEnum.CameraRecordingStart,
      data: {
        payload_index: payloadIndx
      }
    })
    if (code === 0) {
      message.success('开始录像成功')
    }
  }

  async function stopCameraRecording(sn: string, payloadIndx: string) {
    const {code} = await postPayloadCommands(sn, {
      cmd: PayloadCommandsEnum.CameraRecordingStop,
      data: {
        payload_index: payloadIndx
      }
    })
    if (code === 0) {
      message.success('结束录像成功')
    }
  }

  async function changeCameraFocalLength(sn: string, data: PostCameraFocalLengthBody) {
    const {code} = await postPayloadCommands(sn, {
      cmd: PayloadCommandsEnum.CameraFocalLengthSet,
      data: data,
    })
    if (code === 0) {
      message.success('变焦成功')
    }
  }

  async function cameraAim(sn: string, data: PostCameraAimBody) {
    const {code} = await postPayloadCommands(sn, {
      cmd: PayloadCommandsEnum.CameraAim,
      data: data,
    })
    if (code === 0) {
      message.success('变焦对准成功')
    }
  }

  async function cameraExposure(sn: string, data: PostCameraExposureBody) {
    const {code} = await postPayloadCommands(sn, {
      cmd: PayloadCommandsEnum.CameraExposure,
      data: data,
    })
    if (code === 0) {
      message.success('相机曝光模式设置成功')
    }
  }

  async function cameraExposureValue(sn: string, data: PostCameraExposureValueBody) {
    const {code} = await postPayloadCommands(sn, {
      cmd: PayloadCommandsEnum.CameraExposureValue,
      data: data,
    })
    if (code === 0) {
      message.success('相机曝光值调节成功')
    }
  }

  async function cameraFocus(sn: string, data: PostCameraFocusBody) {
    const {code} = await postPayloadCommands(sn, {
      cmd: PayloadCommandsEnum.CameraFocus,
      data: data,
    })
    if (code === 0) {
      message.success('相机对焦模式设置成功')
    }
  }

  async function cameraFocusValue(sn: string, data: PostCameraFocusValueBody) {
    const {code} = await postPayloadCommands(sn, {
      cmd: PayloadCommandsEnum.CameraFocusValue,
      data: data,
    })
    if (code === 0) {
      message.success('相机对焦值调节成功')
    }
  }

  async function thermometric(sn: string, data: PostCameraThermometricBody) {
    const {code} = await postPayloadCommands(sn, {
      cmd: PayloadCommandsEnum.CameraThermometric,
      data: data,
    })
    if (code === 0) {
      message.success('红外测温模式设置成功')
    }
  }

  async function splitscreen(sn: string, data: PostCameraSplitscreenBody) {
    const {code} = await postPayloadCommands(sn, {
      cmd: PayloadCommandsEnum.CameraSplitscreen,
      data: data,
    })
    if (code === 0) {
      message.success('分屏设置成功')
    }
  }

  async function photostorage(sn: string, data: PostCameraPhotostorageBody) {
    const {code} = await postPayloadCommands(sn, {
      cmd: PayloadCommandsEnum.CameraPhotostorage,
      data: data,
    })
    if (code === 0) {
      message.success('照片存储设置成功')
    }
  }

  async function videostorage(sn: string, data: PostCameraVideostorageBody) {
    const {code} = await postPayloadCommands(sn, {
      cmd: PayloadCommandsEnum.CameraVideostorage,
      data: data,
    })
    if (code === 0) {
      message.success('视频存储设置成功')
    }
  }

  async function thermometricPoint(sn: string, data: PostCameraThermometricPointBody) {
    const {code} = await postPayloadCommands(sn, {
      cmd: PayloadCommandsEnum.CameraThermometricPoint,
      data: data,
    })
    if (code === 0) {
      message.success('红外测温点设置成功')
    }
  }

  async function thermometricArea(sn: string, data: PostCameraThermometricAreaBody) {
    const {code} = await postPayloadCommands(sn, {
      cmd: PayloadCommandsEnum.CameraThermometricArea,
      data: data,
    })
    if (code === 0) {
      // message.success('红外测温区域设置成功')
    }
  }

  return {
    checkPayloadAuth,
    authPayload,
    resetGimbal,
    switchCameraMode,
    takeCameraPhoto,
    startCameraRecording,
    stopCameraRecording,
    changeCameraFocalLength,
    cameraAim,
    cameraExposure,
    cameraExposureValue,
    cameraFocus,
    cameraFocusValue,
    thermometric,
    splitscreen,
    photostorage,
    videostorage,
    thermometricPoint,
    thermometricArea,
    photoCount
  }
}