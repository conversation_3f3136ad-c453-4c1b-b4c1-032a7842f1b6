export const changeMenu = (data) => {
    const userInfo = JSON.parse(localStorage.getItem('hztech-userInfo') || '{}');
    console.log(userInfo?.content?.role_name);

    if (userInfo?.content?.role_name.includes('ssoUser')) {
        return data
    } else {
        return filterHttpRecursively(data);
    }
};

const filterHttpRecursively = (data) => {
    if (!Array.isArray(data)) return data;

    const filteredData = data.filter(item => !item.path?.includes('http'));

    return filteredData.map(item => {
        if (item.children && Array.isArray(item.children)) {
            return {
                ...item,
                children: filterHttpRecursively(item.children)
            };
        }
        return item;
    });
};



