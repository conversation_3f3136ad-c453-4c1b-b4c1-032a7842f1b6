/**
 * 全局配置文件
 */
export default {
  title: '智飞平台',
  logo: 'S',
  key: 'hztech', //配置主键,目前用于存储
  indexTitle: '智飞平台',
  clientId: 'hztech', // 客户端id
  clientSecret: 'hztech_secret', // 客户端密钥
  tenantMode: true, // 是否开启租户模式
  tenantId: '000000', // 管理组租户编号
  captchaMode: process.env.NODE_ENV == 'production' ? true : false, // 是否开启验证码模式
  switchMode: false, // 是否开启部门切换模式
  lockPage: '/lock',
  tokenTime: 3000,
  tokenHeader: 'HzTech-Auth',
  //http的status默认放行不才用统一处理的,
  statusWhiteList: [],
  //配置首页不可关闭
  setting: {
    sidebar: 'vertical',
    tag: true,
    debug: true,
    collapse: true,
    search: true,
    lock: true,
    screenshot: true,
    fullscren: true,
    theme: true,
    menu: true,
  },
  fistPage: {
    name: '首页',
    path: '/wel/index',
  },
  //oauth2配置
  oauth2: {
    // 使用后端工程 @org.hztech.test.Sm2KeyGenerator 获取
    publicKey: '044a7ec03917d0f9fb704b52973cb1001f427240710e0c0c7f873f6eb6cfdaa197d5d867b46d48eb716f00ccc538391157d10e5fe02b7add0aafdd02836bfc0f3f',
    privateKey: 'b325f997b95fb3f55ba8dc67d7d900c59613d913d21c241652e80fe5995a1d30',
  },
  //配置菜单的属性
  menu: {
    iconDefault: 'icon-caidan',
    label: 'name',
    path: 'path',
    icon: 'source',
    children: 'children',
    query: 'query',
    href: 'path',
    meta: 'meta',
  },
  // 流程设计器类型(true->nutflow,false->flowable)
  // designMode: true,
  // 流程设计器地址(flowable模式)
  // designUrl: 'http://localhost:9999',
  // 第三方系统授权地址
  authUrl: 'http://localhost/hztech-auth/oauth/render',
  // 报表设计器地址(cloud端口为8108,boot端口为80)
  reportUrl: 'http://localhost:8108/ureport',
  // 单点登录系统认证(hztech-auth服务的地)
  ssoUrl: 'http://localhost:8100/oauth/authorize?client_id=hztech&response_type=code&redirect_uri=',
  // 单点登录回调地址(HzTech服务的登录界面地址)
  redirectUri: 'http://localhost:2888/login',
};
