<template>
    <div id="cockpitMap">
        <div class="back-button-container">
            <a-button class="back-btn" @click="goBack">
                <template #icon><left-outlined /></template>
                返回
            </a-button>
        </div>
        <div class="view-control-container">
            <a-tooltip placement="left" v-if="route.query.file_url === '/models/pc/0/terra_b3dms/tileset.json'"
                title="切换模型">
                <a-button class="view-btn" @click="switchModel">
                    <template #icon><swap-outlined /></template>
                </a-button>
            </a-tooltip>
            <a-tooltip placement="left" title="顶视图">
                <a-button class="view-btn" @click="switchToTopView">
                    <template #icon><column-height-outlined /></template>
                </a-button>
            </a-tooltip>
            <a-tooltip placement="left" title="透视图">
                <a-button class="view-btn" @click="switchToPerspectiveView">
                    <template #icon><eye-outlined /></template>
                </a-button>
            </a-tooltip>
            <a-tooltip placement="left" title="绘制圆圈">
                <a-button class="view-btn" @click="drawCircle">
                    <template #icon><border-outlined /></template>
                </a-button>
            </a-tooltip>
            <a-tooltip placement="left" title="坐标转换">
                <a-button class="view-btn" @click="showCoordinateDialog">
                    <template #icon><environment-outlined /></template>
                </a-button>
            </a-tooltip>
            <a-tooltip placement="left" title="模型信息">
                <a-button class="view-btn" @click="showModelInfo">
                    <template #icon><info-circle-outlined /></template>
                </a-button>
            </a-tooltip>
        </div>

        <!-- 坐标转换对话框 -->
        <a-modal
            v-model:open="coordinateDialogVisible"
            title="坐标系转换设置"
            @ok="applyCoordinateTransform"
            @cancel="coordinateDialogVisible = false"
            width="600px"
        >
            <a-form :model="coordinateForm" layout="vertical">
                <a-row :gutter="16">
                    <a-col :span="12">
                        <a-form-item label="源坐标系">
                            <a-select v-model:value="coordinateForm.sourceSystem" placeholder="选择源坐标系">
                                <a-select-option value="local">本地坐标系</a-select-option>
                                <a-select-option value="utm">UTM坐标系</a-select-option>
                                <a-select-option value="wgs84">WGS84</a-select-option>
                            </a-select>
                        </a-form-item>
                    </a-col>
                    <a-col :span="12">
                        <a-form-item label="目标坐标系">
                            <a-select v-model:value="coordinateForm.targetSystem" placeholder="选择目标坐标系">
                                <a-select-option value="wgs84">WGS84 (推荐)</a-select-option>
                                <a-select-option value="gcj02">GCJ-02</a-select-option>
                            </a-select>
                        </a-form-item>
                    </a-col>
                </a-row>
                <a-row :gutter="16" v-if="coordinateForm.sourceSystem === 'utm'">
                    <a-col :span="12">
                        <a-form-item label="UTM区域">
                            <a-input-number v-model:value="coordinateForm.utmZone" :min="1" :max="60" placeholder="UTM区域号" />
                        </a-form-item>
                    </a-col>
                    <a-col :span="12">
                        <a-form-item label="南北半球">
                            <a-select v-model:value="coordinateForm.hemisphere">
                                <a-select-option value="N">北半球</a-select-option>
                                <a-select-option value="S">南半球</a-select-option>
                            </a-select>
                        </a-form-item>
                    </a-col>
                </a-row>
                <a-row :gutter="16" v-if="coordinateForm.sourceSystem === 'local'">
                    <a-col :span="8">
                        <a-form-item label="参考点经度">
                            <a-input-number v-model:value="coordinateForm.referenceLon" :precision="6" placeholder="参考点经度" />
                        </a-form-item>
                    </a-col>
                    <a-col :span="8">
                        <a-form-item label="参考点纬度">
                            <a-input-number v-model:value="coordinateForm.referenceLat" :precision="6" placeholder="参考点纬度" />
                        </a-form-item>
                    </a-col>
                    <a-col :span="8">
                        <a-form-item label="参考点高程">
                            <a-input-number v-model:value="coordinateForm.referenceAlt" :precision="2" placeholder="参考点高程" />
                        </a-form-item>
                    </a-col>
                </a-row>
                <a-form-item label="模型偏移量">
                    <a-row :gutter="8">
                        <a-col :span="8">
                            <a-input-number v-model:value="coordinateForm.offsetX" :precision="2" placeholder="X偏移" addon-before="X" />
                        </a-col>
                        <a-col :span="8">
                            <a-input-number v-model:value="coordinateForm.offsetY" :precision="2" placeholder="Y偏移" addon-before="Y" />
                        </a-col>
                        <a-col :span="8">
                            <a-input-number v-model:value="coordinateForm.offsetZ" :precision="2" placeholder="Z偏移" addon-before="Z" />
                        </a-col>
                    </a-row>
                </a-form-item>
            </a-form>
        </a-modal>

        <!-- 模型信息对话框 -->
        <a-modal
            v-model:open="modelInfoVisible"
            title="模型信息"
            :footer="null"
            width="500px"
        >
            <a-descriptions :column="1" bordered>
                <a-descriptions-item label="模型类型">{{ modelInfo.type }}</a-descriptions-item>
                <a-descriptions-item label="模型URL">{{ modelInfo.url }}</a-descriptions-item>
                <a-descriptions-item label="包围球半径">{{ modelInfo.boundingRadius }}米</a-descriptions-item>
                <a-descriptions-item label="中心坐标">
                    经度: {{ modelInfo.centerLon }}°<br>
                    纬度: {{ modelInfo.centerLat }}°<br>
                    高度: {{ modelInfo.centerHeight }}米
                </a-descriptions-item>
                <a-descriptions-item label="坐标系">{{ modelInfo.coordinateSystem }}</a-descriptions-item>
                <a-descriptions-item label="加载状态">
                    <a-tag :color="modelInfo.loadStatus === '已加载' ? 'green' : 'orange'">
                        {{ modelInfo.loadStatus }}
                    </a-tag>
                </a-descriptions-item>
            </a-descriptions>
        </a-modal>
    </div>
</template>

<script setup>
import { onMounted, ref, reactive } from 'vue';
import { useGMapManage } from '@/hooks/use-c-map';
import { getApp } from '@/root';
import {
    ColumnHeightOutlined,
    EyeOutlined,
    LeftOutlined,
    SwapOutlined,
    BorderOutlined,
    EnvironmentOutlined,
    InfoCircleOutlined
} from '@ant-design/icons-vue';
import { useRouter, useRoute } from 'vue-router';
import { message } from 'ant-design-vue';

const viewer = ref(null);
const tileset = ref(null);
const glbModel = ref(null); // 添加glb模型引用
const router = useRouter();
const route = useRoute();
const currentModelUrl = ref('');  // 添加变量来跟踪当前模型
const isGlbModel = ref(false);  // 添加变量来跟踪当前是否为glb模型
const circleEntity = ref(null); // 存储绘制的圆圈实体

// 坐标转换对话框相关
const coordinateDialogVisible = ref(false);
const coordinateForm = reactive({
    sourceSystem: 'local',
    targetSystem: 'wgs84',
    utmZone: 50,
    hemisphere: 'N',
    referenceLon: 114.31,
    referenceLat: 30.57,
    referenceAlt: 0,
    offsetX: 0,
    offsetY: 0,
    offsetZ: 0
});

// 模型信息对话框相关
const modelInfoVisible = ref(false);
const modelInfo = reactive({
    type: '',
    url: '',
    boundingRadius: 0,
    centerLon: 0,
    centerLat: 0,
    centerHeight: 0,
    coordinateSystem: 'WGS84',
    loadStatus: '未加载'
});

// 返回上一级页面
const goBack = () => {
    router.back();
};

// 显示坐标转换对话框
const showCoordinateDialog = () => {
    coordinateDialogVisible.value = true;
};

// 显示模型信息对话框
const showModelInfo = () => {
    updateModelInfo();
    modelInfoVisible.value = true;
};

// 更新模型信息
const updateModelInfo = () => {
    if (!viewer.value) return;

    const model = isGlbModel.value ? glbModel.value : tileset.value;
    if (!model) return;

    const boundingSphere = model.boundingSphere;
    const cartographic = Cesium.Cartographic.fromCartesian(boundingSphere.center);

    modelInfo.type = isGlbModel.value ? 'GLB模型' : '3D Tiles';
    modelInfo.url = currentModelUrl.value;
    modelInfo.boundingRadius = Math.round(boundingSphere.radius * 100) / 100;
    modelInfo.centerLon = Math.round(Cesium.Math.toDegrees(cartographic.longitude) * 1000000) / 1000000;
    modelInfo.centerLat = Math.round(Cesium.Math.toDegrees(cartographic.latitude) * 1000000) / 1000000;
    modelInfo.centerHeight = Math.round(cartographic.height * 100) / 100;
    modelInfo.loadStatus = '已加载';
};

// 坐标转换工具函数
const coordinateTransformUtils = {
    // UTM转WGS84
    utmToWgs84(x, y, zone, hemisphere = 'N') {
        // 这里使用简化的UTM转换，实际项目中建议使用proj4js库
        const a = 6378137.0; // WGS84长半轴
        const e = 0.0818191908426; // WGS84第一偏心率
        const k0 = 0.9996; // UTM比例因子

        const x0 = 500000; // UTM东偏移
        const y0 = hemisphere === 'N' ? 0 : 10000000; // UTM北偏移

        const lon0 = (zone - 1) * 6 - 180 + 3; // 中央经线

        // 简化转换（实际应用中需要更精确的算法）
        const lon = lon0 + (x - x0) / (a * k0) * 180 / Math.PI;
        const lat = (y - y0) / (a * k0) * 180 / Math.PI;

        return { lon, lat };
    },

    // 本地坐标转WGS84
    localToWgs84(x, y, z, refLon, refLat, refAlt) {
        // 简化的本地坐标转换
        const earthRadius = 6378137.0;
        const latOffset = y / earthRadius * 180 / Math.PI;
        const lonOffset = x / (earthRadius * Math.cos(refLat * Math.PI / 180)) * 180 / Math.PI;

        return {
            lon: refLon + lonOffset,
            lat: refLat + latOffset,
            alt: refAlt + z
        };
    }
};

// 应用坐标转换
const applyCoordinateTransform = () => {
    if (!viewer.value || (!tileset.value && !glbModel.value)) {
        message.error('请先加载模型');
        return;
    }

    try {
        let newPosition;
        const form = coordinateForm;

        if (form.sourceSystem === 'utm') {
            // UTM坐标转换
            const modelCenter = getModelCenter();
            const cartographic = Cesium.Cartographic.fromCartesian(modelCenter);
            const utmCoords = coordinateTransformUtils.utmToWgs84(
                cartographic.longitude,
                cartographic.latitude,
                form.utmZone,
                form.hemisphere
            );
            newPosition = Cesium.Cartesian3.fromDegrees(
                utmCoords.lon + form.offsetX / 111000,
                utmCoords.lat + form.offsetY / 111000,
                cartographic.height + form.offsetZ
            );
        } else if (form.sourceSystem === 'local') {
            // 本地坐标转换
            const localCoords = coordinateTransformUtils.localToWgs84(
                form.offsetX,
                form.offsetY,
                form.offsetZ,
                form.referenceLon,
                form.referenceLat,
                form.referenceAlt
            );
            newPosition = Cesium.Cartesian3.fromDegrees(
                localCoords.lon,
                localCoords.lat,
                localCoords.alt
            );
        } else {
            // WGS84坐标，只应用偏移
            const modelCenter = getModelCenter();
            const cartographic = Cesium.Cartographic.fromCartesian(modelCenter);
            newPosition = Cesium.Cartesian3.fromDegrees(
                Cesium.Math.toDegrees(cartographic.longitude) + form.offsetX / 111000,
                Cesium.Math.toDegrees(cartographic.latitude) + form.offsetY / 111000,
                cartographic.height + form.offsetZ
            );
        }

        // 应用新的变换矩阵
        const modelMatrix = Cesium.Transforms.eastNorthUpToFixedFrame(newPosition);

        if (isGlbModel.value && glbModel.value) {
            glbModel.value.modelMatrix = modelMatrix;
        } else if (tileset.value) {
            tileset.value.modelMatrix = modelMatrix;
        }

        // 更新相机视角
        viewer.value.zoomTo(isGlbModel.value ? glbModel.value : tileset.value);

        coordinateDialogVisible.value = false;
        message.success('坐标转换应用成功');

    } catch (error) {
        console.error('坐标转换失败:', error);
        message.error('坐标转换失败，请检查参数设置');
    }
};

// 计算模型的实际中心点（考虑变换矩阵）
const getModelCenter = () => {
    if (isGlbModel.value && glbModel.value) {
        // 对于GLB模型，使用其包围球的中心
        return glbModel.value.boundingSphere.center;
    } else if (!isGlbModel.value && tileset.value) {
        const boundingSphere = tileset.value.boundingSphere;
        const modelMatrix = tileset.value.modelMatrix;

        // 如果有模型变换矩阵，需要应用变换
        if (modelMatrix && !Cesium.Matrix4.equals(modelMatrix, Cesium.Matrix4.IDENTITY)) {
            return Cesium.Matrix4.multiplyByPoint(modelMatrix, boundingSphere.center, new Cesium.Cartesian3());
        }

        return boundingSphere.center;
    }

    return null;
};

// 绘制圆圈
const drawCircle = (color = '#ccae62') => {
    if (!viewer.value) return;

    // 如果已经有圆圈，先移除
    if (circleEntity.value) {
        viewer.value.entities.remove(circleEntity.value);
        circleEntity.value = null;
        return;
    }

    const modelCenter = getModelCenter();
    if (!modelCenter) return;

    // 获取模型中心的笛卡尔坐标
    const cartographic = Cesium.Cartographic.fromCartesian(modelCenter);
    const centerLongitude = cartographic.longitude;
    const centerLatitude = cartographic.latitude;

    // 确保颜色是字符串类型
    const colorString = typeof color === 'string' ? color : '#ccae62';

    // 创建Cesium颜色对象
    let mainColor, borderColor;
    mainColor = Cesium.Color.fromCssColorString(colorString).withAlpha(0.5);
    borderColor = Cesium.Color.fromCssColorString(colorString).brighten(-0.3, new Cesium.Color());

    // 创建一个圆形实体，并设置材质
    circleEntity.value = viewer.value.entities.add({
        position: Cesium.Cartesian3.fromRadians(centerLongitude, centerLatitude, cartographic.height),
        name: '模型中心圆',
        // 使用椭圆实体，因为Cesium中的圆是椭圆的特例
        ellipse: {
            semiMinorAxis: 100, // 100米半径
            semiMajorAxis: 100, // 100米半径
            material: new Cesium.ColorMaterialProperty(mainColor),
            outline: true,
            outlineColor: borderColor,
            outlineWidth: 30,
            // 确保圆圈采样足够的点以贴合起伏地形
            granularity: Cesium.Math.RADIANS_PER_DEGREE / 4, // 每度切分4个点
            classificationType: Cesium.ClassificationType.BOTH // 同时贴合地形和3D Tiles
        }
    });

    // 缩放视图以查看圆圈
    viewer.value.zoomTo(circleEntity.value);
};

// 切换到顶视图
const switchToTopView = () => {
    if (!viewer.value) return;
    if ((!tileset.value && !glbModel.value) || !getModelCenter()) return;

    const modelCenter = getModelCenter();
    if (!modelCenter) return;

    const cartographic = Cesium.Cartographic.fromCartesian(modelCenter);
    const boundingSphere = isGlbModel.value ? glbModel.value.boundingSphere : tileset.value.boundingSphere;

    // 计算合适的高度，确保整个模型可见
    const radius = boundingSphere.radius;
    const height = Math.max(radius * 2, 300);

    viewer.value.camera.flyTo({
        destination: Cesium.Cartesian3.fromRadians(
            cartographic.longitude,
            cartographic.latitude,
            cartographic.height + height
        ),
        orientation: {
            heading: 0.0,
            pitch: -Cesium.Math.PI_OVER_TWO, // -90度，直视下方
            roll: 0.0
        },
        duration: 1.5, // 2秒飞行时间，避免乱飞跃
    });
};

// 切换到透视图
const switchToPerspectiveView = () => {
    if (!viewer.value) return;
    if ((!tileset.value && !glbModel.value) || !getModelCenter()) return;

    const modelCenter = getModelCenter();
    if (!modelCenter) return;

    const cartographic = Cesium.Cartographic.fromCartesian(modelCenter);
    const boundingSphere = isGlbModel.value ? glbModel.value.boundingSphere : tileset.value.boundingSphere;

    // 计算合适的高度和距离，确保整个模型可见
    const radius = boundingSphere.radius;
    const height = Math.max(radius * 1.5, 200);
    const distance = height * 1.2; // 水平距离偏移量

    // 计算相机位置：向西南方向偏移以便从西南角观察模型
    const cameraPosition = Cesium.Cartesian3.fromRadians(
        cartographic.longitude - Cesium.Math.toRadians(distance / 111000 / Math.sqrt(2)), // 向西偏移
        cartographic.latitude - Cesium.Math.toRadians(distance / 111000 / Math.sqrt(2)), // 向南偏移
        cartographic.height + height
    );

    viewer.value.camera.flyTo({
        destination: cameraPosition,
        orientation: {
            heading: Cesium.Math.toRadians(45), // 从西南角观察（朝向东北方向）
            pitch: Cesium.Math.toRadians(-45), // -45度俯角
            roll: 0.0
        },
        duration: 1.5
    });
};

// 加载GLB模型
const loadGlbModel = (url) => {
    if (!viewer.value) return;

    // 移除现有的glb模型（如果有）
    if (glbModel.value) {
        viewer.value.scene.primitives.remove(glbModel.value);
        glbModel.value = null;
    }

    // 加载GLB模型
    const position = Cesium.Cartesian3.fromDegrees(114.31, 30.57, -10); // 将高度设为负值，让模型位于地面以下
    const headingPitchRoll = new Cesium.HeadingPitchRoll(0, -Math.PI / 2, 0); // 调整pitch为-90度，将竖直模型放平
    const modelMatrix = Cesium.Transforms.headingPitchRollToFixedFrame(
        position,
        headingPitchRoll
    );

    glbModel.value = Cesium.Model.fromGltf({
        url: url,
        modelMatrix: modelMatrix,
        scale: 1.0, // 可根据需要调整模型大小
        minimumPixelSize: 128,
        maximumScale: 20000,
        allowPicking: true
    });

    viewer.value.scene.primitives.add(glbModel.value);

    // 监听模型加载完成并缩放到合适位置
    glbModel.value.readyPromise.then(function (model) {
        viewer.value.camera.flyToBoundingSphere(model.boundingSphere, {
            offset: new Cesium.HeadingPitchRange(0, -Math.PI / 4, model.boundingSphere.radius * 2.0)
        });
    });
};

// 加载3D Tiles模型
const load3DTilesModel = (url) => {
    if (!viewer.value) return;

    // 移除现有的tileset（如果有）
    if (tileset.value) {
        viewer.value.scene.primitives.remove(tileset.value);
        tileset.value = null;
    }

    // 加载3D Tiles模型
    tileset.value = viewer.value.scene.primitives.add(
        new Cesium.Cesium3DTileset({
            url: url,
            maximumScreenSpaceError: 1.0,
            dynamicScreenSpaceError: true,
            dynamicScreenSpaceErrorFactor: 4.0,
            dynamicScreenSpaceErrorDensity: 0.00278,
            preferLeaves: true
        })
    );

    // 监听模型加载完成并缩放到合适位置
    tileset.value.readyPromise.then(function (tilesetLoaded) {
        viewer.value.zoomTo(tilesetLoaded);
    });
};

// 切换模型
const switchModel = () => {
    if (!viewer.value) return;

    // 在两个模型之间切换
    const originalModel = route.query.file_url;
    const alternateModel = '/models/pc/0/terra_pnts/tileset.json';

    // 如果当前是原始模型，则切换到替代模型，反之亦然
    currentModelUrl.value = currentModelUrl.value === alternateModel ? originalModel : alternateModel;

    // 加载选定的模型
    load3DTilesModel(currentModelUrl.value);
    isGlbModel.value = false;
};

// 加载模型（根据文件扩展名决定如何加载）
const loadModel = (url) => {
    if (!url) return;

    // 检查文件类型
    const fileExtension = url.split('.').pop().toLowerCase();

    if (fileExtension === 'glb') {
        // 加载GLB模型
        loadGlbModel(url);
        isGlbModel.value = true;
    } else {
        // 加载3D Tiles模型
        load3DTilesModel(url);
        isGlbModel.value = false;
    }

    currentModelUrl.value = url;
};

onMounted(async () => {
    const app = getApp();
    await useGMapManage().globalPropertiesConfig(app, [114.31, 30.57], 'cockpitMap');
    viewer.value = app.config.globalProperties.$map;

    // 设置当前模型URL为初始URL
    currentModelUrl.value = route.query.file_url;

    // 根据URL加载合适的模型
    loadModel(currentModelUrl.value);
});
</script>

<style lang="scss" scoped>
#cockpitMap {
    width: 100%;
    height: 100%;
    position: relative;
}

:deep(.cesium-viewer-bottom) {
    display: none;
}

.back-button-container {
    position: absolute;
    left: 20px;
    top: 20px;
    z-index: 999;
}

.back-btn {
    height: 36px;
    background-color: white;
    border-radius: 4px;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
    display: flex;
    align-items: center;

    &:hover {
        background-color: #f5f5f5;
    }
}

.view-control-container {
    position: absolute;
    right: 20px;
    bottom: 20px;
    z-index: 999;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.view-btn {
    width: 40px;
    height: 40px;
    background-color: white;
    border-radius: 4px;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
    display: flex;
    align-items: center;
    justify-content: center;

    &:hover {
        background-color: #f5f5f5;
    }
}
</style>