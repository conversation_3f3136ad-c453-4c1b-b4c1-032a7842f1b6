<template>
  <div class="demo-app">
    <a-layout class="page">
    <a-layout-sider class="left" width="40%" style="border-radius: 4px;">
      <div style="width:90%; height: 90%; margin: 4vh">
        <a-layout style="height: 20%; margin-top: 3vh; background-color: white; ">
          <a-layout-sider width="25%" theme="light" align="center">
            <a-avatar :size="60" :src="cloudapi"> 
            </a-avatar>
          </a-layout-sider>
          <a-layout-content style="margin-left: 1vw;" @click="showStatus">
            <div style="height: 50%;">
              <span style="font-size: 16px; font-weight: bolder">{{workspaceName}} </span>
              <RightOutlined style="float: right; margin-top: 5px; color: #8894a0" />
            </div>
            <div style="height: 50%;">
              <CloudSyncOutlined v-if="thingState === EStatusValue.CONNECTED" style="color: #75c5f6" />
              <SyncOutlined spin v-else/>
              <span style="color: #737373; margin-left: 3px;">{{ thingState }}</span>
            </div>
            <a-drawer  placement="right"  width="340px" v-model:visible="drawerVisible">
              <div class="mb10 flex-row flex-justify-center flex-align-center">
                <p class="fz14" style="font-weight: 100;">模块状态</p>
              </div>
              <div class= "width-100 mb10 flex-align-start"  v-for="m in modules" :key="m.name"   style="height: 30px;">
                <div class="ml5" style="float: left; color: #000000;">{{m.name}}</div>
                <div class="ml10" style="float: right; margin-bottom: 8px;">
                  <span :key="m.state" :class="m.state.value === EStatusValue.CONNECTED ? 'green' : 'red'">{{ m.state.value }}&nbsp;</span>
                  <a-button-group >
                  <a-button class="ml5" type="primary" size="small" @click.stop="moduleInstall(m)">install</a-button>
                  <a-button class="ml5 mr5" type="danger" size="small" @click.stop="moduleUninstall(m)">uninstall</a-button>
                  </a-button-group>
                </div>
                <a-divider />

              </div>
            </a-drawer>
          </a-layout-content>

        </a-layout>
        <a-divider  style="height: 2px; background-color: #f5f5f5; margin-top: 3vh;" />

        <a-button id="exitBtn" class="fz18"  @click="confirmAgain"
        style="width: 10vw; height: 10vh; position: fixed; bottom: 13vh; left: 15vw; background-color: #e6e6e6; color: red; border: 0;"
        type="primary">退出
        </a-button>
        <a-modal  v-model:visible="exitVisible" width="300px" :closable="false">
          <template #footer>
            <a-button type="text" style="width: 48%; float: left;" @click="onBack">取消</a-button>
            <a-button type="text" style="width: 48%;" @click="onExit">确认</a-button>
          </template>
          <p>退出后，DJI Pilot和此服务器之间的数据将不会同步</p>
        </a-modal>
      </div>
    </a-layout-sider>
    <!-- 右边区域 -->
    <a-layout-content class="right flex-column">
      <div class="mb5">
        <span class="ml5" style="color: #939393;">序列号</span>
      </div>
      <div class="fz16" style="background-color: white; border-radius: 4px;">
        <a-row style="border-bottom: 1px solid #f4f8f9; height: 45px;" align="middle">
          <a-col :span="1"></a-col>
            <a-col :span="9">
           遥控SN
            </a-col>
          <a-col :span="13" class="flex-align-end flex-column">
            <span style="color: #737373">{{ device.data.gateway_sn }}</span>
          </a-col>
        </a-row>
        <a-row style="border-bottom: 1px solid #f4f8f9; height: 45px;" align="middle" v-if="device.data.online_status && device.data.sn">
          <a-col :span="1"></a-col>
          <a-col :span="9">飞行器 SN</a-col>
          <a-col :span="13" class="flex-align-end flex-column" >
            <span style="color: #737373">{{ device.data.sn }}</span>
          </a-col>
        </a-row>
      </div>
      <div class="mt5 mb5">
        <span class="ml5" style="color: #939393;">设置</span>
      </div>
      <div class="fz16" style="background-color: white; border-radius: 4px;">
        <a-row v-if="device.data.online_status && device.data.sn" style="border-bottom: 1px solid #f4f8f9; height: 45px;" align="middle" @click="bindingDevice">
          <a-col :span="1"></a-col>
          <a-col :span="11">
            绑定设备
          </a-col>
          <a-col :span="10" style="text-align: right">
            <span v-if="device.data.bound_status" style="color: #737373">Aircraft bound</span>
            <span v-else style="color: #737373">Aircraft not bound</span>
          </a-col>
          <a-col :span="2" class="flex-align-center flex-column" >
            <RightOutlined style="color: #8894a0; font-size: 20px;" />
          </a-col>
        </a-row>
        <a-row style="border-bottom: 1px solid #f4f8f9; height: 45px;" align="middle" @click="onMediaSetting">
          <a-col :span="1"></a-col>
          <a-col :span="21">
            媒体文件上传
          </a-col>
          <a-col :span="2" class="flex-align-center flex-column" >
            <RightOutlined style="color: #8894a0; font-size: 20px;" />
          </a-col>
        </a-row>
        <a-row style="border-bottom: 1px solid #f4f8f9; height: 45px;" align="middle" @click="onLiveshareSetting">
          <a-col :span="1"></a-col>
          <a-col :span="21">手动直播</a-col>
          <a-col :span="2" class="flex-align-center flex-column">
            <RightOutlined style="color: #8894a0; font-size: 20px;" />
          </a-col>
        </a-row>
        <a-row style="border-bottom: 1px solid #f4f8f9; height: 45px;" align="middle" @click="onOpen3rdApp">
          <a-col :span="1"></a-col>
          <a-col :span="21">打开第三方页面</a-col>
          <a-col :span="2" class="flex-align-center flex-column">
            <RightOutlined style="color: #8894a0; font-size: 20px;" />
          </a-col>
        </a-row>
      </div>
    </a-layout-content>
  </a-layout>
  </div>
</template>
<script setup>
import cloudapi from '@/assets/cloudapi.png'
import { onMounted, onUnmounted, reactive, ref, watch } from 'vue'
import apiPilot from '@/api/pilot-bridge/index'
import {getPlatformInfo,bindDevice,getDeviceBySn,getUserInfo } from '@/api/manage/index'
import { message, Popconfirm } from 'ant-design-vue'
import { RightOutlined, CloudOutlined, CloudSyncOutlined, SyncOutlined } from '@ant-design/icons-vue'
import { EBizCode, EComponentName, EDownloadOwner, ELocalStorageKey, ERouterName, EStatusValue,EVideoPublishType } from '@/api/enum/index'
import router from '@/router';
import { useConnectWebSocket } from '@/hooks/use-connect-websocket'
import  store  from '@/store'
import EventBus from '@/event-bus'
import {  removeToken, removeRefreshToken } from '@/utils/auth';

const components = apiPilot.init()
const drawerVisible = ref(false)
const exitVisible = ref(false)//退出弹窗状态
const thingState = ref(EStatusValue.DISCONNECT)
const apiState = ref(EStatusValue.DISCONNECT)
const liveState = ref(EStatusValue.DISCONNECT)
const wsState = ref(EStatusValue.DISCONNECT)
const mapState = ref(EStatusValue.DISCONNECT)
const tsaState = ref(EStatusValue.DISCONNECT)
const mediaState = ref(EStatusValue.DISCONNECT)
const waylineState = ref(EStatusValue.DISCONNECT)
const username = ref(localStorage.getItem(ELocalStorageKey.Username))
const wsId = ref(localStorage.getItem(ELocalStorageKey.WorkspaceId))
const workspaceName = ref()
const device = reactive({
  data: {
    sn: '',
    online_status: false,
    device_callsign: '',
    user_id: '',
    user_callsign: '',
    bound_status: false,
    model: '',
    gateway_sn: EStatusValue.DISCONNECT,
    domain: -1
  }
})
const bindParam = {
  device_sn: '',
  userId: '',
  workspaceId: wsId.value
}
const modules = [{
  name: 'Cloud',
  state: thingState,
  module: EComponentName.Thing
}, {
  name: 'Api',
  state: apiState,
  module: EComponentName.Api
}, {
  name: 'Live',
  state: liveState,
  module: EComponentName.Liveshare
}, {
  name: 'Ws',
  state: wsState,
  module: EComponentName.Ws
}, {
  name: 'Map',
  state: mapState,
  module: EComponentName.Map
}, {
  name: 'Tsa',
  state: tsaState,
  module: EComponentName.Tsa
}, {
  name: 'Media',
  state: mediaState,
  module: EComponentName.Media
}, {
  name: 'Wayline',
  state: waylineState,
  module: EComponentName.Mission
}]

let minitor
const showStatus = async () => {
  minitor = setInterval(() => {
    refreshStatus()
    if (!drawerVisible.value) {
      clearInterval(minitor)
    }
  }, 2000)
  drawerVisible.value = true
}
const confirmAgain = () => {
  exitVisible.value = true
}

const onBack = () => {
  exitVisible.value = false
}

const onExit = () => {
removeToken()
removeRefreshToken()
localStorage.clear()
window.djiBridge.platformStopSelf()
}
function returnBool (response) {
  const res = JSON.parse(response)
  const isError = errorHint(res)
  if (JSON.stringify(res.data) !== '{}') {
    return isError && res.data
  }
  return isError
}
function refreshStatus () {
  thingState.value = apiPilot.thingGetConnectState() ? EStatusValue.CONNECTED : EStatusValue.DISCONNECT
  apiState.value = apiPilot.isComponentLoaded(EComponentName.Api) ? EStatusValue.CONNECTED : EStatusValue.DISCONNECT
  liveState.value = apiPilot.isComponentLoaded(EComponentName.Liveshare) ? EStatusValue.CONNECTED : EStatusValue.DISCONNECT
  wsState.value = apiPilot.isComponentLoaded(EComponentName.Ws) && apiPilot.wsGetConnectState()
    ? EStatusValue.CONNECTED
    : EStatusValue.DISCONNECT
  mapState.value = apiPilot.isComponentLoaded(EComponentName.Map) ? EStatusValue.CONNECTED : EStatusValue.DISCONNECT
  tsaState.value = apiPilot.isComponentLoaded(EComponentName.Tsa) ? EStatusValue.CONNECTED : EStatusValue.DISCONNECT
  mediaState.value = apiPilot.isComponentLoaded(EComponentName.Media) ? EStatusValue.CONNECTED : EStatusValue.DISCONNECT
  waylineState.value = apiPilot.isComponentLoaded(EComponentName.Mission) ? EStatusValue.CONNECTED : EStatusValue.DISCONNECT
}
const onLiveshareSetting = async () => {
  router.push(ERouterName.PILOT_LIVESHARE)
}
//打开第三方链接 
const onOpen3rdApp = () => {
  message.error('com.dji.sample is not installed')
  // const packageName = 'com.dji.sample'
  // const isInstalled = window.djiBridge.platformIsAppInstalled(packageName) //是否装了包
  // if (isInstalled) {
  //   window.open('https://www.dji.com')
  // } else {
  //   message.error(packageName + ' is not installed.')
  // }
}
// 媒体文件上传
const onMediaSetting = async () => {
  router.push(ERouterName.PILOT_MEDIA)
}
const messageHandler = async (payload) => {
  if (!payload) {
    return
  }

  switch (payload.biz_code) {
    case EBizCode.GatewayOsd: {
      store.commit('SET_GATEWAY_INFO', payload.data)
      break
    }
    case EBizCode.DeviceOsd: {
      store.commit('SET_DEVICE_INFO', payload.data)
      break
    }
    case EBizCode.DockOsd: {
      store.commit('SET_DOCK_INFO', payload.data)
      break
    }
    case EBizCode.MapElementCreate: {
      store.commit('SET_MAP_ELEMENT_CREATE', payload.data)
      break
    }
    case EBizCode.MapElementUpdate: {
      store.commit('SET_MAP_ELEMENT_UPDATE', payload.data)
      break
    }
    case EBizCode.MapElementDelete: {
      store.commit('SET_MAP_ELEMENT_DELETE', payload.data)
      break
    }
    case EBizCode.DeviceOnline: {
      store.commit('SET_DEVICE_ONLINE', payload.data)
      break
    }
    case EBizCode.DeviceOffline: {
      store.commit('SET_DEVICE_OFFLINE', payload.data)
      break
    }
    case EBizCode.FlightTaskProgress:
    case EBizCode.FlightTaskMediaProgress:
    case EBizCode.FlightTaskMediaHighestPriority: {
      EventBus.emit('flightTaskWs', payload)
      break
    }
    case EBizCode.DeviceHms: {
      store.commit('SET_DEVICE_HMS_INFO', payload.data)
      break
    }
    case EBizCode.DeviceReboot:
    case EBizCode.DroneOpen:
    case EBizCode.DroneClose:
    case EBizCode.CoverOpen:
    case EBizCode.CoverClose:
    case EBizCode.PutterOpen:
    case EBizCode.PutterClose:
    case EBizCode.ChargeOpen:
    case EBizCode.ChargeClose:
    case EBizCode.DeviceFormat:
    case EBizCode.DroneFormat:
    {
      store.commit('SET_DEVICES_CMD_EXECUTE_INFO', {
        biz_code: payload.biz_code,
        timestamp: payload.timestamp,
        ...payload.data,
      })
      break
    }
    case EBizCode.ControlSourceChange:
    case EBizCode.FlyToPointProgress:
    case EBizCode.TakeoffToPointProgress:
    case EBizCode.JoystickInvalidNotify:
    case EBizCode.DrcStatusNotify:
    {
      EventBus.emit('droneControlWs', payload)
      break
    }
    case EBizCode.FlightAreasSyncProgress: {
      EventBus.emit('flightAreasSyncProgressWs', payload.data)
      break
    }
    case EBizCode.FlightAreasDroneLocation: {
      EventBus.emit('flightAreasDroneLocationWs', payload)
      break
    }
    case EBizCode.FlightAreasUpdate: {
      EventBus.emit('flightAreasUpdateWs', payload.data)
      break
    }
    default:
      break
  }
}

// 监听ws 消息
useConnectWebSocket(messageHandler)
let bindNum
onMounted(() => {
  onBackClickReg()
  onStopPlatform()
  let sn = JSON.parse(window.djiBridge.platformGetRemoteControllerSN())
  device.data.gateway_sn = sn.data
  getPlatformInfo().then(res => {
    workspaceName.value = res.data.data.workspace_name
    wsId.value = res.data.data.workspace_id
    localStorage.setItem(ELocalStorageKey.PlatformName, res.data.data.platform_name)
    localStorage.setItem(ELocalStorageKey.WorkspaceName, workspaceName.value)
    localStorage.setItem(ELocalStorageKey.WorkspaceDesc, res.data.data.workspace_desc)
    apiPilot.setPlatformMessage(
      res.data.data.platform_name,
      workspaceName.value,
      res.data.data.workspace_desc
    )
    apiPilot.setWorkspaceId(wsId.value)
  })
  window.connectCallback = (arg) => {
    connectCallback(arg)
  }
  window.wsConnectCallback = (arg) => {
    wsConnectCallback(arg)
  }
  device.data.gateway_sn = apiPilot.getRemoteControllerSN()
  if (device.data.gateway_sn === EStatusValue.DISCONNECT.toString()) {
    message.warn('Data is not available, please restart the remote control.')
    return
  }

  //调用jsbridge遥控SN
  let snInfo = JSON.parse(window.djiBridge.platformGetAircraftSN())
  device.data.sn = snInfo.data
  getDeviceInfo()
  const isLoaded = apiPilot.isComponentLoaded(EComponentName.Thing)
  if (isLoaded) {
    username.value = '' + localStorage.getItem(ELocalStorageKey.Username)
    workspaceName.value = '' + localStorage.getItem(ELocalStorageKey.WorkspaceName)
    refreshStatus()
    apiPilot.setPlatformMessage(
      '' + localStorage.getItem(ELocalStorageKey.PlatformName),
      workspaceName.value,
      '' + localStorage.getItem(ELocalStorageKey.WorkspaceDesc)
    )
    return
  }
  setWorkspaceInfo()
  getUserInfo(username.value,wsId.value).then(res => {
    username.value = res.data.data.username
    localStorage.setItem(ELocalStorageKey.Username, username.value)
    // thing
    const param = {
      host: res.data.data.mqtt_addr,
      username: res.data.data.mqtt_username,
      password: res.data.data.mqtt_password,
      connectCallback: 'connectCallback'
    }

    components.set(EComponentName.Thing, param)
    apiPilot.loadComponent(EComponentName.Thing, components.get(EComponentName.Thing))

    const liveshareParam = {
      videoPublishType: 'video-demand-aux-manual',
      statusCallback: 'liveStatusCallback'
    }
    components.set(EComponentName.Liveshare, liveshareParam)
    bindParam.device_sn = device.data.gateway_sn
    bindParam.user_id = res.data.data.user_id
    bindParam.workspace_id = res.data.data.workspace_id
  })
})
const wsConnectCallback = async (arg) => {
  if (arg) {
    wsState.value = EStatusValue.CONNECTED
  } else {
    wsState.value = EStatusValue.DISCONNECT
  }
}
function errorHint (response) {
  if (response.code !== 0) {
    message.error(response.message)
    console.error(response.message)
    return false
  }
  return true
}
function returnString (response) {
  const res = JSON.parse(response)
  return errorHint(res) ? res.data : ''
}
const bindingDevice = async () => {
  router.push(ERouterName.PILOT_BIND)
}
const connectCallback = async (arg) => {
  if (arg) {
    thingState.value = EStatusValue.CONNECTED
    // liveshare
    apiPilot.loadComponent(EComponentName.Liveshare, components.get(EComponentName.Liveshare))

    // ws
    const wsParam = components.get(EComponentName.Ws)
    wsParam.token = apiPilot.getToken()
    apiPilot.loadComponent(EComponentName.Ws, components.get(EComponentName.Ws))

    // map
    const mapParam = components.get(EComponentName.Map)
    mapParam.userName = username.value
    apiPilot.loadComponent(EComponentName.Map, components.get(EComponentName.Map))

    // tsa
    apiPilot.loadComponent(EComponentName.Tsa, components.get(EComponentName.Tsa))

    // media
    apiPilot.loadComponent(EComponentName.Media, components.get(EComponentName.Media))
    apiPilot.setDownloadOwner(EDownloadOwner.Mine.valueOf())

    // mission
    apiPilot.loadComponent(EComponentName.Mission, {})
    if (!bindParam.device_sn) {
        device.data.gateway_sn = apiPilot.getRemoteControllerSN()
        bindParam.device_sn = device.data.gateway_sn
        return
      }
      //设备绑定
      bindDevice(bindParam).then(bindRes => {
        console.log('设备绑定信息'+bindRes)
        if (bindRes.data.code!= 0) {
          console.error(bindRes.message)
        } else {
          clearInterval(bindNum)
        }
      })
    setTimeout(getDeviceInfo, 3000)
  } else {
    thingState.value = EStatusValue.DISCONNECT
  }
  refreshStatus()
}
function getDeviceInfo () {
  if (!device.data.sn || device.data.sn === EStatusValue.DISCONNECT) {
    return
  }
  getDeviceBySn(bindParam.workspaceId, device.data.sn).then(res => {
   if (res.data.code !== 0) {
      return
    }
    let result = res.data
    device.data.online_status = result.data.status
    device.data.bound_status = result.data.bound_status
    device.data.device_callsign = result.data.nickname
    device.data.model = result.data.device_name
    localStorage.setItem(ELocalStorageKey.Device, JSON.stringify(device.data))
  })
}
function onBackClickReg () {
  // let path = JSON.parse(router)
    window.djiBridge.onBackClick = () => {
      if (window.location.pathname == '/pilot-home') {
        return false
      } else {
        history.go(-1)
        return true
      }
    }
  }
  //点击brige注销按钮事件
  function onStopPlatform () {
    window.djiBridge.onStopPlatform = () => {
      localStorage.clear()
      removeRefreshToken();
      removeToken();
    }
  }
  function moduleInstall (m) {
  let param
  switch (m.module) {
    case EComponentName.Thing:
      param = apiPilot.thingGetConfigs()
      break
    case EComponentName.Api: {
      const apiParam = {
        host: apiPilot.getHost(),
        token: apiPilot.getToken()
      }
      param = apiParam
      break
    }
    case EComponentName.Map: {
      const mapParam = components.get(EComponentName.Map)
      mapParam.userName = '' + localStorage.getItem(ELocalStorageKey.Username)
      param = mapParam
      break
    }
    case EComponentName.Ws: {
      const wsParam = components.get(EComponentName.Ws)
      wsParam.token = '' + localStorage.getItem(ELocalStorageKey.Token)
      param = wsParam
      break
    }
    default:
      param = components.get(m.module)
  }

  components.set(m.module, param)
  console.info(components.get(m.module))
  apiPilot.loadComponent(m.module, components.get(m.module))
  refreshStatus()
}

function moduleUninstall (m) {
  message.info('uninstall ' + m.module)
  apiPilot.unloadComponent(m.module)
  refreshStatus()
}
function setWorkspaceInfo () {
  if (localStorage.getItem(ELocalStorageKey.WorkspaceName)) {
    apiPilot.setPlatformMessage(
      '' + localStorage.getItem(ELocalStorageKey.PlatformName),
      workspaceName.value,
      '' + localStorage.getItem(ELocalStorageKey.WorkspaceDesc)
    )
    apiPilot.setWorkspaceId(wsId.value)
    return
  }
}
</script>
<style lang="scss" scoped>
@import '../../styles/dij/index.scss';
.demo-app {
  width: 100%;
  height: 100%;

  .map-wrapper {
    height: 100%;
    width: 100%;
  }
}
.page {
  display: flex;
  position: absolute;
  transition: width 0.2s ease;
  height: 100%;
  width: 100%;

  .left {
    height: 90%;
    background-color: white;
    margin-top: 6vh;
    margin-left: 2vh;
  }

  .right {
    height: 90%;
    margin-top: 6vh;
    margin-left: 5vh;
    margin-right: 5vh;
  }
}

.green {
  color: green
}

.red {
  color: red;
}

#exitBtn:hover :active {
  background-color: rgb(77, 75, 75);
  width: 10vw;
  height: 10vh;
  position: fixed;
  bottom: 13vh;
  left: 15vw;
  line-height: 10vh;
}
.flex-column{
    flex-direction: column;
}
</style>
