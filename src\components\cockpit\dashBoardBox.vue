<template>
	<div class="mapLatlng">
		<span>纬度：{{ device.latitude }}</span>
		<span>经度：{{ device.longitude }}</span>
	</div>
	<div class="dashBoard-container">
		<div class="dashBoardBox" :class="{'dashBoardBox-collapsed': isCollapsed}">
			<!-- 左 -->
			<div class="indicators speedIndicator">
				<div class="main-container">
					<div class="value-container">
						<div class="ias-value">
							<div class="title">
								<span>水平速度</span>
								<span class="unit">m/s</span>
							</div>
							<div class="value">{{ device.horizontal_speed }}</div>
						</div>
						<div class="gs-value">
							<div class="title">垂直速度</div>
							<div class="value">{{ device.vertical_speed }}<span class="unit">m/s</span></div>
						</div>
					</div>
					<div class="slider-container">
						<div class="slider-group"
							:style="{ '--top': device.speedTop + '%', '--transition': 'all linear 0.2s' }">
							<div class="slider">
								<div class="scale-container" v-for="item in speedList">
									<span>{{ item }}</span>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
			<!-- 右 -->
			<div class="indicators altitudeIndicator">
				<div class="main-container1">
					<div class="value-container1">
						<div class="alt-value">
							<div class="value">{{ device.elevation }}</div>
							<div class="title">
								<span>相对</span>
								<span class="unit">m</span>
							</div>
						</div>
						<div class="rl-value">
							<div class="value">{{ device.height.toFixed(2) }} <span class="unit">m</span></div>
							<div class="title">海拔</div>
						</div>
					</div>
					<div class="slider-container1">
						<div class="slider-group"
							:style="{ '--top': device.heightTop + '%', '--transition': 'all linear 0.2s' }">
							<div class="slider">
								<div class="scale-container1" v-for="item in altitude">
									<span>{{ item }}</span>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
			<!-- 中间罗盘 -->
			<!-- <div class="compassData">{{ device.attitude_head }}</div> -->
			<div class="dashBoard">
				<!-- <div class="compassA pa">N</div>
				<div class="compassB pa">S</div>
				<div class="compassC pa">W</div>
				<div class="compassD pa">E</div>
				<div id="gauge" style="width: 11vw;height: 11vw;"></div> -->
				<div class="avoidContainer a-left" style="--deg: 0deg;">
					<div class="centerPointer"></div>
					<div class="avoidValue">
						<div class="snip" style="--color: rgba(255, 255, 255, 0.3);"></div>
						<div class="snip" style="--color: rgba(255, 255, 255, 0.3);"></div>
						<div class="snip" style="--color: rgba(255, 255, 255, 0.3);"></div>
						<div class="snip" style="--color: rgba(255, 255, 255, 0.3);"></div>
					</div>
				</div>
				<div class="avoidContainer a-right" style="--deg: 0deg;">
					<div class="centerPointer"></div>
					<div class="avoidValue">
						<div class="snip" style="--color: rgba(255, 255, 255, 0.3);"></div>
						<div class="snip" style="--color: rgba(255, 255, 255, 0.3);"></div>
					</div>
				</div>
				<div class="pointer">
					<span class="value">{{ device.attitude_head }}°</span>
				</div>
				<div class="yaw-circle" :style="{ transform: `rotate(${device.attitude_count}deg)` }"></div>
				<div class="roll-circle"></div>
				<div class="pitch-circle" style="--value: 50%; --deg: 0deg;">
					<div class="level"></div>
				</div>
				<div class="value" style="--deg: 0deg;"></div>
			</div>
		</div>
		<!-- 底部箭头按钮 -->
		<div class="toggle-btn" @click.stop="toggleCollapse">
			<i class="arrow" :class="{'arrow-down': !isCollapsed, 'arrow-up': isCollapsed}"></i>
		</div>
	</div>
</template>

<script>
// import store from '@/store'
// import * as echarts from 'echarts';
export default {
	data() {
		return {
			// 高度
			altitude: [],
			// 速度
			speedList: [],
			device: {
				latitude: 0,
				longitude: 0,
				height: 0,
				elevation: 0,
				attitude_head: 0,
				attitude_count: 0,
				horizontal_speed: 0,
				vertical_speed: 0,
				speedTop: 0,
				heightTop: -2
			},
			isCollapsed: false,
		};
	},
	props: {
		deviceInfoAttrs: {
			type: Object,
			default: null
		}
	},
	computed: {
		// deviceInfo() {
		// 	return store.state.dock.deviceState.deviceInfo[this.$attrs.cockpit_dock.sn];
		// }
	},
	watch: {
		deviceInfoAttrs: {
			handler(data, oldVal) {
				// const data = newVal.deviceInfo[this.$attrs.cockpit_dock.sn];
				// console.log('驾驶舱deviceInfoinfo', data);
				if (!data) {
					return
				}
				const { attitude_head, vertical_speed, horizontal_speed, height_limit, elevation } = data
				let value = 0;
				// 角度
				if (attitude_head) {
					attitude_head < 0 ? value = Math.abs(attitude_head) : value = 360 - attitude_head
				}
				// 速度
				data.vertical_speed = vertical_speed ? Math.abs(vertical_speed).toFixed(2) : 0
				data.horizontal_speed = horizontal_speed ? Math.abs(horizontal_speed).toFixed(2) : 0
				data.attitude_count = value
				data.heightTop = elevation * 8.6 || -2
				data.speedTop = horizontal_speed * 10
				this.device = data;
				this.setHeight(height_limit)
			},
			deep: true,
			immediate: true
		},
	},
	mounted() {

	},
	methods: {
		setHeight(height_limit) {
			if (!height_limit) {
				return
			}
			//限制高度
			let height = []
			for (let i = height_limit; i > -height_limit; i--) {
				height.push(i)
			}
			this.altitude = height;
			// 速度
			let speed = []
			for (let i = 15; i > -15; i--) {
				speed.push(i)
			}
			this.speedList = speed;
		},
		toggleCollapse() {
			this.isCollapsed = !this.isCollapsed;
		}
	}
}	
</script>

<style lang="scss" scoped>
.mapLatlng {
	position: absolute;
	bottom: 1vw;
	left: 1vw;
	display: flex;
	flex-direction: column;
	font-size: .9em;
	color: #1dcb63;
	filter: drop-shadow(0 0 5px rgba(0, 0, 0, .2));
	padding: .5rem 1.25rem;
	background: rgba(60, 60, 60, .38);
	border-radius: .25rem;
	z-index: 1;
}

.dashBoard-container {
	position: absolute;
	bottom: 2vw;
	left: 50vw;
	transform: translateX(-50%);
	width: 30vw;
	height: 16.25vw;
	z-index: 1;
	pointer-events: none;
	perspective: 800px;
	display: flex;
	flex-direction: column;
	align-items: center;
}

.dashBoardBox {
	width: 30vw;
	height: 16.25vw;
	background: rgba(60, 60, 60, .38);
	position: absolute;
	top: 0;
	left: 50%;
	transform: translateX(-50%);
	border-radius: .25rem;
	color: #fff;
	pointer-events: none;
	transform-origin: bottom center;
	transition: width 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275), 
	            height 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275), 
	            transform 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275), 
	            border-radius 0.5s ease,
                opacity 0.5s ease;
	will-change: transform, width, height, opacity;
	backface-visibility: hidden;
}

.dashBoardBox-collapsed {
	width: 2vw;
	height: 1.5vw;
	transform: translateX(-50%) translateY(90%) scale(0.1) rotateX(30deg);
	border-radius: 50%;
	overflow: hidden;
	opacity: 0.6;
}

.indicators {
	position: relative;
	width: 1.9rem;
	height: 9rem;
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
	align-items: center;
	user-select: none;
}

.speedIndicator {
	width: 2.6vw;
	height: 9vw;
	bottom: 4vw;
	left: 5.5vw;
	position: absolute;
	transform: translateX(-50%);
	filter: drop-shadow(0 0 5px rgba(0, 0, 0, .2));
}

.main-container {
	width: 100%;
	height: 100%;
	-webkit-box-flex: 1;
	flex: auto;
	position: relative;

	&::after {
		content: "";
		display: block;
		height: 1px;
		width: 100%;
		position: absolute;
		top: calc(50% - 1px);
		left: 0;
		transform: translateY(-50%);
		background-color: #1dcb63;
		opacity: .6;
	}
}

.main-container1 {
	width: 100%;
	height: 100%;
	-webkit-box-flex: 1;
	flex: auto;
	position: relative;

	&::after {
		content: "";
		display: block;
		height: 1px;
		width: 100%;
		position: absolute;
		top: calc(50% - 1px);
		right: 0;
		transform: translateY(-50%);
		background-color: #1dcb63;
		opacity: .6;
	}
}

.value-container {
	position: absolute;
	top: 50%;
	left: 0;
	transform: translate(-100%, -50%);
	z-index: 9;
	color: #1dcb63;
	display: flex;
	flex-direction: column;
	justify-content: center;
	font-size: .8em;
}

.value-container1 {
	position: absolute;
	top: 50%;
	right: 0;
	transform: translate(100%, -50%);
	z-index: 9;
	color: #1dcb63;
	display: flex;
	flex-direction: column;
	justify-content: center;
	font-size: .8em;
}

//左
.ias-value {
	border: 1px solid #1dcb63;
	width: 6em;
	border-radius: 3px;
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: .3em 0 .3em 0em;
	white-space: nowrap;

	.title {
		flex: 0 0 20%;
		display: -webkit-box;
		display: -ms-flexbox;
		display: flex;
		flex-direction: column;
		align-items: flex-start;
		justify-content: center;
		font-size: .6em;
		line-height: 1.1;
	}

	.value {
		flex: auto;
		display: -webkit-box;
		display: -ms-flexbox;
		display: flex;
		justify-content: center;
		align-items: center;
		font-size: 1.2em;
		font-weight: 700;
	}
}

.rl-value {
	position: absolute;
	font-weight: 700;
	top: -1.5em;
	right: 0;
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	font-size: .9em;
	white-space: nowrap;

	.title {
		margin-left: .5em;
	}
}

//右
.alt-value {
	border: 1px solid #1dcb63;
	width: 6em;
	border-radius: 3px;
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: .3em 0em .3em 0;
	white-space: nowrap;

	.value {
		flex: auto;
		display: -webkit-box;
		display: -ms-flexbox;
		display: flex;
		justify-content: center;
		align-items: center;
		font-size: 1.2em;
		font-weight: 700;
	}

	.title {
		flex: 0 0 20%;
		display: -webkit-box;
		display: -ms-flexbox;
		display: flex;
		flex-direction: column;
		align-items: flex-end;
		justify-content: center;
		font-size: .6em;
		line-height: 1.1;
	}
}

.gs-value {
	position: absolute;
	font-weight: 700;
	top: -1.5em;
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	font-size: .9em;
	white-space: nowrap;

	.title {
		margin-right: .5em;
	}
}

.slider-container {
	position: relative;
	width: 100%;
	height: 100%;
	margin-bottom: .4rem;
	overflow: hidden;
	border-right: 1px solid #1dcb63;

	&::before {
		content: "";
		display: block;
		width: 60%;
		height: 2px;
		position: absolute;
		top: 0;
		right: 0;
		background-color: #1dcb63;
	}

	&::after {
		content: "";
		display: block;
		width: 60%;
		height: 2px;
		position: absolute;
		bottom: 0;
		right: 0;
		background-color: #1dcb63;
	}
}

.slider-container1 {
	position: relative;
	width: 100%;
	height: 100%;
	margin-bottom: .4rem;
	overflow: hidden;
	border-left: 1px solid #1dcb63;

	&::before {
		content: "";
		display: block;
		width: 60%;
		height: 2px;
		position: absolute;
		top: 0;
		left: 0;
		background-color: #1dcb63;
	}

	&::after {
		content: "";
		display: block;
		width: 60%;
		height: 2px;
		position: absolute;
		bottom: 0;
		left: 0;
		background-color: #1dcb63;
	}
}

.slider-group {
	height: 100%;
	width: 100%;
	display: flex;
	flex-direction: column;
	justify-content: center;
	transition: var(--transition);
	position: absolute;
	top: var(--top);

	.slider {
		flex: 0 0 500%;
		width: 100%;
		display: flex;
		flex-direction: column;
		justify-content: space-between;
		align-items: center;
	}

	.scale-container {
		-webkit-box-flex: 1;
		flex: auto;
		width: 100%;
		color: #1dcb63;
		padding-right: 36%;
		display: -webkit-box;
		display: -ms-flexbox;
		display: flex;
		justify-content: flex-end;
		align-items: center;
		position: relative;
		font-size: .6em;

		&::before {
			content: "";
			width: 30%;
			position: absolute;
			height: 1px;
			background-color: #1dcb63;
			right: 0;
		}

		&::after {
			content: "";
			width: 20%;
			position: absolute;
			height: 1px;
			background-color: #1dcb63;
			right: 0;
			bottom: 0;
		}
	}

	.scale-container1 {
		flex: auto;
		width: 100%;
		color: #1dcb63;
		padding-right: 36%;
		display: flex;
		justify-content: flex-end;
		align-items: center;
		position: relative;
		font-size: .6em;

		&::before {
			content: "";
			width: 30%;
			position: absolute;
			height: 1px;
			background-color: #1dcb63;
			left: 0;
		}

		&::after {
			content: "";
			width: 20%;
			position: absolute;
			height: 1px;
			background-color: #1dcb63;
			left: 0;
			bottom: 0;
		}
	}
}

//右
.altitudeIndicator {
	position: absolute;
	width: 2.6vw;
	height: 9vw;
	bottom: 4vw;
	right: 5.5vw;
	transform: translateX(50%);
	filter: drop-shadow(0 0 5px rgba(0, 0, 0, .2));
}


.compassData {
	position: fixed;
	left: 200px;
	top: 30px
}

//中间罗盘
.dashBoard {
	.pa {
		position: absolute;
	}

	width: 11vw;
	height: 11vw;
	bottom: 2vw;
	left: 50%;
	transform: translateX(-50%);
	position: absolute;
	// transform: translateX(-50%);
	filter: drop-shadow(0 0 5px rgba(0, 0, 0, .2));
	user-select: none;

	.compassA {
		left: 74px;
	}

	.compassB {
		bottom: 0px;
		left: 74px;
	}

	.compassC {
		top: 65px;
	}

	.compassD {
		top: 65px;
		left: 145px;
	}

	.avoidContainer {
		position: absolute;
		top: 50%;
		width: 25%;
		height: 25%;
		z-index: 1;
		transition: all .3s linear;

		.centerPointer {
			width: 5%;
			height: 5%;
			background-color: #faebd7;
			position: absolute;
			top: 50%;
			left: 50%;
			transform: translate(-50%, -50%);

			&::before {
				content: "";
				display: block;
				position: absolute;
				top: 50%;
				left: 50%;
				transform: translate(-50%, -50%);
				border-radius: 50%;
				border: 1px solid hsla(0, 0%, 100%, .3);
				width: 1040%;
				height: 1040%;
			}

			&::after {
				content: "";
				display: block;
				position: absolute;
				top: 50%;
				left: 50%;
				transform: translate(-50%, -50%);
				border-radius: 50%;
				border: 1px solid hsla(0, 0%, 100%, .3);
				width: 520%;
				height: 520%;
			}
		}
	}

	.value {
		top: 45.5%;
		width: 30%;
		height: 60%;
		background-image: url('../../assets/luopanzj.png');
		background-size: 100% 100%;

		position: absolute;
		left: 50%;
		transform: translate(-50%, -50%) rotate(var(--deg));
		transition: all .3s linear;
	}

	.a-left {
		left: 25%;
		transform-origin: 150% 50%;
		transform: translate(-50%, -50%);
	}

	.a-right {
		left: 75%;
		transform: translate(-50%, -50%) rotate(var(--deg));
		transform-origin: -50% 50%;
		transform: translate(-50%, -50%);
	}
}

.avoidValue {
	width: 81%;
	height: 81%;
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	border-radius: 50%;
	background-color: rgba(0, 0, 0, .3);
	overflow: hidden;

	.snip {
		border-right: 500px solid transparent;
		border-left: 500px solid transparent;
		border-top: 200px solid var(--color);
		border-bottom: 200px solid transparent;
		transform: translate(-500px, -200px) rotate(0deg);
		position: absolute;
		width: 0;
		height: 0;
		top: 50%;
		left: 50%;
		transition: all .3s linear;
		border-radius: 100%;

		&:first-child {
			transform: translate(-140px, -200px) rotate(0deg);
		}
	}
}

.pointer {
	position: absolute;
	left: 50%;
	transform: translateX(-50%);
	width: 2px;
	height: .8rem;
	top: -.4rem;
	background-color: #1dcb63;
	z-index: 1;

	.value {
		top: -.8rem;
		line-height: 1;
		display: flex;
		justify-content: center;
		align-items: center;
		color: #1dcb63;
		position: absolute;
		left: 50%;
		transform: translateX(-50%);
	}
}

.yaw-circle {
	position: absolute;
	// top: 50%;
	// left: 50%;
	border-radius: 50%;
	transition: all .3s linear;
	background-size: 100% 100%;
	transform: translate(-50%, -50%) rotate(var(--deg));
	width: 100%;
	height: 100%;
	background-image: url('https://yukong.live/static/img/切图_圆盘.87d1934.png');
}

.roll-circle {
	width: 86%;
	height: 86%;
	transform: translate(-50%, -50%);
	background-image: url('https://yukong.live/static/img/切图_蓝色.8fe7782.png');
	opacity: .3;

	position: absolute;
	top: 50%;
	left: 50%;
	border-radius: 50%;
	transition: all .3s linear;
	background-size: 100% 100%;
}

.pitch-circle {
	top: 50%;
	width: 80%;
	height: 80%;
	border-radius: 50%;
	overflow: hidden;
	background-color: transparent;

	position: absolute;
	left: 50%;
	transform: translate(-50%, -50%) rotate(var(--deg));
	transition: all .3s linear;

	.level {
		width: 100%;
		height: 100%;
		transform: translateY(var(--value));
		transition: all .3s linear;
		background-color: #333;
		background-image: url('https://yukong.live/static/切图_蓝色副本.53be53b.png');
		background-size: 100% 100%;
	}
}

// 底部箭头按钮样式
.toggle-btn {
	position: absolute;
	bottom: 0;
	left: 50%;
	transform: translateX(-50%);
	width: 4vw;
	height: 1.5vw;
	background: rgba(60, 60, 60, .7);
	border-radius: 0 0 .25rem .25rem;
	display: flex;
	justify-content: center;
	align-items: center;
	cursor: pointer;
	pointer-events: auto;
	z-index: 10;
	transition: transform 0.3s ease, background-color 0.3s ease;
}

.dashBoardBox-collapsed ~ .toggle-btn {
	bottom: 0.5vw;
	background: rgba(29, 203, 99, 0.2);
	border-radius: 50%;
	width: 1.5vw;
	height: 1.5vw;
	transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.toggle-btn:hover {
	background: rgba(80, 80, 80, .8);
}

.dashBoardBox-collapsed ~ .toggle-btn:hover {
	background: rgba(29, 203, 99, 0.4);
}

.arrow {
	width: 0;
	height: 0;
	border-left: 0.5vw solid transparent;
	border-right: 0.5vw solid transparent;
	transition: transform 0.3s ease;
}

.arrow-down {
	border-top: 0.5vw solid #1dcb63;
}

.arrow-up {
	border-bottom: 0.5vw solid #1dcb63;
}

.dashBoardBox-collapsed ~ .toggle-btn .arrow {
	// transform: rotate(180deg);
}

.speedIndicator,
.altitudeIndicator,
.dashBoard {
	transition: opacity 0.3s ease 0.1s;
}

.dashBoardBox-collapsed .speedIndicator,
.dashBoardBox-collapsed .altitudeIndicator,
.dashBoardBox-collapsed .dashBoard {
	opacity: 0;
	transition: opacity 0.2s ease;
}
</style>