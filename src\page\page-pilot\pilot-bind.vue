<template>
    <a-layout class="flex-display" style="height: 100vh; background-color: white;">
    <div class="height100 width100 flex-column flex-justify-start flex-align-start">
      <a-row class="pt20 pl20" style="height: 45px; width: 100vw" align="middle">
        <a-col :span="1">
          <span style="color: #1fa3f6" class="fz26"><SendOutlined rotate="90" /></span>
        </a-col>
        <a-col :span="20">
          <span class="fz20 pl5">{{ drone.data.model}}</span>
        </a-col>
        <a-col :span="3">
          <span class="fz16" v-if="drone.data.bound_status"  style="color: #737373">Bound</span>
          <a-button type="primary" @click="onBindDevice" v-else>Bind</a-button>
        </a-col>
      </a-row>
    </div>
    </a-layout>
  </template>
  <style lang="scss" scoped>
  @import '../../styles/dij/index.scss';
  </style>
  <script lang="ts" setup>
  import { SendOutlined } from '@ant-design/icons-vue'
  import { message } from 'ant-design-vue'
  import { onMounted, reactive, ref } from 'vue'
  import { BindBody, bindDevice } from '@/api/manage/index'
  import apiPilot from '@/api/pilot-bridge/index'
  import { ELocalStorageKey,DeviceStatus } from '@/api/enum/index'
  const drone = reactive({
    data: JSON.parse(localStorage.getItem(ELocalStorageKey.Device))
  })
  
  function onBindDevice () {
    const bindParam = {
      device_sn: drone.data.sn,
      user_id: localStorage.getItem(ELocalStorageKey.UserId),
      workspace_id: localStorage.getItem(ELocalStorageKey.WorkspaceId)
    }
    bindDevice(bindParam).then(bindRes => {
      if (bindRes.data.code !== 0) {
        message.error('bind failed:' + bindRes.data.message)
        console.error(bindRes.data.message)
        return
      }
      drone.data.bound_status = true
      localStorage.setItem(ELocalStorageKey.Device, JSON.stringify(drone.data))
    })
  }
  </script>
  