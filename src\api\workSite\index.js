import request from '@/axios';
import { getWorkspaceId } from '@/utils/storage'

export const getList = (current, size, params) => {
    return request({
        url: `/hztech-gong-di/aiAlarmRecord/list`,
        method: 'get',
        params: {
            ...params,
            current,
            size,
            workSpaceId: getWorkspaceId()
        },
    });
};

export const getList2 = (current, size, params) => {
    return request({
        url: `/hztech-gong-di/aiAlarmRecord/page`,
        method: 'get',
        params: {
            ...params,
            current,
            size,
            workSpaceId: getWorkspaceId()
        },
    });
};

export const getHostDeviceList = (current, size, params) => {
    return request({
        url: `/hztech-gong-di/jxai/ai-client/getHostDeviceList`,
        method: 'get',
        params: {
            ...params,
            current,
            size,
        },
    });
};

export const getAlgorithmList = (current, size, params) => {
    return request({
        url: `/hztech-gong-di/jxai/ai-client/page`,
        method: 'get',
        params: {
            ...params,
            current,
            size,
        },
    });
};

export const activateAlgo = (params) => {
    return request({
        url: `/hztech-flight-core/wayline/api/v1/workspaces/${getWorkspaceId()}/batchBindConfigs`,
        method: 'post',
        params
    });
}

export const deactivateAlgo = (params) => {
    return request({
        url: `/hztech-flight-core/wayline/api/v1/workspaces/${getWorkspaceId()}/FlightTaskAiUnBindConfigDTO`,
        method: 'post',
        params
    });
}

export const getAlgoHosts = (current, size, params) => {
    return request({
        url: `/hztech-gong-di/jxai/ai-client/getAlgoHosts`,
        method: 'get',
        params: {
            ...params,
            current,
            size,
        },
    });
};

export const createWork = (params) => {
    return request({
        url: `/hztech-gong-di/firework/create`,
        method: 'post',
        data: {
            ...params,
            workSpaceId: getWorkspaceId()
        }
    });
};

export const getUserList = () => {
    return request({
        url: `/hztech-system/user/page?current=1&size=-1&deptId=`,
        method: 'get'
    });
};

export const getPlayAlgoList = (current, size, params) => {
    return request({
        url: `/hztech-gong-di/jxai/ai-client/playAlgoList`,
        method: 'get',
        params: {
            ...params,
            current,
            size,
        },
    });
};

