<template>
<div>
    <!-- 飞机OSD -->
    <div v-if="osdVisible.visible&& !osdVisible.is_dock" v-drag-window class="osd-panel fz12">
        <div class="drag-title pl5 pr5 flex-align-center flex-row flex-justify-between" style="border-bottom: 1px solid #515151; height: 22%;">
          <span>{{ osdVisible.callsign }}</span>
          
        </div>
        <div class="drag-title pl5 pr5 flex-align-center flex-row flex-justify-between" style=" height: 18%;">
        <span><a class="fz16" style="color: white;position: absolute;right:10px;" @click.stop="() => osdVisible.visible = false"><CloseOutlined /></a></span>
        </div>
        <div style="height: 82%;">
          <div class="flex-column flex-align-center flex-justify-center" style="margin-top: -5px; padding-top: 25px; float: left; width: 60px; background: #2d2d2d;">
            <a-tooltip :title="osdVisible.model">
              <div style="width: 90%;" class="flex-column flex-align-center flex-justify-center">
                <span><a-image :src="M30" :preview="false"/></span>
                <span>{{ osdVisible.model }}</span>
              </div>
            </a-tooltip>
          </div>
          <div class="osd">
              <a-row>
                <a-col span="16" :style="deviceInfo.device.mode_code === EModeCode.未连接 ? 'color: red; font-weight: 700;': 'color: rgb(25,190,107)'">{{ EModeCode[deviceInfo.device.mode_code] }}</a-col>
              </a-row>
              <a-row>
                <a-col span="6">
                  <a-tooltip :title="deviceTitle.hd">
                    <span>HD</span>
                    <span class="ml10">{{ deviceInfo.gateway?.transmission_signal_quality }}</span>
                  </a-tooltip>
                </a-col>
                <a-col span="6">
                  <a-tooltip :title="deviceTitle.rc_capacity_percent">
                    <span><ThunderboltOutlined style="color:rgb(25, 190, 107)" class="fz14"/></span>
                    <span class="ml10">{{ deviceInfo.gateway && deviceInfo.gateway.capacity_percent !== str ? deviceInfo.gateway?.capacity_percent + ' %' : deviceInfo.gateway?.capacity_percent }}</span>
                  </a-tooltip>
                </a-col>

                <a-col span="6">
                  <a-tooltip :title="deviceTitle.drone_capacity_percent">
                    <span><ThunderboltOutlined style="color:rgb(25, 190, 107)" class="fz14"/></span>
                    <span class="ml10">{{ deviceInfo.device.battery.capacity_percent !== str ? deviceInfo.device.battery.capacity_percent + ' %' : deviceInfo.device.battery.capacity_percent }}</span>
                  </a-tooltip>
                </a-col>
              </a-row>
              <a-row>
                <a-tooltip :title="deviceTitle.rtk_fixed">
                  <a-col span="6" class="flex-row flex-align-center flex-justify-start">
                    <span>Fixed</span>
                    <span class="ml10 circle" :style="deviceInfo.device.position_state.is_fixed === 1 ? 'backgroud: rgb(25,190,107);' : ' background: red;'"></span>
                  </a-col>
                </a-tooltip>
                <a-col span="6">
                  <a-tooltip title="GPS">
                    <span>GPS</span>
                    <span class="ml10">{{ deviceInfo.device.position_state.gps_number }}</span>
                  </a-tooltip>
                </a-col>
                <a-col span="6">
                  <a-tooltip :title="deviceTitle.rtk">
                    <span><TrademarkOutlined class="fz14"/></span>
                    <span class="ml10">{{ deviceInfo.device.position_state.rtk_number }}</span>
                  </a-tooltip>
                </a-col>
              </a-row>
              <a-row>
                <a-col span="6">
                  <a-tooltip :title="deviceTitle.gear">
                    <span><ControlOutlined class="fz16" /></span>
                    <span class="ml10">{{ EGear[deviceInfo.device.gear] }}</span>
                  </a-tooltip>
                </a-col>
                <a-col span="6">
                  <a-tooltip :title="deviceTitle.Altitude">
                    <span>ASL</span>
                    <span class="ml10">{{ deviceInfo.device.height === str ? str : Number(deviceInfo.device.height).toFixed(2) + ' m'}}</span>
                  </a-tooltip>
                </a-col>
                <a-col span="6">
                  <a-tooltip :title="deviceTitle.takeoff">
                    <span>ALT</span>
                    <span class="ml10">{{ deviceInfo.device.elevation === str ? str : deviceInfo.device.elevation + ' m' }}</span>
                  </a-tooltip>
                </a-col>
                <a-col span="6">
                  <a-tooltip :title="deviceTitle.distance">
                    <span>H</span>
                    <span class="ml10">{{ deviceInfo.device.home_distance === str ? str : deviceInfo.device.home_distance + ' m' }}</span>
                  </a-tooltip>
                </a-col>
              </a-row>
              <a-row>
                <a-col span="6">
                  <a-tooltip :title="deviceTitle.horizontal_speed">
                    <span>H.S</span>
                    <span class="ml10">{{ deviceInfo.device.horizontal_speed === str ? str : deviceInfo.device.horizontal_speed + ' m/s'}}</span>
                  </a-tooltip>
                </a-col>
                <a-col span="6">
                  <a-tooltip :title="deviceTitle.vertical_speed">
                    <span>V.S</span>
                    <span class="ml10">{{ deviceInfo.device.vertical_speed === str ? str : deviceInfo.device.vertical_speed + ' m/s'}}</span>
                  </a-tooltip>
                </a-col>
                <a-col span="6">
                  <a-tooltip :title="deviceTitle.wind_speed">
                    <span>W.S</span>
                    <span class="ml10">{{ deviceInfo.device.wind_speed+' m/s'}}</span>
                  </a-tooltip>
                </a-col>
              </a-row>
          </div>
        </div>

    </div>
     <!-- 机场OSD -->
     <div v-if="osdVisible.visible&& osdVisible.is_dock"  v-drag-window class="osd-panel fz12">
      <div class="drag-title fz16 pl5 pr5 flex-align-center flex-row flex-justify-between" style="border-bottom: 1px solid #515151; height: 10%;">
        <span>{{ osdVisible.gateway_callsign }}</span>
      </div>
      <span><a style="color: white; position: absolute; top: 5px; right: 5px;" @click="() => osdVisible.visible = false"><CloseOutlined /></a></span>
        <!-- 机场 -->
      <div class ="flex-display" style="border-bottom: 1px solid #515151;">
        <div class="flex-column flex-align-stretch flex-justify-center" style="width: 60px; background: #2d2d2d;">
          <a-tooltip :title="osdVisible.model">
            <div class="flex-column  flex-align-center flex-justify-center" style="width: 90%;">
              <span><RobotFilled style="font-size: 48px;"/></span>
              <span class="mt10">Dock</span>
            </div>
          </a-tooltip>
        </div>
        <div class="osd flex-1" style="flex: 1">
            <a-row>
              <a-col span="16" :style="deviceInfo.dock.basic_osd?.mode_code === EDockModeCode.未连接 ? 'color: red; font-weight: 700;': 'color: rgb(25,190,107)'">
                {{ EDockModeCode[deviceInfo.dock.basic_osd?.mode_code] }}</a-col>
            </a-row>
            <a-row>
              <a-col span="12">
                <a-tooltip :title="dockTitle.acc_time">
                  <span><HistoryOutlined /></span>
                  <span class="ml10">
                    <span v-if="deviceInfo.dock.work_osd?.acc_time >= 2592000"> {{ Math.floor(deviceInfo.dock.work_osd?.acc_time / 2592000) }}m </span>
                    <span v-if="(deviceInfo.dock.work_osd?.acc_time % 2592000) >= 86400"> {{ Math.floor((deviceInfo.dock.work_osd?.acc_time % 2592000) / 86400) }}d </span>
                    <span v-if="(deviceInfo.dock.work_osd?.acc_time % 2592000 % 86400) >= 3600"> {{ Math.floor((deviceInfo.dock.work_osd?.acc_time % 2592000 % 86400) / 3600) }}h </span>
                    <span v-if="(deviceInfo.dock.work_osd?.acc_time % 2592000 % 86400 % 3600) >= 60"> {{ Math.floor((deviceInfo.dock.work_osd?.acc_time % 2592000 % 86400 % 3600) / 60) }}min </span>
                    <span>{{ Math.floor(deviceInfo.dock.work_osd?.acc_time % 2592000 % 86400 % 3600 % 60) }} s</span>
                  </span>
                </a-tooltip>
              </a-col>
              <a-col span="12">
                <a-tooltip :title="dockTitle.activation_time">
                  <span><FieldTimeOutlined /></span>
                  <span class="ml10">{{ new Date((deviceInfo.dock.work_osd?.activation_time ?? 0) * 1000).toLocaleString() }}
                  </span>
                </a-tooltip>
              </a-col>
            </a-row>
            <a-row>
              <a-col span="6">
                <a-tooltip :title="dockTitle.network_state">
                  <span :style="qualityStyle">
                    <span v-if="deviceInfo.dock.basic_osd?.network_state?.type === NetworkStateTypeEnum.FOUR_G"><SignalFilled /></span>
                    <span v-else><GlobalOutlined /></span>
                  </span>
                  <span class="ml10" >{{ deviceInfo.dock.basic_osd?.network_state?.rate }} kb/s</span>
                </a-tooltip>
              </a-col>
              <a-col span="6">
                <a-tooltip :title="dockTitle.job_number">
                  <span><CarryOutOutlined /></span>
                  <span class="ml10" >{{ deviceInfo.dock.work_osd?.job_number }} </span>
                </a-tooltip>
              </a-col>
              <a-col span="6" :title="dockTitle.remain_upload">
                <a-tooltip title="媒体文件上传">
                  <span><CloudUploadOutlined class="fz14"/></span>
                  <span class="ml10">{{ deviceInfo.dock.link_osd?.media_file_detail?.remain_upload }}</span>
                </a-tooltip>
              </a-col>
              <a-col span="6">
                <a-tooltip>
                  <template #title>
                    <p>{{dockTitle.total}}: {{ deviceInfo.dock.basic_osd?.storage?.total }}</p>
                    <p>{{dockTitle.used}}: {{ deviceInfo.dock.basic_osd?.storage?.used  }}</p>
                  </template>
                  <span><FolderOpenOutlined /></span>
                  <span class="ml10" v-if="deviceInfo.dock.basic_osd?.storage?.total > 0">
                    <el-progress type="circle" :width="20" :percentage="deviceInfo.dock.basic_osd?.storage?.used * 100/ deviceInfo.dock.basic_osd?.storage?.total"
                      :stroke-width="2" :show-text="showprogress" :color="deviceInfo.dock.basic_osd?.storage?.used * 100 / deviceInfo.dock.basic_osd?.storage?.total > 80 ? 'red' : '#00ee8b' "/>
                  </span>
                </a-tooltip>
              </a-col>
            </a-row>
            <a-row>
              <a-col span="6">
                <a-tooltip :title="dockTitle.wind_speed">
                  <span>W.S</span>
                  <span class="ml10">{{ (deviceInfo.dock.basic_osd?.wind_speed ?? str) + ' m/s'}}</span>
                </a-tooltip>
              </a-col>
              <a-col span="6">
                <a-tooltip :title="dockTitle.rainfall">
                  <span>🌧</span>
                  <span class="ml10">{{ RainfallEnum[deviceInfo.dock.basic_osd.rainfall] }}</span>
                </a-tooltip>
              </a-col>
              <a-col span="6">
                <a-tooltip :title="dockTitle.environment_temperature">
                  <span>°C</span>
                  <span class="ml10">{{ deviceInfo.dock.basic_osd?.environment_temperature }}</span>
                </a-tooltip>
              </a-col>
              <a-col span="6">
                <a-tooltip :title="dockTitle.dock_temperature">
                  <span>°C</span>
                  <span class="ml10">{{ deviceInfo.dock.basic_osd?.temperature }}</span>
                </a-tooltip>
              </a-col>
            </a-row>
            <a-row>
              <a-col span="6">
                <a-tooltip :title="dockTitle.dock_humidity">
                  <span>💦</span>
                  <span class="ml10">{{ deviceInfo.dock.basic_osd?.humidity }}</span>
                </a-tooltip>
              </a-col>
              <a-col span="6">
                <a-tooltip :title="dockTitle.working_voltage">
                  <span style="border: 1px solid; border-radius: 50%; width: 18px; height: 18px; line-height: 16px; text-align: center; float: left;">V</span>
                  <span class="ml10">{{ (deviceInfo.dock.work_osd?.working_voltage ?? str) + ' mV' }}</span>
                </a-tooltip>
              </a-col>
              <a-col span="6">
                <a-tooltip :title="dockTitle.working_current">
                  <span style="border: 1px solid; border-radius: 50%; width: 18px; height: 18px; line-height: 15px; text-align: center; float: left;" >A</span>
                  <span class="ml10">{{ (deviceInfo.dock.work_osd?.working_current ?? str) + ' mA' }}</span>
                </a-tooltip>
              </a-col>
              <a-col span="6">
                <a-tooltip :title="dockTitle.drone_in_dock">
                  <span><RocketOutlined /></span>
                  <span class="ml10">{{ deviceInfo.dock.basic_osd?.drone_in_dock }}</span>
                </a-tooltip>
              </a-col>
            </a-row>
            <a-row class="p5">
              <a-col span="24">
                <a-button type="primary" :disabled="dockControlPanelVisible" size="small" @click="setDockControlPanelVisible(true)">
                  操作
                </a-button>
              </a-col>
            </a-row>
            <!-- 机场控制面板 -->
            <DockControlPanel v-if="dockControlPanelVisible" :sn="osdVisible.gateway_sn"  :deviceInfo="deviceInfo" @close-control-panel="onCloseControlPanel">
            </DockControlPanel>
        </div>
      </div>
      <!--  飞机-->
      <div class ="flex-display">
        <div class="flex-column flex-align-stretch flex-justify-center" style="width: 60px;  background: #2d2d2d;">
          <a-tooltip :title="osdVisible.model">
            <div style="width: 90%;" class="flex-column flex-align-center flex-justify-center">
              <span><a-image :src="M30" :preview="false"/></span>
              <span>M30</span>
            </div>
          </a-tooltip>
        </div>
        <div class="osd flex-1">
            <a-row>
              <a-col span="16" :style="!deviceInfo.device || deviceInfo.device?.mode_code === EModeCode.未连接 ? 'color: red; font-weight: 700;': 'color: rgb(25,190,107)'">
                {{ !deviceInfo.device ? EModeCode[EModeCode.未连接] : EModeCode[deviceInfo.device?.mode_code] }}</a-col>
            </a-row>
            <a-row>
              <a-col span="6">
                <a-tooltip :title="deviceTitle.upward_quality">
                  <span><SignalFilled /><ArrowUpOutlined style="font-size: 9px; vertical-align: top;" /></span>
                  <span class="ml10">{{ deviceInfo.dock.link_osd?.sdr?.up_quality }}</span>
                </a-tooltip>
              </a-col>
              <a-col span="6">
                <a-tooltip :title="deviceTitle.downward_quality">
                  <span><SignalFilled /><ArrowDownOutlined style="font-size: 9px; vertical-align: top;" /></span>
                  <span class="ml10">{{ deviceInfo.dock.link_osd?.sdr?.down_quality }}</span>
                </a-tooltip>
              </a-col>
              <a-col span="6">
                <a-tooltip :title="deviceTitle.capacity_percent">
                  <span><ThunderboltOutlined style="color:rgb(25, 190, 107)" class="fz14"/></span>
                  <span class="ml10">{{ deviceInfo.device && deviceInfo.device.battery.capacity_percent !== str ? deviceInfo.device?.battery.capacity_percent + ' %' : str }}</span>
                </a-tooltip>
              </a-col>
              <a-col span="6">
                <a-tooltip>
                  <template #title>
                    <p>{{deviceTitle.total}}: {{ deviceInfo.device?.storage?.total }}</p>
                    <p>{{deviceTitle.used}}: {{ deviceInfo.device?.storage?.used  }}</p>
                  </template>
                  <span><FolderOpenOutlined /></span>
                  <span class="ml10" v-if="deviceInfo.device?.storage?.total > 0">
                    <el-progress type="circle" :width="20" :percentage="deviceInfo.device?.storage?.used * 100/ deviceInfo.device?.storage?.total"
                      :strokeWidth="2"    :stroke-width="2" :show-text="showprogress" :strokeColor="deviceInfo.device?.storage?.used * 100 / deviceInfo.device?.storage?.total > 80 ? 'red' : '#00ee8b' "/>
                  </span>
                </a-tooltip>
              </a-col>
            </a-row>
            <a-row>
              <a-tooltip :title="deviceTitle.rtk_fixed">
                <a-col span="6" class="flex-row flex-align-center flex-justify-start">
                  <span>Fixed</span>
                  <span class="ml10 circle" :style="deviceInfo.device?.position_state.is_fixed === 1 ? 'backgroud: rgb(25,190,107);' : ' background: red;'"></span>
                </a-col>
              </a-tooltip>
              <a-col span="6">
                <a-tooltip title="GPS">
                  <span>GPS</span>
                  <span class="ml10">{{ deviceInfo.device ? deviceInfo.device.position_state.gps_number : str }}</span>
                </a-tooltip>
              </a-col>
              <a-col span="6">
                <a-tooltip :title="deviceTitle.rtk">
                  <span><TrademarkOutlined class="fz14"/></span>
                  <span class="ml10">{{ deviceInfo.device ? deviceInfo.device.position_state.rtk_number : str }}</span>
                </a-tooltip>
              </a-col>
            </a-row>
            <a-row>
              <a-col span="6">
                <a-tooltip :title="deviceTitle.gear">
                  <span><ControlOutlined class="fz16" /></span>
                  <span class="ml10">{{ deviceInfo.device ? EGear[deviceInfo.device?.gear] : str }}</span>
                </a-tooltip>
              </a-col>
              <a-col span="6">
                <a-tooltip :title="deviceTitle.Altitude">
                  <span>ASL</span>
                  <span class="ml10">{{ !deviceInfo.device || deviceInfo.device.height === str ? str : deviceInfo.device?.height.toFixed(2) + ' m'}}</span>
                </a-tooltip>
              </a-col>
              <a-col span="6">
                <a-tooltip :title="deviceTitle.takeoff">
                  <span>ALT</span>
                  <span class="ml10">{{ !deviceInfo.device || deviceInfo.device.elevation === str ? str : deviceInfo.device?.elevation.toFixed(2) + ' m' }}</span>
                </a-tooltip>
              </a-col>
              <a-col span="6">
                <a-tooltip :title="deviceTitle.distance">
                  <span style="border: 1px solid; border-radius: 50%; width: 16px; height: 16px; line-height: 15px; text-align: center;  display: block; float: left;" >H</span>
                  <span class="ml10">{{ !deviceInfo.device || deviceInfo.device.home_distance === str ? str : deviceInfo.device?.home_distance.toFixed(2) + ' m' }}</span>
                </a-tooltip>
              </a-col>
            </a-row>
            <a-row>
              <a-col span="6">
                <a-tooltip :title="deviceTitle.horizontal_speed">
                  <span>H.S</span>
                  <span class="ml10">{{ !deviceInfo.device || deviceInfo.device?.horizontal_speed === str ? str : deviceInfo.device?.horizontal_speed.toFixed(2) + ' m/s'}}</span>
                </a-tooltip>
              </a-col>
              <a-col span="6">
                <a-tooltip :title="deviceTitle.vertical_speed">
                  <span>V.S</span>
                  <span class="ml10">{{ !deviceInfo.device || deviceInfo.device.vertical_speed === str ? str : deviceInfo.device?.vertical_speed.toFixed(2) + ' m/s'}}</span>
                </a-tooltip>
              </a-col>
              <a-col span="6">
                <a-tooltip :title="deviceTitle.wind_speed">
                  <span>W.S</span>
                  <span class="ml10">{{ !deviceInfo.device || deviceInfo.device.wind_speed === str ? str : (deviceInfo.device.wind_speed / 10).toFixed(2) + ' m/s'}}</span>
                </a-tooltip>
              </a-col>
              <a-col span="6">
                <a-tooltip :title="deviceTitle.camera_mode">
                  <span><CameraOutlined class="fz16"/></span>
                  <span class="ml10">
                     {{
                        deviceInfo.device && deviceInfo.device.cameras ? (deviceInfo.device.cameras[0].camera_mode == 0 ? '拍照' : deviceInfo.device.cameras[0].camera_mode == 1
                         ? '录像' : deviceInfo.device.cameras[0].camera_mode == 2 ? '智能低光' : '全景拍照') : str
                    }}
                  </span>
                </a-tooltip>
              </a-col>
            </a-row>
        </div>
      </div>
      <div class="battery-slide" v-if="deviceInfo.device && deviceInfo.device.battery.remain_flight_time !== 0" style="border: 1px solid red">
        <div style="background: #535759;" class="width-100"></div>
        <div class="capacity-percent" :style="{ width: deviceInfo.device.battery.capacity_percent + '%'}"></div>
        <div class="return-home" :style="{ width: deviceInfo.device.battery.return_home_power + '%'}"></div>
        <div class="landing" :style="{ width: deviceInfo.device.battery.landing_power + '%'}"></div>
        <div class="white-point" :style="{ left: deviceInfo.device.battery.landing_power + '%'}"></div>
        <div class="battery" :style="{ left: deviceInfo.device.battery.capacity_percent + '%' }">
          {{ Math.floor(deviceInfo.device.battery.remain_flight_time / 60) }}:
          {{ 10 > (deviceInfo.device.battery.remain_flight_time % 60) ? '0' : ''}}{{deviceInfo.device.battery.remain_flight_time % 60 }}
        </div>
      </div>
      <!-- 飞行指令 -->
      <DroneControlPanel :sn="osdVisible.gateway_sn" :deviceInfo="deviceInfo" :payloads="osdVisible.payloads"></DroneControlPanel>
    </div>
    <!-- liveview -->
    <div class="liveview" v-if="livestreamOthersVisible" v-drag-window>
      <div style="height: 40px; width: 100%" class="drag-title"></div>
      <a style="position: absolute; right: 10px; top: 10px; font-size: 16px; color: white;" @click="closeLivestreamOthers"><CloseOutlined /></a>
      <LivestreamOthers />
    </div>

</div>

</template>
<script lang="ts">
import { computed, defineComponent, onMounted, reactive, ref, watch } from 'vue'
import store from '@/store';
import {
  BorderOutlined, LineOutlined, CloseOutlined, ControlOutlined, TrademarkOutlined, ArrowDownOutlined, CameraOutlined,
  ThunderboltOutlined, SignalFilled, GlobalOutlined, HistoryOutlined, CloudUploadOutlined, RocketOutlined,
  FieldTimeOutlined, CloudOutlined, CloudFilled, FolderOpenOutlined, RobotFilled, ArrowUpOutlined, CarryOutOutlined
} from '@ant-design/icons-vue'
import { EGear, EModeCode, EDockModeCode,} from '@/types/device'
import { EDeviceTypeName } from '../../types'
import {deviceTitle,dockTitle,NetworkStateQualityEnum,NetworkStateTypeEnum,RainfallEnum,DrcStateEnum,FourGLinkStateEnum,SdrLinkStateEnum,LinkWorkModeEnum,DroneBatteryStateEnum} from '@/api/enum/index'
import { gcj02towgs84, wgs84togcj02 } from '@/vendors/coordtransform'
import { useConnectMqtt } from '../g-map/use-connect-mqtt'
import { MapDoodleType, MapElementEnum } from '@/constants/map'
import { deviceTsaUpdate } from '@/hooks/use-g-map-tsa'
import M30 from '@/assets/m30.png'
import { useDockControl } from '../g-map/use-dock-control'
import DroneControlPanel from '../g-map/DroneControlPanel.vue'
import DockControlPanel from '../g-map/DockControlPanel.vue'
import LivestreamOthers from '../livestream-others.vue'
export default defineComponent({
  components: {
    LivestreamOthers,BorderOutlined, LineOutlined, CloseOutlined, ControlOutlined, TrademarkOutlined, ArrowDownOutlined,
  ThunderboltOutlined, SignalFilled, GlobalOutlined, HistoryOutlined, CloudUploadOutlined, RocketOutlined, CameraOutlined,
  FieldTimeOutlined, CloudOutlined, CloudFilled, FolderOpenOutlined, RobotFilled, ArrowUpOutlined, CarryOutOutlined,DockControlPanel,DroneControlPanel
  },
  name: 'djiinfo',
  props: {},
  setup () {
    const deviceTsaUpdateHook = deviceTsaUpdate()
    const osdVisible = computed(() => {
      return store.state.dock.osdVisible
    })
    const state = reactive({
      currentType: '',
      coverIndex: 0,
      isFlightArea: false,
    })

    const str = '--'
    const drawVisible = computed(() => {
      return store.state.dock.drawVisible
    })
    const livestreamOthersVisible = computed(() => {
      return store.state.dock.livestreamOthersVisible
    })
    const livestreamAgoraVisible = computed(() => {
      return store.state.dock.livestreamAgoraVisible
    })

    const deviceInfo = reactive({
      gateway: {
        capacity_percent: str,
        transmission_signal_quality: str,
      },
      dock: {
        basic_osd: {
          network_state: {
          type:null, // 示例值
          quality: null, // 示例值
          rate: null, // 示例值（可能是比特率）
        },
        drone_charge_state: {
          state: 1, // 示例值，可能代表充电中
          capacity_percent: 90,
        },
        drone_in_dock: true,
        rainfall:0,
        wind_speed: 5.0, // 米/秒
        environment_temperature: 22.5,
        temperature: 25.0, // 设备温度
        humidity: 60,
        latitude: 34.0522,
        longitude: -118.2437,
        height: 10.0, // 米
        alternate_land_point: {
          latitude: 34.0530,
          longitude: -118.2440,
          height: 5.0,
          safe_land_height: 3.0,
          is_configured: 1, // 假设1表示已配置
        },
        first_power_on: 1609459200000, // 示例时间戳（2021年1月1日）
        positionState: {
          gps_number: 4, // 示例值
          is_fixed: 1, // 假设1表示已固定
          rtk_number: 0, // 示例值
          is_calibration: 0, // 假设0表示未校准
          quality: 3, // 示例值
        },
        storage: {
          total: 1024, // 示例总容量
          used: 512, // 示例已用量
        },
        mode_code: 1, // 示例模式代码
        cover_state: 1, // 示例覆盖状态
        supplement_light_state: 0, // 示例补光灯状态
        emergency_stop_state: 0, // 示例紧急停止状态
        air_conditioner: {
          air_conditioner_state: 0, // 示例空调状态
          switch_time: 1609459200000, // 示例切换时间（与first_power_on相同，仅为示例）
        },
        battery_store_mode: 0, // 示例电池保养模式
        alarm_state: 0, // 示例报警状态
        putter_state: 1, // 示例放置机构状态
        sub_device: {
          device_sn: '',
          device_model_key: '',
          device_online_status: 1, // 假设1表示在线
          device_paired: 1, // 假设1表示已配对
        },
        },
        link_osd: {
          drc_state: DrcStateEnum.DISCONNECT, // 示例值  
          flighttask_prepare_capacity: 100, // 示例值  
          flighttask_step_code: 1, // 示例值  
          media_file_detail: {
            remain_upload: 50, // 示例值  
          },
          sdr: {
            up_quality: 'HD', // 示例值  
            down_quality: 'HD', // 示例值  
            frequency_band: 2.4, // 示例值  
          },
          wireless_link: {
            dongle_number: 2, // dongle 数量  
            '4g_link_state': FourGLinkStateEnum.OPEN, // 4G链路状态  
            sdr_link_state: SdrLinkStateEnum.CLOSE, // SDR链路连接状态  
            link_workmode: LinkWorkModeEnum.SDR, // 图传链路模式  
            sdr_quality: 4, // SDR信号质量 0-5  
            '4g_quality': 3, // 4G信号质量 0-5  
            '4g_freq_band': 800, // 4G频率带  
            '4g_gnd_quality': 3, // 4G地面信号质量  
            '4g_uav_quality': 4, // 4G无人机信号质量  
            sdr_freq_band: 2.4, // SDR频率带  
          },
        },
        work_osd: {
          job_number: 0,
          acc_time:null,
          activation_time: 0,
          maintain_status: {
            maintain_status_array:[]
          },
          electric_supply_voltage: null,
          working_voltage: null,
          working_current: null,
          backup_battery: {
            voltage: null,
            temperature: null,
            switch: null,
          },
          drone_battery_maintenance_info: { // 飞行器电池保养信息
            maintenance_state: DroneBatteryStateEnum.MaintenanceInProgress, // 保养状态
            maintenance_time_left: 0, // 电池保养剩余时间(小时)
          }
        }
      },
      device: {
        gear: -1,
        mode_code: EModeCode.未连接,
        height: str,
        home_distance: str,
        horizontal_speed: str,
        vertical_speed: str,
        wind_speed: 0,
        wind_direction: str,
        elevation: str,
        position_state: {
          gps_number: str,
          is_fixed: 0,
          rtk_number: str
        },
        battery: {
          capacity_percent: str,
          landing_power: str,
          remain_flight_time: 0,
          return_home_power: str,
        },
        latitude: 0,
        longitude: 0,
      }


     })
    const qualityStyle = computed(() => {
      if (deviceInfo.dock.basic_osd?.network_state?.type === NetworkStateTypeEnum.ETHERNET ||
        (deviceInfo.dock.basic_osd?.network_state?.quality || 0) > NetworkStateQualityEnum.FAIR) {
        return 'color: #00ee8b'
      }
      if ((deviceInfo.dock.basic_osd?.network_state?.quality || 0) === NetworkStateQualityEnum.FAIR) {
        return 'color: yellow'
      }
      return 'color: red'
    })
    const showprogress = ref(false)

    watch(() => store.state.dock.deviceState,
     data => {
      //遥控器
      if (data.currentType === EDeviceTypeName.Gateway && data.gatewayInfo[data.currentSn]) {
        const coordinate = wgs84togcj02(data.gatewayInfo[data.currentSn].longitude, data.gatewayInfo[data.currentSn].latitude)
        deviceTsaUpdateHook.moveTo(data.currentSn, coordinate[0], coordinate[1])
        if (osdVisible.value.visible && osdVisible.value.gateway_sn !== '') {
          deviceInfo.gateway = data.gatewayInfo[osdVisible.value.gateway_sn]
        }
      }
//飞行器数据状态
      if (data.currentType === EDeviceTypeName.Aircraft && data.deviceInfo[data.currentSn]) {
        const coordinate = wgs84togcj02(data.deviceInfo[data.currentSn].longitude, data.deviceInfo[data.currentSn].latitude)
        deviceTsaUpdateHook.moveTo(data.currentSn, coordinate[0], coordinate[1])
        if (osdVisible.value.visible && osdVisible.value.sn !== '') {
          deviceInfo.device = data.deviceInfo[osdVisible.value.sn]
        }
      }
      //机场数据
      if (data.currentType === EDeviceTypeName.Dock && data.dockInfo[data.currentSn]) {
        const coordinate = wgs84togcj02(data.dockInfo[data.currentSn].basic_osd?.longitude, data.dockInfo[data.currentSn].basic_osd?.latitude)
        deviceTsaUpdateHook.initMarker(EDeviceTypeName.Dock, EDeviceTypeName[EDeviceTypeName.Dock], data.currentSn, coordinate[0], coordinate[1])
        if (osdVisible.value.visible) {
          deviceInfo.dock = data.dockInfo[osdVisible.value.gateway_sn]
          deviceInfo.device = data.deviceInfo[deviceInfo.dock.basic_osd.sub_device?.device_sn ?? osdVisible.value.sn]
        }
      }
    }, {
      deep: true
    }
    )
    function closeLivestreamOthers () {
      store.commit('SET_LIVESTREAM_OTHERS_VISIBLE', false)
    }
    function closeLivestreamAgora () {
      store.commit('SET_LIVESTREAM_AGORA_VISIBLE', false)
    }
   function closeOSD(){
      osdVisible.value.visible = false
    }
    // dock 控制面板
    const {
      dockControlPanelVisible,
      setDockControlPanelVisible,
      onCloseControlPanel,
    } = useDockControl()
      // 连接或断开drc
    // useConnectMqtt()

    return {
      osdVisible,
      drawVisible,
      state,
      deviceInfo,
      EGear,
      EModeCode,
      str,
      EDockModeCode,
      qualityStyle,
      livestreamOthersVisible,
      livestreamAgoraVisible,
      M30,
      dockControlPanelVisible,
      setDockControlPanelVisible,
      onCloseControlPanel,
      NetworkStateTypeEnum,
      NetworkStateQualityEnum,
      RainfallEnum,
      DrcStateEnum,
      FourGLinkStateEnum,
      SdrLinkStateEnum,
      LinkWorkModeEnum,
      DroneBatteryStateEnum,
      closeLivestreamOthers,
      closeLivestreamAgora,
      showprogress,
      dockTitle,
      deviceTitle,
      closeOSD
    }
  },
  })

</script>
<style>
  .ant-btn[disabled] {
    cursor: not-allowed;
}
.ant-btn-primary[disabled], .ant-btn-primary[disabled]:hover, .ant-btn-primary[disabled]:focus, .ant-btn-primary[disabled]:active {
    color: rgba(0, 0, 0, 0.25);
    background: #f5f5f5;
    border-color: #d9d9d9;
    text-shadow: none;
    box-shadow: none;
}
</style>