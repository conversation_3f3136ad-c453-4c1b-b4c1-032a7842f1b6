import { App, reactive } from 'vue'
import { CESIUM_CONFIG } from '@/constants/index'
import store from '@/store'
// import { AMapImageryProvider, BaiduImageryProvider, GeoVisImageryProvider } from '@cesium-china/cesium-map'
export function useGMapManage() {
  const state = reactive({
    viewer: null, // Cesium Viewer对象
    mouseTool: null,
    cockpitMap: null,
  })

  async function initMap(container: string, app: App, center?: any, type?: string) {
    // 设置Cesium Ion访问令牌
    Cesium.Ion.defaultAccessToken = CESIUM_CONFIG.accessToken

    // 创建Cesium Viewer实例
    state.viewer = new Cesium.Viewer(container, {
      timeline: false,
      geocoder: false,
      animation: false,
      homeButton: false,
      baseLayerPicker: false,
      sceneModePicker: false,
      fullscreenButton: false,
      navigationHelpButton: false,
      selectionIndicator: false,
      infoBox: false,
      terrainProvider: await Cesium.createWorldTerrain({
        requestVertexNormals: true
      })
    })

    // 监听瓦片数据加载完毕事件
    let tilesLoadingCount = 0;
    state.viewer.scene.globe.tileLoadProgressEvent.addEventListener((count) => {
      if (count > 0) {
        tilesLoadingCount = count;
      } else if (tilesLoadingCount > 0 && count === 0) {
        // 更新Vuex中的地形加载状态
        store.commit('SET_TERRAIN_LOADED', true)
        console.log('瓦片数据加载完毕')
        tilesLoadingCount = 0;
      }
    });

    // 开启抗锯齿
    state.viewer.scene.postProcessStages.fxaa.enabled = true;
    // state.viewer.scene.globe.depthTestAgainstTerrain = true;

    // 设置相机位置
    const defaultCenter = [120.23670702899278, 30.302041927098482]
    const targetCenter = center || defaultCenter

    state.viewer.camera.setView({
      destination: Cesium.Cartesian3.fromDegrees(targetCenter[0], targetCenter[1], 500),
      orientation: {
        heading: Cesium.Math.toRadians(0.0), // 正东，默认北
        pitch: Cesium.Math.toRadians(-90), // 向正下方看
        roll: 0.0, // 左右
      }
    })

    // 加载高德地图图层
    // 加载高德影像底图
    // const gdBasicLayer = new Cesium.ImageryLayer(new Cesium.UrlTemplateImageryProvider({
    //   url: 'https://webst02.is.autonavi.com/appmaptile?style=6&x={x}&y={y}&z={z}',
    //   minimumLevel: 1,
    //   maximumLevel: 18,
    // }))
    // // 加载高德地图影像地理标签
    // const gdLabelLayer = new Cesium.ImageryLayer(new Cesium.UrlTemplateImageryProvider({
    //   url: 'http://webst02.is.autonavi.com/appmaptile?x={x}&y={y}&z={z}&lang=zh_cn&size=1&scale=1&style=8',
    //   minimumLevel: 1,
    //   maximumLevel: 18,
    // }))

    // state.viewer.imageryLayers.add(gdBasicLayer)
    // state.viewer.imageryLayers.add(gdLabelLayer)

    // 加载高德地图影像纠偏
    // var options = {
    //   style: 'img', // style: img、elec、cva
    //   crs: 'WGS84', // 使用84坐标系，默认为：GCJ02
    //   minimumLevel: 1,
    //   maximumLevel: 18,
    // }
    // state.viewer.imageryLayers.add(new Cesium.ImageryLayer(new Cesium.AMapImageryProvider(options)))

    //配置并创建天地图Web瓦片服务影像提供者
    // const tianditu = new Cesium.WebMapTileServiceImageryProvider({
    //   url: 'http://{s}.tianditu.gov.cn/img_w/wmts?service=wmts&request=GetTile&version=1.0.0&LAYER=img&tileMatrixSet=w&TileMatrix={TileMatrix}&TileRow={TileRow}&TileCol={TileCol}&style=default&format=tiles&tk=' + CESIUM_CONFIG.tdtKey,
    //   layer: 'img_w',
    //   style: 'default',
    //   format: 'tiles',
    //   tileMatrixSetID: 'w',
    //   subdomains: ['t0', 't1', 't2', 't3', 't4', 't5', 't6', 't7'],
    //   maximumLevel: 18,
    //   credit: new Cesium.Credit('天地图'),
    // })
    // // 将天地图层添加到观众实例的影像图层集合中
    // state.viewer.imageryLayers.addImageryProvider(tianditu)

    // 加载天地图影像纠偏
    // state.viewer.imageryLayers.add(new Cesium.ImageryLayer(new Cesium.TdtImageryProvider({
    //   style: 'cva', //style: vec、cva、img、cia、ter
    //   key: CESIUM_CONFIG.tdtKey, // 使用配置中的天地图key
    // })))
    // 加载百度地图影像纠偏
    // var options = {
    //   style: 'vec', // style: img、vec、normal、dark
    //   crs: 'WGS84' // 使用84坐标系，默认为：BD09
    // }
    // state.viewer.imageryLayers.add(new Cesium.ImageryLayer( new Cesium.BaiduImageryProvider(options)))

    // 挂在到全局
    if (container === 'cockpitMap') {
      app.config.globalProperties.$cockpitMap = state.viewer
    } else {
      app.config.globalProperties.$viewer = state.viewer
    }
    app.config.globalProperties.$map = state.viewer
  }

  function globalPropertiesConfig(app: App, center?: any, name?: string, type?: string) {
    initMap(name || 'g-container', app, center, type)
  }

  return {
    globalPropertiesConfig,
  }
}
