export const destinationTypeOptions = [
  {label: '协调转弯，不过点，提前转弯', value: 'coordinateTurn'},
  {label: '直线飞行，飞行器到点停', value: 'toPointAndStopWithDiscontinuityCurvature'},
  {label: '曲线飞行，飞行器到点停', value: 'toPointAndStopWithContinuityCurvature'},
  {label: '曲线飞行，飞行器过点不停', value: 'toPointAndPassWithContinuityCurvature'},
]

export const yawAngleModeOptions = [
  {label: '沿航线方向', value: 'followWayline'},
  {label: '手动控制', value: 'manually'},
  {label: '锁定当前偏航角', value: 'fixed'},
  {label: '自定义', value: 'smoothTransition'},
]

export const pitchAngleModeOptions = [
  {label: '手动控制', value: 'manual'},
  {label: '依照每个航点设置', value: 'usePointSetting'},
]

export const completeTheActionOptions = [
  {label: '自动返航', value: 'goHome'},
  {label: '返回航线起始点悬停', value: 'gotoFirstWaypoint'},
  {label: '退出航线模式', value: 'noAction'},
  // {label: '原地降落', value: 'autoLand'},
]

export const isImplementRouteOptions = [
  // {label: '继续执行航线', value: 'goContinue'},
  {label: '退出航线执行失控动作', value: 'executeLostAction'},
]

export const haywireTypeOptions = [
  {label: '返航,飞行器从失控位置飞向起飞点', value: 'goBack'},
  {label: '降落,飞行器从失控位置原地降落', value: 'landing'},
  {label: '悬停,飞行器从失控位置悬停', value: 'hover'},
]

export function matching(item) {
  switch (item.action_actuator_func) {
    case 8: {
      // 区分云台偏航行角、云台俯仰角航点动作,两者action_actuator_func都为8
      if (item.action_actuator_func_params.gimbal_pitch_rotate_enable) {
        item.index = 9
      } else {
        item.index = 8
      }
      break;
    }
    case 9:
    case 10:
    case 11:
    case 12: {
      item.index = item.action_actuator_func + 1;
      break;
    }
    default:
      item.index = item.action_actuator_func;
      break;
  }
}

export function convert(data) {
  const photoSet = []
  data.map((item) => {
    switch (item) {
      case '广角照片':
        photoSet.push('wide')
        break;
      case '变焦照片':
        photoSet.push('zoom')
        break;
      case '红外照片':
        photoSet.push('ir')
        break;
      case '可见光':
        photoSet.push('visable')
        break;
      default:
    }
  })
  photoSet.sort((a, b) => a - b) // 排序
  return photoSet
}

export function converts(data) {
  const photoSet = []
  data.split(',').map((item) => {
    switch (item) {
      case 'wide':
        photoSet.push('广角照片')
        break;
      case 'zoom':
        photoSet.push('变焦照片')
        break;
      case 'ir':
        photoSet.push('红外照片')
        break;
      case 'visable':
        photoSet.push('可见光')
        break;
      default:
    }
  })
  return photoSet
}

export function photoTypeSelect(i) {
  var option = []
  if (i == 'M30') {
    option = ['广角照片', '变焦照片']
  } else if (i == 'M30T') {
    option = ['广角照片', '变焦照片', '红外照片']
  } else if (i == 'M3E') {
    option = []
  } else if (i == 'M3T') {
    option = []
  } else if (i == 'M3D') {
    option = ['可见光']
  } else if (i == 'M3TD') {
    option = ['可见光', '红外照片']
  } else if (i == 'M4E') {
    option = ['可见光']
  } else if (i == 'M4T') {
    option = ['可见光', '红外照片']
  } else if (i == 'M4D') {
    option = ['可见光']
  } else if (i == 'M4TD') {
    option = ['可见光', '红外照片']
  } else {
    option = ['可见光', '红外照片']
  }
  return option
}

export function cameraTypeSelect(i) {
  var option = []
  if (i == 'M30') {
    option = ['广角照片']
  } else if (i == 'M30T') {
    option = ['广角照片', '红外照片']
  } else if (i == 'M3E') {
    option = ['可见光']
  } else if (i == 'M3T') {
    option = ['可见光', '红外照片']
  } else if (i == 'M3D') {
    option = ['可见光']
  } else if (i == 'M3TD') {
    option = ['可见光', '红外照片']
  } else if (i == 'M4E') {
    option = ['可见光']
  } else if (i == 'M4T') {
    option = ['可见光', '红外照片']
  } else if (i == 'M4D') {
    option = ['可见光']
  } else if (i == 'M4TD') {
    option = ['可见光', '红外照片']
  } else {
    option = ['可见光', '红外照片']
  }
  return option
}