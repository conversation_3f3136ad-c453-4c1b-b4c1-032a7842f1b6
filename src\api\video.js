import request from '@/axios';

const HTTP_PREFIX = '/hztech-mediaServer/api'

// 在直播期间直播相机切换
export const Delete = async function (ids) {
  return request({
    url: `${HTTP_PREFIX}/cloud/record/removeMediaFile`,
    method: 'get',
    params: {
      ids
    }
  })
}

// 云端录像列表请求
export const getList = async function (param) {
  return request({
    url: `${HTTP_PREFIX}/cloud/record/list`,
    method: 'get',
    params: param
  })
}