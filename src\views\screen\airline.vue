<template>
  <div class="main">
    <div class="left">
      <div class="search">
        <el-date-picker style="width: 58%;margin-right: 2%;" v-model="searchTime" type="daterange" range-separator="-"
          start-placeholder="开始时间" end-placeholder="结束时间" @change="changeTime" @clear="clearTime" />
        <el-input style="width: 40%;" v-model="params.wayLineName" clearable placeholder="请输入航线名称" />
      </div>

      <div class="list" ref="scrollContainer">
        <table class="flight-table">
          <thead>
            <tr>
              <th v-for="(item, index) in headers" :key="index">{{ item }}</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="(item, index) in flightOptions" :key="index" style="cursor: pointer;"
              @click="clickDetail(item, index)"
              :class="{ 'odd-row': index % 2 === 0, 'even-row': index % 2 !== 0, 'selected-row': selectedIndex === index }">
              <td>{{ item.name }}</td>
              <td>{{ WaylineTypeMap[item.template_types] }}</td>
              <td>{{ DEVICE_NAME[item.drone_model_key] }}</td>
              <td>{{ item.user_name }}</td>
              <td>{{ new Date(item.update_time).toLocaleString() }}</td>
              <td>详情</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <div class="right">
      <div id="g-container" :style="{ width: '100%', height: '100%' }" />

      <airlineMap :wgs84Center="[120.241176, 30.299719]" :placemark="placemark" :take0ffPoint="take0ffPoint"
        :Polygon="Polygon" :saData="saData" :isSet="false" :zoom="14.5" :aerobatModel="session"
        @allPoints="handlePoints" @takeoffPoint="handleTakeoff" @pretreatment="pretreatment" />

      <div class="action" v-if="timeData && timeData.distance && session.routeType === 'waypoint'">
        <div class="title">航点动作</div>
        <ul class="tooltips-content">
          <template v-for="(subItem, indexJ) in dataList">
            <li :key="indexJ" v-if="subItem.action_list?.length">
              <div class="tooltips-container">
                <div class="index">{{ indexJ + 1 }}.</div>
                <div class="item_list">
                  <div class="item" v-for="(item1, index1) in subItem.action_list" :key="index1">
                    <div :class="`img_${item1.index}`" />
                  </div>
                </div>
              </div>
            </li>
          </template>
        </ul>
      </div>

      <div v-if="timeData && timeData.distance" class="leftManage">
        <div class="info-container" v-if="session.routeType !== 'waypoint'">
          <div class="info-item">
            <div class="info-title">面积</div>
            <div class="info-value">{{ timeData?.PolygonArea?.toFixed(2) + '㎡' }}</div>
          </div>
          <div class="info-item">
            <div class="info-title">航线长度</div>
            <div class="info-value">{{ timeData?.distance?.toFixed(1) + 'm' }}</div>
          </div>
          <div class="info-item">
            <div class="info-title">预计总时长</div>
            <div class="info-value">{{ formatSeconds(timeData?.time?.toFixed(0)) || 0 }}</div>
          </div>
          <div class="info-item">
            <div class="info-title">预计总照片数</div>
            <div class="info-value">{{ timeData?.pallcont || 0 }}</div>
          </div>
        </div>
        <div class="info-container" v-if="session.routeType === 'waypoint'">
          <div class="info-item">
            <div class="info-title">航线长度</div>
            <div class="info-value">{{ timeData?.distance + 'm' }}</div>
          </div>
          <div class="info-item">
            <div class="info-title">预计总时长</div>
            <div class="info-value">{{ timeData?.workTime || 0 }}</div>
          </div>
          <div class="info-item">
            <div class="info-title">航点</div>
            <div class="info-value">{{ timeData?.pointCount || 0 }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import moment from 'moment';
import { ref, onMounted, watch, onBeforeUnmount } from 'vue';
import { WayLineRecord } from '@/api/screen';
import { DEVICE_NAME } from '@/types/device';
import { getWayLineDetail } from "@/api/wayline";
import { wgs84togcj02 } from "@/vendors/coordtransform";
import airlineMap from './map/airlineMap.vue';
import { getApp } from "@/root";
import { useGMapManage } from '@/hooks/use-c-map';
import { matching } from "@/utils/common";
import { WaylineTypeMap } from '@/types/task';

let searchTime = ref(null);
let params = ref({
  startTime: '',
  endTime: '',
  wayLineName: '',
  page_size: 25,
  page: 1,
});
let timeData = ref({});
let dataList = ref({});
let flightOptions = ref([]);
let selectedIndex = ref(null);
let headers = ref(['航线名称', '航线类型', '飞行器型号', '创建人', '更新时间', '操作']);
const scrollContainer = ref(null);

const init = () => {
  WayLineRecord(params.value).then(res => {
    if (res.data.code === 0) {
      if (res.data.data.list.length > 0) {
        flightOptions.value.push(...res.data.data.list)
      }
    }
  })
}

const handleScroll = () => {
  if (scrollContainer.value.scrollHeight - scrollContainer.value.scrollTop <= scrollContainer.value.clientHeight + 30) {
    params.value.page++
    init();
  }
}

const session = ref({});
const placemark = ref([]);
const take0ffPoint = ref([]);
const Polygon = ref([]);
const saData = ref([]);
const clickDetail = async (item, index) => {
  selectedIndex.value = index
  const res = await getWayLineDetail(item.id);
  if (res.code !== 0) {
    return;
  } else {
    // 飞行航线转换经纬度
    const arr = [];
    let currentGroup = [];

    res.data.waylineData.placemark.forEach(item => {
      const point = item.point.split(',').map(Number);
      point.push(Number(item.height) || Number(item.execute_height));

      if (item.index == 0) {
        // 当遇到index为0时，如果currentGroup不为空，将其添加到arr中
        if (currentGroup.length > 0) {
          arr.push(currentGroup);
        }
        // 重置当前组并添加当前点
        currentGroup = [point];
      } else {
        currentGroup.push(point);
      }
    });

    // 确保最后一组也被添加到arr中
    if (currentGroup.length > 0) {
      arr.push(currentGroup);
    }

    placemark.value = arr
    Polygon.value = res.data.waylineData?.template_placemark?.polygon

    // 有数据-回显地图起飞点
    if (res.data.waylineData.take_off_ref_point && res.data.waylineData.take_off_ref_point.substring(0, 3) != 'NaN') {
      const array = res.data.waylineData.take_off_ref_point.split(',')
      // 起飞点数据处理-取字符串前两个值，经纬度格式转为gcj02进行上图
      take0ffPoint.value = [parseFloat(array[1]), parseFloat(array[0]), parseFloat(array[2])]
    } else {
      take0ffPoint.value = []
    }
    /**航线库点位*/
    const {
      file_name, device_mode, template_type, image_format, take_off_ref_point,
      fly_to_wayline_mode, take_off_security_height, height_mode, global_height, auto_flight_speed,
      global_transitional_speed, global_waypoint_turn_mode, waypoint_heading_mode,
      gimbal_pitch_mode, finish_action, exit_on_rc_lost, execute_rc_lost_action,
    } = res.data.waylineData
    session.value = {
      name: file_name,
      model: device_mode,//无人机型号
      routeType: template_type,//航线类型,
    }
    console.log('session.value', session.value.routeType);
    // 飞行航线数据统计
    if (template_type === 'waypoint') {
      timeData.value = res.data.wayLineCount
    } else {
      timeData.value = res.data['3ddata']
    }
    saData.value = res.data['3ddata']?.sa_data

    // sessionStorage.removeItem('aerobatModel');
    sessionStorage.setItem('aerobatModel', JSON.stringify(session.value));
    dataList.value = res.data.waylineData.placemark

    dataList.value.forEach((item) => {
      if (item.action_list && item.action_list.length > 0) {
        item.action_list.forEach((item1) => {
          if (item1) matching(item1)
        })
      }
    })
  }
}

const formatSeconds = (seconds) => {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const remainingSeconds = seconds % 60;

  if (hours > 0) {
    return `${hours}h ${minutes}m ${remainingSeconds}s`;
  } else {
    return `${minutes}m ${remainingSeconds}s`;
  }
}

const changeTime = () => {
  if (searchTime.value) {
    let Dates = searchTime.value.map(date => {
      return moment(date).format('YYYY-MM-DD');
    });
    params.value.startTime = Dates[0]
    params.value.endTime = Dates[1]
    params.value.page = 1
    flightOptions.value = []
    init();
  }
}

const clearTime = () => {
  params.value.startTime = ''
  params.value.endTime = ''
  params.value.page = 1
  flightOptions.value = []
  init();
}

onMounted(() => {
  scrollContainer.value.addEventListener('scroll', handleScroll);
  const app = getApp();
  useGMapManage().globalPropertiesConfig(app);
  init();
})

onBeforeUnmount(() => {
  scrollContainer.value.removeEventListener('scroll', handleScroll);
  timeData.value = {};
})

watch(() => params.value.wayLineName, () => {
  flightOptions.value = []
  params.value.page = 1
  init();
})
</script>

<style lang="scss" scoped>
.main {
  width: 100%;
  height: 100%;
  position: relative;

  .left {
    position: absolute;
    left: 1.3vw;
    top: 0;
    width: 35.8vw;
    height: 100%;
  }

  .right {
    position: absolute;
    top: 0;
    right: 0;
    width: 62.6vw;
    height: 100%;
  }
}

.action {
  position: absolute;
  top: 0;
  left: 0;
  background: #333232;
  width: 160px;
  height: 220px;

  .title {
    width: 100%;
    height: 30px;
    line-height: 30px;
    text-align: center;
  }

  .tooltips-content {
    width: 100%;
    height: calc(100% - 30px);
    overflow: auto;

    .tooltips-container {
      width: 100%;
      display: flex;

      .index {
        width: 16%;
        box-sizing: border-box;
      }

      .item_list {
        width: 80%;
        display: flex;
        flex-wrap: wrap;

        .item {
          width: 100%;
        }
      }
    }
  }
}

@for $n from 1 through 13 {
  .img_#{$n} {
    background-image: url('../wayline/img/#{$n}.jpg');
    background-size: 100% 100%;
    display: inline-block;
    width: 30px;
    height: 30px;
  }
}

ul {
  list-style-type: none;
  padding: 10px;
}

.search {
  width: 100%;
  height: 4vh;
}

.list {
  width: 100%;
  height: 85vh;
  margin-top: 6px;
  overflow-y: auto;
  overflow-x: hidden;

  .flight-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    margin-top: 0;
  }

  .flight-table th {
    position: sticky;
    top: 0;
    color: #fff;
    background-color: silver;
    z-index: 1;
  }

  .flight-table th,
  .flight-table td {
    border: none;
    padding: 8px;
    font-size: .7vw;
    text-align: center;
  }

  .flight-table .odd-row {
    height: 0.5vh;
    background: linear-gradient(270deg, rgba(69, 78, 92, 0) 0%, rgba(178, 192, 213, 0.41) 100%);
  }

  .flight-table .even-row {
    height: 0.5vh;
    background: linear-gradient(270deg, rgba(111, 153, 215, 0) 0%, rgba(111, 153, 215, 0.51) 100%);
  }
}

.rightManage {
  position: absolute;
  bottom: 4%;
  right: 28%;
  color: black;
  width: 44%;
  height: 66px;
  z-index: 1;
}

.leftManage {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translate(-50%, 0);
  color: white;
  padding: 10px;
  z-index: 1;
  background: rgba(0, 0, 0, 0.4);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);

  .info-container {
    display: flex;
    flex-wrap: nowrap;
    justify-content: center;
  }

  .info-item {
    padding: 0 15px;
    display: flex;
    flex-direction: column;
    align-items: center;
    min-width: 100px;

    &:not(:last-child) {
      border-right: 1px solid rgba(255, 255, 255, 0.2);
    }
  }

  .info-title {
    font-size: 16px;
    white-space: nowrap;
    margin-bottom: 5px;
  }

  .info-value {
    font-size: 18px;
    text-align: center;
  }
}

.selected-row {
  background-color: #09559a !important;
}

:deep(.el-date-editor.el-input__wrapper) {
  background: rgba(11, 35, 57, 0.4);
  box-shadow: 0 0 0 1px #09559a inset;
}

:deep(.el-date-editor .el-range-input) {
  color: #FFFFFF;
}

:deep(.el-date-editor .el-range__icon) {
  color: #FFFFFF;
}

:deep(.el-select__wrapper) {
  background: rgba(11, 35, 57, 0.4);
  box-shadow: 0 0 0 1px #09559a;
}

:deep(.el-select__placeholder) {
  color: #FFFFFF;
}

:deep(.el-input__wrapper) {
  background: rgba(11, 35, 57, 0.4);
  box-shadow: 0 0 0 1px #09559a;
}

:deep(.el-input__inner) {
  color: #FFFFFF;
}

:deep(.cesium-viewer-bottom) {
  display: none;
}
</style>