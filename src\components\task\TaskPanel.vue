<template>
  <div class="plan-panel-wrapper" :style="componentStyle">
    <a-config-provider :locale="zhCN">
      <a-table size="small" row-key="job_id" :data-source="plansData.data" :columns="columns"
               :pagination="paginationProp" :scroll="{ x: 1500, y: 550 }" @change="refreshData">
        <!-- 执行时间 -->
        <template #duration="{ record }">
          <div class="flex-row" style="white-space: pre-wrap">
            <div>
              <div>{{ formatTaskTime(record.begin_time) }}</div>
              <div>{{ formatTaskTime(record.end_time) }}</div>
            </div>
            <div class="ml10">
              <div>{{ formatTaskTime(record.execute_time) }}</div>
              <div>{{ formatTaskTime(record.completed_time) }}</div>
            </div>
          </div>
        </template>
        <!-- 执行状态 -->
        <template #status="{ record }">
          <div>
            <div class="flex-display flex-align-center">
              <div class="circle-icon" :style="{backgroundColor: formatTaskStatus(record).color}"></div>
              {{ formatTaskStatus(record).text }}
              <a-tooltip v-if="!!record.code" placement="bottom" arrow-point-at-center>
                <template #title>
                  <div>{{ getCodeMessage(record.code) }}</div>
                </template>
                <exclamation-circle-outlined class="ml5" :style="{color: commonColor.WARN, fontSize: '16px' }"/>
              </a-tooltip>
            </div>
            <div v-if="record.status === TaskStatus.Carrying">
              <a-progress :percent="record.progress || 0"/>
            </div>
          </div>
        </template>
        <!-- 任务类型 -->
        <template #taskType="{ record }">
          <div>{{ formatTaskType(record) }}</div>
        </template>
        <!-- 失控动作 -->
        <template #lostAction="{ record }">
          <div>{{ formatLostAction(record) }}</div>
        </template>
        <!-- 航线类型 -->
        <template #waylineType="{ record }">
          <div>{{ formatWaylineType(record) }}</div>
        </template>
        <!-- 媒体上传状态 -->
        <template #media_upload="{ record }">
          <div>
            <div class="flex-display flex-align-center">
              <div class="circle-icon" :style="{backgroundColor: formatMediaTaskStatus(record).color}"></div>
              {{ formatMediaTaskStatus(record).text }}
            </div>
            <div class="pl15">
              {{ formatMediaTaskStatus(record).number }}
              <a-tooltip v-if="formatMediaTaskStatus(record).status === MediaStatus.ToUpload" placement="bottom" arrow-point-at-center>
                <template #title>
                  <div>Upload now</div>
                </template>
                <UploadOutlined class="ml5" :style="{color: commonColor.BLUE, fontSize: '16px' }" @click="onUploadMediaFileNow(record.job_id)"/>
              </a-tooltip>
            </div>
          </div>
        </template>
        <!-- 操作 -->
        <template #action="{ record }">
          <div class="action-area">
            <a-popconfirm v-if="record.status === TaskStatus.Wait"
                          title="您确定要删除航线任务吗？"
                          ok-text="是"
                          cancel-text="否"
                          @confirm="onDeleteTask(record.job_id)"
            >
              <div style="cursor: pointer;margin-right: 10px;">删除</div>
            </a-popconfirm>
            <!--v-if="record.status === 3"-->
            <a style="margin-right: 10px;" @click="rowClick(record)">飞行记录</a><br>
            <a style="margin-right: 10px;" @click="rowClick1(record)">飞行记录视频</a><br>
            <a style="margin-right: 10px;" v-if="record.is_break_point === true" @click="onBreakPointJob(record)">断点续飞</a>
            <!--            <a-popconfirm v-if="record.status === TaskStatus.Carrying"-->
            <!--                          title="您确定要挂起吗?"-->
            <!--                          ok-text="是"-->
            <!--                          cancel-text="否"-->
            <!--                          @confirm="onSuspendTask(record.job_id)"-->
            <!--            >-->
            <!--              <span style="cursor: pointer;margin-right: 10px;">挂起</span>-->
            <!--            </a-popconfirm>-->
            <!--            <a-popconfirm v-if="record.status === TaskStatus.Paused"-->
            <!--                          title="您确定要继续吗？"-->
            <!--                          ok-text="是"-->
            <!--                          cancel-text="否"-->
            <!--                          @confirm="onResumeTask(record.job_id)"-->
            <!--            >-->
            <!--              <div style="cursor: pointer;margin-right: 10px;">继续</div>-->
            <!--            </a-popconfirm>-->
          </div>
        </template>
      </a-table>
    </a-config-provider>
  </div>
</template>

<script setup lang="ts">
import zhCN from "ant-design-vue/es/locale/zh_CN";
import {reactive, onMounted, onBeforeUnmount, defineEmits, inject, computed} from 'vue'
import {message} from 'ant-design-vue'
import {TableState} from 'ant-design-vue/lib/table/interface'
import {IPage} from '@/api/http/type'
import {deleteTask, updateTaskStatus, UpdateTaskStatus, getWaylineJobs, Task, uploadMediaFileNow, getWayLineDetail, getHistoryTrajectory, breakPointJob} from '@/api/wayline'
import {ELocalStorageKey} from '@/types/enums'
import {useFormatTask} from './use-format-task'
import {TaskStatus, TaskProgressInfo, TaskProgressStatus, TaskProgressWsStatusMap, MediaStatus, MediaStatusProgressInfo, TaskMediaHighestPriorityProgressInfo} from '@/types/task'
import {useTaskWsEvent} from './use-task-ws-event'
import {getErrorMessage} from '@/utils/error-code/index'
import {commonColor} from '@/utils/color'
import {ExclamationCircleOutlined, UploadOutlined} from '@ant-design/icons-vue'
import EventBus from '@/event-bus/'
import { getWorkspaceId } from '@/utils/storage'

const componentStyle = computed(() => ({
  width: `${itemSize.value.width}px`,
  height: `${itemSize.value.height}px`,
}));

const itemSize = inject('itemSize', reactive({width: null, height: null}));

const emit = defineEmits(['updateData','showAmp','AmpJobId','showPlanList','AmpJobVideo']);

const body: IPage = {
  page: 1,
  total: 0,
  page_size: 10
}
const paginationProp = reactive({
  pageSizeOptions: ['20', '50', '100'],
  showQuickJumper: true,
  showSizeChanger: true,
  pageSize: 10,
  current: 1,
  total: 0
})

const columns = [
  {
    title: '计划 | 实际时间',
    dataIndex: 'duration',
    width: 230,
    slots: {customRender: 'duration'},
  },
  {
    title: '执行状态',
    key: 'status',
    width: 100,
    slots: {customRender: 'status'}
  },
  {
    title: '类型',
    dataIndex: 'taskType',
    width: 100,
    slots: {customRender: 'taskType'},
  },
  {
    title: '计划名称',
    dataIndex: 'job_name',
    width: 130,
    ellipsis: true
  },
  {
    title: '航线名称',
    dataIndex: 'file_name',
    width: 160,
    ellipsis: true
  },
  {
    title: '航线类型',
    dataIndex: 'wayline_type',
    width: 120,
    slots: {customRender: 'waylineType'},
  },
  {
    title: '设备名称',
    dataIndex: 'dock_name',
    width: 120,
    ellipsis: true
  },
  {
    title: '创建人',
    dataIndex: 'username',
    width: 100,
  },
  {
    title: 'RTH相对于机场的高度（m）',
    dataIndex: 'rth_altitude',
    width: 100,
  },
  {
    title: '失联动作',
    dataIndex: 'out_of_control_action',
    width: 100,
    slots: {customRender: 'lostAction'},
  },
  {
    title: '媒体上传状态',
    key: 'media_upload',
    width: 140,
    slots: {customRender: 'media_upload'}
  },
  {
    title: '操作',
    // width: 220,
    // fixed: 'right',
    slots: {customRender: 'action'}
  }
]
type Pagination = TableState['pagination']

const plansData = reactive({
  data: [] as Task[]
})

const {formatTaskType, formatTaskTime, formatLostAction, formatTaskStatus, formatMediaTaskStatus, formatWaylineType} = useFormatTask()

// 设备任务执行进度更新
function onTaskProgressWs(data: TaskProgressInfo) {
  const {bid, output} = data
  if (output) {
    const {status, progress} = output || {}
    const taskItem = plansData.data.find(task => task.job_id === bid)
    if (!taskItem) return
    if (status) {
      taskItem.status = TaskProgressWsStatusMap[status]
      // 执行中，更新进度
      if (status === TaskProgressStatus.Sent || status === TaskProgressStatus.inProgress) {
        taskItem.progress = progress?.percent || 0
      } else if ([TaskProgressStatus.Rejected, TaskProgressStatus.Canceled, TaskProgressStatus.Timeout, TaskProgressStatus.Failed, TaskProgressStatus.OK].includes(status)) {
        getPlans()
      }
    }
  }
}

//var polyline=null,passedPolyline=null,marker=null,lineArr=[];
let marker = null;      // 将marker声明为全局变量
let polyline = null;    // 将polyline声明为全局变量
let passedPolyline = null; // 将passedPolyline声明为全局变量
let lineArr = [];       // 初始化路径数组

const rowClick = (item) => {
  emit('showAmp', item.file_id);//航线
  emit('AmpJobId', item.job_id);//历史轨迹
  emit('showPlanList', false);
}

const rowClick1 = (item) => {
  emit('AmpJobVideo', item.job_id);//历史轨迹视频
  // emit('showPlanList', false);
}

const onBreakPointJob = (item) => {
  breakPointJob(item.job_id).then(res => {
    if (res.code === 0) {
      message.success('断点续飞成功')
    } else {
      message.error('断点续飞失败')
    }
  }).finally(() => {
    getPlans()
  })
}

function clearMarkersAndPolylines() {
  if (marker) {
    marker.setMap(null);
    marker.destroy(); // 销毁标记对象，确保所有资源被释放

    marker = null;
  }
  if (polyline) {
    polyline.setMap(null);
    polyline.destroy(); // 销毁多段线对象，确保所有资源被释放
    polyline = null;
  }
  if (passedPolyline) {
    passedPolyline.setMap(null);
    passedPolyline.destroy(); // 销毁辅助多段线对象
    passedPolyline = null;
  }
  //lineArr = []; // 清空轨迹数组
  // 清除任何可能存在的事件监听器
  if (marker && marker.off) {
    marker.off('moving');
  }
}

// 媒体上传进度更新
function onTaskMediaProgressWs(data: MediaStatusProgressInfo) {
  const {media_count: mediaCount, uploaded_count: uploadedCount, job_id: jobId} = data
  if (isNaN(mediaCount) || isNaN(uploadedCount) || !jobId) {
    return
  }
  const taskItem = plansData.data.find(task => task.job_id === jobId)
  if (!taskItem) return
  if (mediaCount === uploadedCount) {
    taskItem.uploading = false
  } else {
    taskItem.uploading = true
  }
  taskItem.media_count = mediaCount
  taskItem.uploaded_count = uploadedCount
}

function onoTaskMediaHighestPriorityWS(data: TaskMediaHighestPriorityProgressInfo) {
  const {pre_job_id: preJobId, job_id: jobId} = data
  const preTaskItem = plansData.data.find(task => task.job_id === preJobId)
  const taskItem = plansData.data.find(task => task.job_id === jobId)
  if (preTaskItem) {
    preTaskItem.uploading = false
  }
  if (taskItem) {
    taskItem.uploading = true
  }
}

function getCodeMessage(code: number) {
  return getErrorMessage(code) + `（错误码: ${code}）`
}

useTaskWsEvent({
  onTaskProgressWs,
  onTaskMediaProgressWs,
  onoTaskMediaHighestPriorityWS,
})

onMounted(() => {
  getPlans()
  EventBus.on('updateplan', getPlans) //收到通知
})

onBeforeUnmount(() => {
  EventBus.off('updateplan')
})

function getPlans() {
  getWaylineJobs(getWorkspaceId(), body).then(res => {
    if (res.code !== 0) {
      return
    }
    plansData.data = res.data.list
    paginationProp.total = res.data.pagination.total
    paginationProp.current = res.data.pagination.page
  })
}

function refreshData(page: Pagination) {
  body.page = page?.current!
  body.page_size = page?.pageSize!
  getPlans()
}

// 删除任务
async function onDeleteTask(jobId: string) {
  const {code} = await deleteTask(getWorkspaceId(), {
    job_id: jobId
  })
  if (code === 0) {
    message.success('删除任务成功')
    getPlans()
  }
}

// 挂起任务
async function onSuspendTask(jobId: string) {
  const {code} = await updateTaskStatus(getWorkspaceId(), {
    job_id: jobId,
    status: UpdateTaskStatus.Suspend
  })
  if (code === 0) {
    message.success('挂起任务成功')
    getPlans()
  }
}

// 解除挂起任务
async function onResumeTask(jobId: string) {
  const {code} = await updateTaskStatus(getWorkspaceId(), {
    job_id: jobId,
    status: UpdateTaskStatus.Resume
  })
  if (code === 0) {
    message.success('解除挂起任务成功')
    getPlans()
  }
}

// 立即上传媒体
async function onUploadMediaFileNow(jobId: string) {
  const {code} = await uploadMediaFileNow(getWorkspaceId(), jobId)
  if (code === 0) {
    message.success('上传媒体文件成功')
    getPlans()
  }
}
</script>

<style lang="scss" scoped>
.plan-panel-wrapper {
  width: 99%;

  .plan-table {
    margin: 0 10px;
  }

  .action-area {
    &::v-deep {
      .ant-btn {
        margin-right: 10px;
        margin-bottom: 10px;
      }
    }
  }

  .circle-icon {
    display: inline-block;
    width: 12px;
    height: 12px;
    margin-right: 3px;
    border-radius: 50%;
    vertical-align: middle;
    flex-shrink: 0;
  }
}

:deep(.ant-table-pagination.ant-pagination) {
  float: right;
  margin: 0;
  background: #fff;
}

::v-deep.ant-table-thead > tr > th {
  color: #749dee !important;
}
</style>