<template>
  <div class="login-container" ref="login" @keyup.enter="handleLogin" style="height: 100%; overflow-y: auto;">
    <div class="login-weaper animated bounceInDown">
      <div class="login-left">
        <div class="login-time">
          {{ time }}
        </div>
        <!-- <p class="title">{{ $t('login.info') }}</p> -->
        <img class="img" src="/img/logo.png" alt="">
      </div>
      <div class="login-border">
        <div class="login-main">
          <h4 class="login-title">
            {{ $t('login.title') }}{{ website.title }}
            <!-- <top-lang></top-lang> -->
          </h4>
          <userLogin v-if="activeName === 'user'"></userLogin>
          <!-- <codeLogin v-else-if="activeName === 'code'"></codeLogin>
          <thirdLogin v-else-if="activeName === 'third'"></thirdLogin> -->
          <div class="login-menu">
            <!-- <a href="#" @click.stop="activeName = 'user'">{{ $t('login.userLogin') }}</a> -->
            <!--<a href="#" @click.stop="activeName='code'">{{ $t('login.phoneLogin') }}</a>-->
            <!-- <a href="#" @click.stop="activeName = 'third'">{{ $t('login.thirdLogin') }}</a>
            <a :href="website.ssoUrl + website.redirectUri">{{ $t('login.ssoLogin') }}</a> -->
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import userLogin from './login/userlogin.vue';
import codeLogin from './login/codelogin.vue';
import thirdLogin from './login/thirdlogin.vue';
import { mapGetters } from 'vuex';
import { validatenull } from '@/utils/validate';
import topLang from '@/page/index/top/top-lang.vue';
import { getQueryString, getTopUrl } from '@/utils/util';
import website from '@/config/website';
import { message } from 'ant-design-vue'
import { CURRENT_CONFIG } from '@/config/dijconfig'
import { EBizCode, EComponentName, EDownloadOwner, ELocalStorageKey, ERouterName, EStatusValue,EVideoPublishType } from '@/api/enum/index'
export default {
  name: 'login',
  components: {
    userLogin,
    codeLogin,
    thirdLogin,
    topLang,
  },
  data() {
    return {
      isVerified:false,
      website: website,
      time: '',
      activeName: 'user',
      socialForm: {
        tenantId: '',
        source: '',
        code: '',
        state: '',
      },
    };
  },
  watch: {
    // $route() {
    //   this.handleLogin();
    // },
  },
  created() {
    // this.handleLogin();
    this.getTime();
    this.verifyLicense();
    // let tokens = JSON.parse(localStorage.getItem('hztech-token'))
    // // alert(tokens.content)
    // if(tokens.content){
    //   this.$router.push('/pilot-home');
    // }
  },
  mounted() {
  },
  computed: {
    ...mapGetters(['tagWel']),
  },
  props: [],
  methods: {
    //验证设备的判断
   verifyLicense () {
    this.isVerified = window.djiBridge.platformVerifyLicense(CURRENT_CONFIG.appId, CURRENT_CONFIG.appKey, CURRENT_CONFIG.appLicense)&&window.djiBridge.platformIsVerified()
    if (this.isVerified) {
      console.log('The license verification is successful.')
    } else {
      message.error('Filed to verify the license. Please check license whether the license is correct, or apply again.')
    }
  },
    getTime() {
      setInterval(() => {
        this.time = this.$dayjs().format('YYYY-MM-DD HH:mm:ss');
      }, 1000);
    },
    handleLogin() {
      const topUrl = getTopUrl();
      const redirectUrl = '/oauth/redirect/';
      const ssoCode = '?code=';
      this.socialForm.source = getQueryString('source');
      this.socialForm.code = getQueryString('code');
      this.socialForm.state = getQueryString('state');
      if (validatenull(this.socialForm.source) && topUrl.includes(redirectUrl)) {
        let source = topUrl.split('?')[0];
        source = source.split(redirectUrl)[1];
        this.socialForm.source = source;
      }
      if (
        topUrl.includes(redirectUrl) &&
        !validatenull(this.socialForm.source) &&
        !validatenull(this.socialForm.code) &&
        !validatenull(this.socialForm.state)
      ) {
        const loading = this.$loading({
          lock: true,
          text: '第三方系统登录中,请稍后',
          background: 'rgba(0, 0, 0, 0.7)',
        });
        this.$store
          .dispatch('LoginBySocial', this.socialForm)
          .then(() => {
            window.location.href = topUrl.split(redirectUrl)[0];
            this.$router.push(this.tagWel);
            loading.close();
          })
          .catch(() => {
            loading.close();
          });
      } else if (
        !topUrl.includes(redirectUrl) &&
        !validatenull(this.socialForm.code) &&
        !validatenull(this.socialForm.state)
      ) {
        const loading = this.$loading({
          lock: true,
          text: '单点系统登录中,请稍后',
          background: 'rgba(0, 0, 0, 0.7)',
        });
        this.$store
          .dispatch('LoginBySso', this.socialForm)
          .then(() => {
            window.location.href = topUrl.split(ssoCode)[0];
            this.$router.push(this.tagWel);
            loading.close();
          })
          .catch(() => {
            loading.close();
          });
      }
    },
  },
};
</script>

<style lang="scss">
@import '@/styles/login.scss';
</style>
