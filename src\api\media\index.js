import request from '@/axios'

const HTTP_PREFIX = '/hztech-flight-core/media/api/v1'

// Get Media Files
export const getMediaFiles = (wid, params) => {
  return request({
    url: `${HTTP_PREFIX}/files/${wid}/files`,
    method: 'get',
    params: {
      ...params
    }
  })
}

// Download Media File
export const downloadMediaFile = async function (workspaceId, fileId, chunk) {
  const { start, end } = chunk;
  const response = await request.get(`${HTTP_PREFIX}/files/${workspaceId}/file/${fileId}/url`, {
    headers: {
      'Range': `bytes=${start}-${end}`,
      'content-type': 'application/octet-stream',
    },
    responseType: 'blob'
  })
  return response.data
}

export const getFileSize = async function (workspaceId, fileId) {
  const url = `${HTTP_PREFIX}/files/${workspaceId}/file/${fileId}/getFileSize`
  const result = await request.get(url)
  return result.data.data
}

// 文件删除
export const deleteMediaFiles = (params) => {
  return request({
    url: `${HTTP_PREFIX}/files/file/batchDelete`,
    method: 'get',
    params: {
      ...params
    }
  })
}
/**
 * 获取分页列表
 * @param {*} workspace_id 
 * @param {*} params 
 * @returns 
 */
export const getFilesLevelPage = async function (workspace_id, params) {
  const url = `${HTTP_PREFIX}/files/${workspace_id}/getFilesLevelPage`;
  return request({
    url,
    method: 'get',
    params: {
      ...params
    }
  })
};

export const getMediaLocal = async function (workspaceId, fileId, chunk, token) {
  const { start, end } = chunk;

  const response = await request.get(`${HTTP_PREFIX}/files/${workspaceId}/file/${fileId}/playMp4-test?HzTech-Auth=bearer ${token}`, {
    headers: {
      'Range': `bytes=${start}-${end}`,
      'content-type': 'video/mp4',
    },
    timeout: 300000,
    responseType: 'blob'
  })
  return response.data
}

// 下载文件夹为ZIP
export const downloadFolderAsZip = async function (workspaceId, jobId, onProgressCallback, totalSize) {
  // 确保回调函数被调用，开始时设置为1%
  if (onProgressCallback) {
    onProgressCallback(1);
  }
  
  // 记录开始下载的时间
  const startTime = Date.now();

  const response = await request.get(`${HTTP_PREFIX}/files/${workspaceId}/download-zip`, {
    params: {
      jobId
    },
    responseType: 'blob',
    onDownloadProgress: progressEvent => {
      // 计算已经过去的时间（秒）
      const elapsedSeconds = (Date.now() - startTime) / 1000;
      // 使用传入的文件夹总大小计算总字节
      const totalBytes = totalSize * 1024 * 1024; // 将MB转换为字节
      
      let percentage = 0;
      let loaded = progressEvent.loaded;
      
      // 如果能正常获取到loaded，使用实际进度
      if (progressEvent.loaded > 0 && totalBytes) {
        loaded = progressEvent.loaded;
        percentage = Math.min(Math.round((loaded / totalBytes) * 100), 99);
      } else {
        // 模拟进度：按1MB/s的速度计算
        const estimatedLoaded = Math.min(elapsedSeconds * 1024 * 1024, totalBytes);
        loaded = estimatedLoaded;
        percentage = Math.min(Math.round((estimatedLoaded / totalBytes) * 100), 99);
      }
      
      console.log(`下载进度: ${percentage}%, loaded: ${loaded}, total: ${totalBytes}, 耗时: ${elapsedSeconds.toFixed(1)}s`);
      if (onProgressCallback) {
        onProgressCallback(percentage);
      }
    }
  })

  // 下载完成时设置为100%
  if (onProgressCallback) {
    onProgressCallback(100);
  }

  return response.data
}
