<template>
  <div id="map-controls">
    <el-button v-if="isDrawing" style="width: 90px;" type="primary" @click="setTakeoffPoint">
      重设起飞点
    </el-button>
  </div>
</template>

<script>
import {getRoot} from '@/root';
import EventBus from '@/event-bus';

export default {
  data() {
    return {
      points: [], // 存储点位数据
      startPoint: [], // 存储起飞点数据
      isDrawing: false, // 绘制状态-true表示结束绘制 false表示开始绘制
      texts: null, // 航段中点距离标签
      overlay: null, // 起飞点图层
      polyline: null, // 存储折线数据
      overlayGroups: null, // 航点群体标识
      startPolyline: null, // 存储起飞点折线数据
    };
  },
  props: {
    /**缩放级别 */
    zoom: Number,
    /**地图中心点 */
    center: Array,
    /**地图绘制状态 */
    isSet: Boolean,
    /**航线点位数据 */
    placemark: Array,
    /**起飞点数据 */
    take0ffPoint: Array
  },
  watch: {
    isSet: {
      handler(val) {
        this.isDrawing = val
        const root = getRoot()
        let map = root.$map
        if (this.isDrawing) {
          map.setDefaultCursor('move');
          map.on('click', this.handleMapClick);
        } else {
          map.setDefaultCursor('default');
          map.off('click', this.handleMapClick);
        }
        this.updateMapline()
      }, deep: true
    },
    placemark: {
      handler(val) {
        this.points = val
        this.setZoom(val)
      }, deep: true
    },
    take0ffPoint: {
      handler(val) {
        this.startPoint = val
      }, deep: true
    },
    startPoint: {
      handler() {
        const root = getRoot()
        let map = root.$map
        if (this.startPoint.length == 0) {
          map.setDefaultCursor('move');
        } else {
          map.setDefaultCursor('default');
        }
        this.updateMapline()
      }, deep: true
    },
    points: {
      handler() {
        this.updateMapline()
      }, deep: true
    }
  },
  mounted() {
    EventBus.on('lineClose', () => {
      this.clearWayLine()
      this.points = []
      this.startPoint = []
    });
  },
  beforeUnmount() {
    this.clearWayLine()
    this.points = []
    this.startPoint = []
  },
  methods: {
    //更新航线
    handleMapClick(e) {
      if (this.startPoint.length == 0) {
        this.addTakeoffPoint(e.lnglat);
      } else {
        this.addPoint(e.lnglat);
      }
    },
    //地图航线图层更新
    updateMapline() {
      const root = getRoot()
      let map = root.$map
      let AMap = root.$aMap
      //删除所有图层
      this.clearWayLine()
      //添加起飞点
      if (this.startPoint && this.startPoint.length > 0) {
        this.overlay = new AMap.Marker({
          position: this.startPoint,
          title: '起飞点',
          draggable: this.isDrawing,
          cursor: this.isDrawing ? 'move' : 'default'
        })
        
        this.overlay.on('dragend', (e) => {
          this.startPoint = [e.lnglat.lng, e.lnglat.lat];
          this.$emit('takeoffPoint', this.startPoint);
          this.updateMapline();
        });
        
        map.add(this.overlay);
      }
      //添加起飞点距离第一个航线的连线
      this.createStartPolyline(map, AMap);
      //添加航线折线数据
      if (this.points.length > 1) {
        this.polyline = new AMap.Polyline({
          path: this.points,
          strokeColor: '#ff3f48',
          strokeOpacity: 1,
          strokeWeight: 2,
          strokeStyle: 'solid'
        });
        map.add(this.polyline);
      }
      //添加航点标点
      this.createPointIcont(map, AMap)
      //添加航线之间距离数据
      this.createDistancesText(map, AMap)
    },
    //添加航点标点
    createPointIcont(map, AMap) {
      if (this.points.length > 0) {
        let marker = []
        for (let i = 0; i < this.points.length; i++) {
          let mark = new AMap.Marker({
            position: this.points[i],
            content: `<div class="marker-icon" style="width: 0;height: 0;border-left: 20px solid transparent;border-right: 20px solid transparent;border-top: 40px solid #00D590;color: #fff;position: absolute;right: -20px;bottom: 0;display: flex;justify-content: center;"><div class="content" style="position: absolute;bottom: 14px;">${i + 1}</div></div>`,
            draggable: this.isDrawing,
            cursor: this.isDrawing ? 'move' : 'default'
          });
          
          const pointIndex = i;
          mark.on('dragend', (e) => {
            this.points[pointIndex] = [e.lnglat.lng, e.lnglat.lat];
            
            const newArray = this.points.map(item => ({
              point: `${item[0]},${item[1]}`
            }));
            this.$emit('allPoints', newArray);
            
            this.updateMapline();
          });
          
          marker.push(mark)
        }
        this.overlayGroups = new AMap.OverlayGroup(marker);
        map.add(this.overlayGroups);
      }
    },
    createDistancesText(map, AMap) {
      let count = 0
      if (this.startPoint.length > 0) {
        count++;
      }
      if (count + this.points.length > 2) {
        let arr = [];
        if (this.startPoint.length > 0) {
          arr.push(this.startPoint)
        }
        for (let i = 0; i < this.points.length; i++) {
          arr.push(this.points[i])
        }
        // 计算并显示各航段的距离
        let distances = [];
        let disText = [];
        for (let i = 0; i < arr.length - 1; i++) {
          let distance = AMap.GeometryUtil.distance(arr[i], arr[i + 1]);
          distances.push(distance);
        }
        // 在每个航段的中点放置距离标签
        distances.forEach((distance, i) => {
          let pointMidText = new AMap.Text({
            text: distance.toFixed(2) + ' m',
            style: {
              'background-color': '#29b6f6',
              'font-size': '12px',
              'color': '#fff',
            },
            offset: new AMap.Pixel(-20, -20),
            direction: 'center',
            directionGap: 5,
          });
          let midPoint = this.getMidPoints(arr[i], arr[i + 1]);
          pointMidText.setPosition(midPoint);
          disText.push(pointMidText);
        });
        this.texts = new AMap.OverlayGroup(disText);
        map.add(this.texts)
      }
    },
    //添加起飞点距离第一个航线的连线
    createStartPolyline(map, AMap) {
      if (this.startPoint.length > 0 && this.points.length > 0) {
        let payline = [];
        payline.push(this.startPoint);
        payline.push(this.points[0])
        this.startPolyline = new AMap.Polyline({
          path: payline,
          strokeColor: '#6effd3',
          strokeOpacity: 1,
          strokeWeight: 2,
          strokeStyle: 'solid'
        });
        map.add(this.startPolyline)
      }
    },
    // 添加航点
    addPoint(position) {
      let waylinePoint = []
      waylinePoint.push(position.lng, position.lat);
      this.points.push(waylinePoint);
      const newArray = this.points.map(item => ({
        point: `${item[0]},${item[1]}`
      }));
      this.$emit('allPoints', newArray);
    },
    // 设置起飞点
    addTakeoffPoint(position) {
      this.startPoint = [position.lng, position.lat];
      this.$emit('takeoffPoint', this.startPoint);
    },
    // 重设起飞点
    setTakeoffPoint() {
      const root = getRoot()
      let map = root.$map
      map.setDefaultCursor('move');
      this.startPoint = [];
      this.updateMapline()
    },
    clearWayLine() {
      const root = getRoot()
      let map = root.$map
      if (this.overlay) {
        map.remove(this.overlay);
        this.overlay = null
      }
      //删除航点标识
      if (this.overlayGroups) {
        map.remove(this.overlayGroups);
        this.overlayGroups = null
      }
      //删除航线
      if (this.polyline) {
        map.remove(this.polyline)
        this.polyline = null
      }
      //删除起飞点到第一条线
      if (this.startPolyline) {
        map.remove(this.startPolyline)
        this.startPolyline = null
      }
      //删除距离文字
      if (this.texts) {
        map.remove(this.texts)
        this.texts = null
      }
    },
    // 动态设置地图缩放等级和中心点
    setZoom(val) {
      const root = getRoot()
      let map = root.$map
      let objectCoordinates = val.map(([lng, lat]) => ({
        lng: lng, lat: lat
      }));
      if (objectCoordinates.length > 0) {
        let bounds = this.getBoundingBox(objectCoordinates)
        let center = bounds.getCenter();
        map.setCenter(center);
      }
    },
    // 计算边界框
    getBoundingBox(points) {
      let minLng = Infinity;
      let maxLng = -Infinity;
      let minLat = Infinity;
      let maxLat = -Infinity;
      for (let point of points) {
        if (point.lng < minLng) minLng = point.lng;
        if (point.lng > maxLng) maxLng = point.lng;
        if (point.lat < minLat) minLat = point.lat;
        if (point.lat > maxLat) maxLat = point.lat;
      }
      return new AMap.Bounds([minLng, minLat], [maxLng, maxLat]);
    },
    // 计算航线两点之间中心点
    getMidPoints(pointA, pointB) {
      // 点A和点B的经纬度坐标
      let lat1 = pointA[1];
      let lon1 = pointA[0];
      let lat2 = pointB[1];
      let lon2 = pointB[0];
      // 将经纬度从十进制转换为弧度
      lat1 = lat1 * Math.PI / 180;
      lon1 = lon1 * Math.PI / 180;
      lat2 = lat2 * Math.PI / 180;
      lon2 = lon2 * Math.PI / 180;
      // 计算中心点的经纬度（弧度）
      let midLat = (lat1 + lat2) / 2;
      let midLon = (lon1 + lon2) / 2;
      // 将结果从弧度转换回十进制
      midLat = midLat * 180 / Math.PI;
      midLon = midLon * 180 / Math.PI;
      return [midLon, midLat];
    },
  }
}
</script>

<style scoped lang="scss">
#map-controls {
  position: fixed;
  top: 10px;
  left: 350px;
  z-index: 1;
}

button {
  font-size: 14px;
  cursor: pointer;
}
</style>