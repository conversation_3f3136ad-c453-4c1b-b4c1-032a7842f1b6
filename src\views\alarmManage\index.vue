<template>
  <basic-container>
    <avue-crud :option="option" :table-loading="loading" :data="data" :page.sync="page" :permission="permissionList"
      :before-open="beforeOpen" v-model="form" ref="crud" @row-update="rowUpdate" @row-save="rowSave" @row-del="rowDel"
      @search-change="searchChange" @search-reset="searchReset" @selection-change="selectionChange"
      @current-change="currentChange" @size-change="sizeChange" @refresh-change="refreshChange" @on-load="onLoad">
      <template #srcpicUrl="{ row }">
        <el-image :src="row.srcpicUrl" style="width: 100px; height: 100px;"
          :preview-src-list="[row.srcpicUrl, row.picUrl].filter(Boolean)" preview-teleported="true" />
      </template>
      <template #srcpicUrl-form>
        <el-image :src="form.srcpicUrl" :preview-src-list="[form.srcpicUrl, form.picUrl].filter(Boolean)"
          preview-teleported="true" />
      </template>
      <template #picUrl="{ row }">
        <el-image :src="row.picUrl" style="width: 100px; height: 100px;"
          :preview-src-list="[row.srcpicUrl, row.picUrl].filter(Boolean)" preview-teleported="true" />
      </template>
      <template #picUrl-form>
        <el-image :src="form.picUrl" :preview-src-list="[form.srcpicUrl, form.picUrl].filter(Boolean)"
          preview-teleported="true" />
      </template>
      <template #menu="{ row }">
        <el-button v-if="row.handleStatus == 0 || row.handleStatus == 2" text type="primary" icon="el-icon-edit"
          @click="handleView(row)">处理</el-button>
      </template>
    </avue-crud>

    <!-- 报警详情弹窗 -->
    <div class="alarm-detail-modal" v-if="showDetailModal">
      <div class="modal-content">
        <div class="modal-header">
          <div class="device-info">
            <div class="info-row">
              <span class="label">算法名称:</span>
              <span class="value">{{ currentAlarm.algoName }}</span>
              <span class="label">设备名称:</span>
              <span class="value">{{ currentAlarm.deviceName }}</span>
            </div>
            <div class="info-row">
              <span class="label">设备ID:</span>
              <span class="value">{{ currentAlarm.cid }}</span>
              <span class="label">报警状态:</span>
              <span class="value">{{ statusMap[currentAlarm.handleStatus]?.label || currentAlarm.handleStatus }}</span>
            </div>
            <div class="info-row">
              <span class="label">发生时间:</span>
              <span class="value">{{ currentAlarm.happenTime }}</span>
            </div>
          </div>
          <div class="close-btn" @click="closeDetailModal">×</div>
        </div>
        <div class="modal-body">
          <div class="image-section">
            <div class="image-container">
              <div class="image-title">算法分析前图片</div>
              <div class="image-content">
                <el-image :src="currentAlarm.srcpicUrl || ''" fit="contain" preview-teleported="true"
                  :preview-src-list="[currentAlarm.srcpicUrl, currentAlarm.picUrl]" alt="分析前图片" />
              </div>
            </div>
            <div class="image-container">
              <div class="image-title">算法分析后图片</div>
              <div class="image-content">
                <el-image :src="currentAlarm.picUrl || ''" fit="contain" preview-teleported="true"
                  :preview-src-list="[currentAlarm.picUrl, currentAlarm.srcpicUrl]" alt="分析后图片" />
              </div>
            </div>
          </div>
          <div v-if="currentAlarm.handleStatus != 2" class="process-section">
            <div class="section-title">处理</div>
            <div class="process-form">
              <div class="form-item">
                <span class="required">*</span>
                <span class="label">确认结果</span>
                <div class="radio-group">
                  <label>
                    <input type="radio" v-model="processForm.eventType" value="2" />
                    <span>误报</span>
                  </label>
                  <label>
                    <input type="radio" v-model="processForm.eventType" value="1" />
                    <span>报警</span>
                  </label>
                </div>
              </div>
              <div v-if="processForm.eventType == '1'" class="form-item"
                :class="{ 'has-error': formErrors.handlePeopleId }">
                <span class="required">*</span>
                <span class="label">处理人</span>
                <div class="input-wrapper">
                  <el-select v-model="processForm.handlePeopleId" placeholder="请选择处理人">
                    <el-option v-for="user in userList" :key="user.id" :value="user.realName"
                      :label="user.realName"></el-option>
                  </el-select>
                  <div class="error-message" v-if="formErrors.handlePeopleId">{{ formErrors.handlePeopleId }}</div>
                </div>
              </div>
              <div v-if="processForm.eventType == '1'" class="form-item"
                :class="{ 'has-error': formErrors.handleCommand }">
                <span class="required">*</span>
                <span class="label">下发命令</span>
                <div class="input-wrapper">
                  <textarea v-model="processForm.handleCommand" placeholder="请输入下发命令" maxlength="120"></textarea>
                  <div class="char-count">{{ processForm.handleCommand.length }}/120</div>
                  <div class="error-message" v-if="formErrors.handleCommand">{{ formErrors.handleCommand }}</div>
                </div>
              </div>
              <div v-if="processForm.eventType == '1'" class="form-item"
                :class="{ 'has-error': formErrors.handleRemark }">
                <span class="required">*</span>
                <span class="label">处理意见</span>
                <div class="input-wrapper">
                  <textarea v-model="processForm.handleRemark" placeholder="请输入处理意见" maxlength="120"></textarea>
                  <div class="char-count">{{ processForm.handleRemark.length }}/120</div>
                  <div class="error-message" v-if="formErrors.handleRemark">{{ formErrors.handleRemark }}</div>
                </div>
              </div>
              <div class="form-actions">
                <button class="submit-btn" @click="submitProcess">提交处理</button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </basic-container>
</template>

<script>
import { getList, getDetail, add, update, remove } from "@/api/alarmManage/index";
import { getUserList, createWork } from "@/api/workSite/index.js";
import { mapGetters } from "vuex";

export default {
  data() {
    return {
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0
      },
      selectionList: [],
      option: {
        height: 'auto',
        calcHeight: 30,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        index: false,
        viewBtn: true,
        selection: false,
        dialogClickModal: false,
        refreshBtn: false,
        columnBtn: false,
        searchShowBtn: false,
        labelWidth: 140,
        column: [
          {
            label: "算法名称",
            prop: "algoName",
            search: true,
            maxlength: 20,
          },
          {
            label: "算法分析前图片",
            prop: "srcpicUrl",
            maxlength: 150,
          },
          {
            label: "算法分析后图片",
            prop: "picUrl",
            maxlength: 150,
          },
          {
            label: "设备ID",
            prop: "cid",
            search: true,
            maxlength: 20,
            rules: [{ required: true, message: '请输入设备ID', trigger: 'blur' }],
          },
          {
            label: "告警摄像头名称",
            prop: "deviceName",
            maxlength: 20,
          },
          {
            label: "报警状态",
            prop: "handleStatus",
            type: "select",
            dicData: [
              { label: "未处理", value: 0 },
              { label: "处理中", value: 1 },
              { label: "已处理", value: 2 }
            ],
            search: true,
          },
          {
            label: "处理方式",
            prop: "handleType",
            type: "select",
            dicData: [
              { label: "派发", value: 1 },
              { label: "预案", value: 2 },
              { label: "误报", value: 3 }
            ],
          },
          {
            label: "处理人",
            prop: "handleUser",
          },
          {
            label: "发生时间",
            prop: "happenTime",
            type: "datetime",
            searchRange: true,
          },
        ],
      },
      data: [],
      // 弹窗相关数据
      showDetailModal: false,
      currentAlarm: {},
      processForm: {
        eventType: '1',
        handlePeopleId: '',
        handleCommand: '',
        handleRemark: ''
      },
      formErrors: {
        handlePeopleId: '',
        handleCommand: '',
        handleRemark: ''
      },
      userList: [],
      statusMap: {
        0: { label: "未处理", className: "status-unhandled" },
        1: { label: "处理中", className: "status-processing" },
        2: { label: "已处理", className: "status-handled" }
      }
    };
  },
  computed: {
    ...mapGetters(["permission"]),
    permissionList() {
      return {
        addBtn: this.validData(this.permission.join_add, false),
        viewBtn: this.validData(this.permission.join_view, true),
        delBtn: this.validData(this.permission.join_delete, true),
        editBtn: this.validData(this.permission.join_edit, false)
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach(ele => {
        ids.push(ele.id);
      });
      return ids.join(",");
    }
  },
  created() {
    // 获取用户列表
    this.fetchUserList();
  },
  methods: {
    // 查看详情
    handleView(row) {
      this.currentAlarm = row;

      // 重置处理表单
      this.processForm.eventType = '1';
      this.processForm.handlePeopleId = '';
      this.processForm.handleCommand = '';
      this.processForm.handleRemark = '';

      // 显示弹窗
      this.showDetailModal = true;
    },
    // 关闭详情弹窗
    closeDetailModal() {
      this.showDetailModal = false;
      // 清除所有错误提示
      Object.keys(this.formErrors).forEach(key => {
        this.formErrors[key] = '';
      });
    },
    // 检查表单字段
    validateField(field, message) {
      if (!this.processForm[field]) {
        this.formErrors[field] = message;
        return false;
      }
      this.formErrors[field] = '';
      return true;
    },
    // 提交处理
    submitProcess() {
      if (this.processForm.eventType == '2') {
        this.currentAlarm.handleStatus = 2;
        update(this.currentAlarm).then(() => {
          this.closeDetailModal();
          this.refreshChange();
        });
        return;
      }
      // 重置错误信息
      Object.keys(this.formErrors).forEach(key => {
        this.formErrors[key] = '';
      });

      // 表单验证
      const isHandlerValid = this.validateField('handlePeopleId', '请选择处理人');
      const isCommandValid = this.validateField('handleCommand', '请输入下发命令');
      const isOpinionValid = this.validateField('handleRemark', '请输入处理意见');

      if (!isHandlerValid || !isCommandValid || !isOpinionValid) {
        return;
      }

      this.processForm.eventId = this.currentAlarm.id;
      this.processForm.eventName = this.currentAlarm.algoName;

      try {
        // 处理提交的API调用
        createWork(this.processForm).then(res => {
          this.closeDetailModal();
          this.refreshChange();
        });
      } catch (error) {
        console.error('处理报警失败', error);
      }
    },
    // 获取用户列表
    fetchUserList() {
      getUserList().then(res => {
        this.userList = res.data.data.records;
      }).catch(error => {
        console.error('获取用户列表失败', error);
      });
    },
    changeSwitch(row) {
      update(row).then(() => {
        this.$message({
          type: "success",
          message: "操作成功!"
        });
      }).finally(() => {
        this.onLoad(this.page);
      });
    },
    rowSave(row, done, loading) {
      add(row).then(() => {
        this.onLoad(this.page);
        this.$message({
          type: "success",
          message: "操作成功!"
        });
        done();
      }, error => {
        loading();
        window.console.log(error);
      });
    },
    rowUpdate(row, index, done, loading) {
      update(row).then(() => {
        this.onLoad(this.page);
        this.$message({
          type: "success",
          message: "操作成功!"
        });
        done();
      }, error => {
        loading();
        console.log(error);
      });
    },
    rowCalled(row, index, done, loading) {
      called(row).then(() => {
        this.onLoad(this.page);
        this.$message({
          type: "success",
          message: "确认联系成功!"
        });
        done();
      }, error => {
        loading();
        console.log(error);
      });
    },
    rowDel(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          return remove(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        });
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          return remove(this.ids);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          this.$refs.crud.toggleSelection();
        });
    },
    beforeOpen(done, type) {
      if (["edit", "view"].includes(type)) {
        getDetail(this.form.id).then(res => {
          this.form = res.data.data;
        });
      }
      done();
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      this.loading = true;
      getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    }
  }
};
</script>

<style lang="scss" scoped>
/* 弹窗样式 */
.alarm-detail-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;

  .modal-content {
    width: 90%;
    max-width: 800px;
    background-color: #001529;
    border-radius: 5px;
    overflow: hidden;
    color: white;
    max-height: 90vh;
    display: flex;
    flex-direction: column;

    .modal-header {
      padding: 15px;
      background-color: #001a33;
      position: relative;

      .device-info {
        .info-row {
          display: flex;
          margin-bottom: 8px;
          flex-wrap: wrap;

          .label {
            margin-right: 5px;
            color: #a0a0a0;
            min-width: 70px;
          }

          .value {
            margin-right: 15px;
            flex: 1;
          }
        }
      }

      .close-btn {
        position: absolute;
        top: 10px;
        right: 15px;
        font-size: 24px;
        cursor: pointer;
        color: #a0a0a0;

        &:hover {
          color: white;
        }
      }
    }

    .modal-body {
      display: flex;
      padding: 15px;
      max-height: calc(90vh - 120px);
      overflow: auto;

      .image-section {
        display: flex;
        flex-direction: column;
        width: 60%;
        margin-right: 15px;
        flex: 1;

        &:only-child {
          width: 100%;
          margin-right: 0;
        }

        .image-container {
          margin-bottom: 15px;
          background-color: #172a45;
          border-radius: 5px;
          overflow: hidden;

          .image-title {
            padding: 10px;
            background-color: #0d1b2a;
            font-weight: bold;
          }

          .image-content {
            padding: 10px;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 200px;

            :deep(.el-image) {
              width: 100%;
              height: 100%;
              display: flex;
              justify-content: center;
              align-items: center;
            }

            :deep(.el-image__inner) {
              max-width: 100%;
              max-height: 100%;
              object-fit: contain;
            }
          }
        }
      }

      .process-section {
        width: 40%;
        background-color: #172a45;
        border-radius: 5px;
        padding: 15px;
        min-height: 300px;
        min-width: 250px;

        .section-title {
          font-weight: bold;
          margin-bottom: 15px;
        }

        .process-form {
          .form-item {
            margin-bottom: 15px;
            display: flex;
            align-items: flex-start;

            &.has-error {

              input,
              textarea {
                border-color: #f5222d;
              }
            }

            .required {
              color: #f5222d;
              margin-right: 3px;
            }

            .label {
              width: 80px;
              margin-top: 3px;
            }

            .radio-group {
              flex: 1;
              display: flex;

              label {
                margin-right: 15px;
                display: flex;
                align-items: center;

                input {
                  margin-right: 5px;
                }
              }
            }

            .input-wrapper {
              flex: 1;
              position: relative;

              input,
              textarea {
                width: 100%;
                padding: 8px;
                background-color: #0d1b2a;
                border: 1px solid #364d79;
                border-radius: 4px;
                color: white;
                font-size: 14px;
                outline: none;

                &:focus {
                  border-color: #1890ff;
                }
              }

              textarea {
                min-height: 80px;
                resize: vertical;
              }

              .char-count {
                position: absolute;
                bottom: 5px;
                right: 10px;
                font-size: 12px;
                color: #a0a0a0;
              }

              .error-message {
                color: #f5222d;
                font-size: 12px;
                margin-top: 3px;
              }
            }
          }

          .empty-space {
            height: 200px;
            display: flex;
            align-items: center;
            justify-content: center;
          }

          .empty-message {
            color: #a0a0a0;
            font-size: 14px;
          }

          .form-actions {
            display: flex;
            justify-content: center;
            margin-top: 20px;

            .submit-btn {
              padding: 8px 30px;
              background-color: #1890ff;
              color: white;
              border: none;
              border-radius: 4px;
              cursor: pointer;
              font-weight: bold;

              &:hover {
                background-color: #40a9ff;
              }
            }
          }
        }
      }
    }
  }
}
</style>