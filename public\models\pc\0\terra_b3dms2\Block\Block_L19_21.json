{"asset": {"gltfUpAxis": "Z", "version": "0.0"}, "geometricError": 0.14408671855926514, "root": {"boundingVolume": {"box": [26.504117965698242, -24.464595794677734, -36.439048767089844, 6.349771499633789, 0.0, 0.0, 0.0, 6.355258941650391, 0.0, 0.0, 0.0, 7.74171257019043]}, "children": [{"boundingVolume": {"box": [26.504117965698242, -24.464595794677734, -39.641700744628906, 6.349771499633789, 0.0, 0.0, 0.0, 6.355258941650391, 0.0, 0.0, 0.0, 4.539060592651367]}, "children": [{"boundingVolume": {"box": [26.504117965698242, -27.112621307373047, -39.581844329833984, 6.349771499633789, 0.0, 0.0, 0.0, 3.7072343826293945, 0.0, 0.0, 0.0, 4.479206085205078]}, "children": [{"boundingVolume": {"box": [26.504117965698242, -27.112621307373047, -39.581844329833984, 6.349771499633789, 0.0, 0.0, 0.0, 3.7072343826293945, 0.0, 0.0, 0.0, 4.479206085205078]}, "children": [{"boundingVolume": {"box": [22.270936965942383, -27.112621307373047, -37.27007293701172, 2.1165904998779297, 0.0, 0.0, 0.0, 3.7072343826293945, 0.0, 0.0, 0.0, 2.1674346923828125]}, "content": {"uri": "Block_L23_79.b3dm"}, "geometricError": 0.009365717880427837, "refine": "REPLACE"}, {"boundingVolume": {"box": [28.620708465576172, -27.112621307373047, -39.581844329833984, 4.233180999755859, 0.0, 0.0, 0.0, 3.7072343826293945, 0.0, 0.0, 0.0, 4.479206085205078]}, "content": {"uri": "Block_L23_78.b3dm"}, "geometricError": 0.009476722218096256, "refine": "REPLACE"}], "content": {"uri": "Block_L22_117.b3dm"}, "geometricError": 0.018797729164361954, "refine": "REPLACE"}], "content": {"uri": "Block_L21_82.b3dm"}, "geometricError": 0.03761042281985283, "refine": "REPLACE"}, {"boundingVolume": {"box": [26.504117965698242, -20.757362365722656, -37.488616943359375, 6.349771499633789, 0.0, 0.0, 0.0, 2.648024559020996, 0.0, 0.0, 0.0, 2.385976791381836]}, "children": [{"boundingVolume": {"box": [23.32923126220703, -20.757362365722656, -36.37461853027344, 3.1748857498168945, 0.0, 0.0, 0.0, 2.648024559020996, 0.0, 0.0, 0.0, 1.2719783782958984]}, "children": [{"boundingVolume": {"box": [21.741788864135742, -20.757362365722656, -35.848567962646484, 1.587442398071289, 0.0, 0.0, 0.0, 2.648024559020996, 0.0, 0.0, 0.0, 0.7459297180175781]}, "content": {"uri": "Block_L23_77.b3dm"}, "geometricError": 0.009339039213955402, "refine": "REPLACE"}, {"boundingVolume": {"box": [24.916675567626953, -20.757362365722656, -36.37274169921875, 1.5874433517456055, 0.0, 0.0, 0.0, 2.648024559020996, 0.0, 0.0, 0.0, 1.2701034545898438]}, "content": {"uri": "Block_L23_76.b3dm"}, "geometricError": 0.009512140415608883, "refine": "REPLACE"}], "content": {"uri": "Block_L22_116.b3dm"}, "geometricError": 0.018851913511753082, "refine": "REPLACE"}, {"boundingVolume": {"box": [29.679004669189453, -20.613914489746094, -37.483943939208984, 3.1748857498168945, 0.0, 0.0, 0.0, 2.5045766830444336, 0.0, 0.0, 0.0, 2.381305694580078]}, "children": [{"boundingVolume": {"box": [29.679004669189453, -20.613914489746094, -37.483943939208984, 3.1748857498168945, 0.0, 0.0, 0.0, 2.5045766830444336, 0.0, 0.0, 0.0, 2.381305694580078]}, "content": {"uri": "Block_L23_75.b3dm"}, "geometricError": 0.009529964067041874, "refine": "REPLACE"}], "content": {"uri": "Block_L22_115.b3dm"}, "geometricError": 0.019062068313360214, "refine": "REPLACE"}], "content": {"uri": "Block_L21_81.b3dm"}, "geometricError": 0.03787292540073395, "refine": "REPLACE"}], "content": {"uri": "Block_L20_43.b3dm"}, "geometricError": 0.07557733356952667, "refine": "REPLACE"}, {"boundingVolume": {"box": [26.78414535522461, -24.464595794677734, -31.899986267089844, 6.069745063781738, 0.0, 0.0, 0.0, 6.355258941650391, 0.0, 0.0, 0.0, 3.2026519775390625]}, "children": [{"boundingVolume": {"box": [27.43975830078125, -27.64222526550293, -32.4893798828125, 5.414131164550781, 0.0, 0.0, 0.0, 3.1776294708251953, 0.0, 0.0, 0.0, 2.6132593154907227]}, "children": [{"boundingVolume": {"box": [24.734403610229492, -27.64222526550293, -32.4893798828125, 2.7087764739990234, 0.0, 0.0, 0.0, 3.1776294708251953, 0.0, 0.0, 0.0, 2.6132593154907227]}, "children": [{"boundingVolume": {"box": [24.737823486328125, -27.64222526550293, -32.4893798828125, 2.705355644226074, 0.0, 0.0, 0.0, 3.1776294708251953, 0.0, 0.0, 0.0, 2.6132593154907227]}, "content": {"uri": "Block_L23_74.b3dm"}, "geometricError": 0.008677296340465546, "refine": "REPLACE"}], "content": {"uri": "Block_L22_114.b3dm"}, "geometricError": 0.017358195036649704, "refine": "REPLACE"}, {"boundingVolume": {"box": [30.148534774780273, -27.122339248657227, -33.03295135498047, 2.705354690551758, 0.0, 0.0, 0.0, 2.657743453979492, 0.0, 0.0, 0.0, 2.069685935974121]}, "children": [{"boundingVolume": {"box": [30.148534774780273, -27.122339248657227, -33.03295135498047, 2.705354690551758, 0.0, 0.0, 0.0, 2.657743453979492, 0.0, 0.0, 0.0, 2.069685935974121]}, "content": {"uri": "Block_L23_73.b3dm"}, "geometricError": 0.00869288481771946, "refine": "REPLACE"}], "content": {"uri": "Block_L22_113.b3dm"}, "geometricError": 0.01739199459552765, "refine": "REPLACE"}], "content": {"uri": "Block_L21_80.b3dm"}, "geometricError": 0.03478432819247246, "refine": "REPLACE"}, {"boundingVolume": {"box": [26.78414535522461, -21.28696632385254, -31.900827407836914, 6.069745063781738, 0.0, 0.0, 0.0, 3.1776294708251953, 0.0, 0.0, 0.0, 3.201810836791992]}, "children": [{"boundingVolume": {"box": [24.255084991455078, -21.28696632385254, -32.79261779785156, 3.540684700012207, 0.0, 0.0, 0.0, 3.1776294708251953, 0.0, 0.0, 0.0, 2.31002140045166]}, "children": [{"boundingVolume": {"box": [24.255084991455078, -21.28696632385254, -32.79472351074219, 3.540684700012207, 0.0, 0.0, 0.0, 3.1776294708251953, 0.0, 0.0, 0.0, 2.3079166412353516]}, "content": {"uri": "Block_L23_72.b3dm"}, "geometricError": 0.00871782936155796, "refine": "REPLACE"}], "content": {"uri": "Block_L22_112.b3dm"}, "geometricError": 0.017436115071177483, "refine": "REPLACE"}, {"boundingVolume": {"box": [30.3248291015625, -21.590288162231445, -33.76953887939453, 2.5290603637695312, 0.0, 0.0, 0.0, 2.874307632446289, 0.0, 0.0, 0.0, 1.3330974578857422]}, "children": [{"boundingVolume": {"box": [30.3248291015625, -21.590288162231445, -33.76953887939453, 2.5290603637695312, 0.0, 0.0, 0.0, 2.874307632446289, 0.0, 0.0, 0.0, 1.3330974578857422]}, "content": {"uri": "Block_L23_71.b3dm"}, "geometricError": 0.008756359107792377, "refine": "REPLACE"}], "content": {"uri": "Block_L22_111.b3dm"}, "geometricError": 0.017516475170850754, "refine": "REPLACE"}, {"boundingVolume": {"box": [30.3248291015625, -22.41144561767578, -30.568687438964844, 2.5290603637695312, 0.0, 0.0, 0.0, 2.053150177001953, 0.0, 0.0, 0.0, 1.8677558898925781]}, "children": [{"boundingVolume": {"box": [30.3248291015625, -22.41144561767578, -30.57010841369629, 2.5290603637695312, 0.0, 0.0, 0.0, 2.053150177001953, 0.0, 0.0, 0.0, 1.8663349151611328]}, "content": {"uri": "Block_L23_70.b3dm"}, "geometricError": 0.008151106536388397, "refine": "REPLACE"}], "content": {"uri": "Block_L22_110.b3dm"}, "geometricError": 0.01630960777401924, "refine": "REPLACE"}], "content": {"uri": "Block_L21_79.b3dm"}, "geometricError": 0.03424190357327461, "refine": "REPLACE"}], "content": {"uri": "Block_L20_42.b3dm"}, "geometricError": 0.0690811425447464, "refine": "REPLACE"}], "content": {"uri": "Block_L19_21.b3dm"}, "geometricError": 0.14408671855926514, "refine": "REPLACE"}}