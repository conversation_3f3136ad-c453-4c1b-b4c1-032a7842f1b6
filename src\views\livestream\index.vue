<template>
  <div>
    <div class="live" v-drag-window v-if="showOthers">
      <div style="height: 40px;width: 100%;" class="drag-title"></div>
      <a style="position: absolute;right: 10px;top: 10px;font-size: 16px;color: white;"
         @click="closeLive"
      >
        <CloseOutlined/>
      </a>
      <LiveOthers></LiveOthers>
    </div>
  </div>
</template>

<script lang="ts" setup>
import {ref} from 'vue';
import {useRoute} from 'vue-router';
import {getRoot} from '@/root';
import {ERouterName} from '@/types';
import {CloseOutlined} from '@ant-design/icons-vue';
import LiveAgora from '@/components/livestream-agora.vue';
import LiveOthers from '@/components/livestream-others.vue';
import EventBus from '@/event-bus';

const url = useRoute();
const root = getRoot();
const routeName = ref<string>('LiveOthers');
// const showLive = ref<boolean>(root.$route.name === ERouterName.LIVING)
let showLive = ref(false);
let showOthers = ref(true);
const options = [
  // {
  //   key: 0,
  //   label: 'Agora Live',
  //   path: '/' + ERouterName.LIVESTREAM + '/' + ERouterName.LIVING,
  //   routeName: 'LiveAgora',
  // },
  {
    key: 1,
    label: 'RTMP/GB28181 Live',
    path: '/' + ERouterName.LIVESTREAM + '/' + ERouterName.LIVING,
    routeName: 'LiveOthers',
  },
];
const liveother = ref()
// 关闭视频
const closeLive=()=>{
  EventBus.emit('closeLive',true)
  showOthers.value = false;
}
const selectLivestream = route => {
  if (route == 'LiveAgora') {
    showLive.value = true;
    showOthers.value = false;
  } else {
    showLive.value = false;
    showOthers.value = true;
  }
};
</script>

<style lang="scss">
.full-modal {
  .ant-modal {
    max-width: 100%;
    top: 0;
    padding-bottom: 0;
    margin: 0;
  }

  .ant-modal-content {
    display: flex;
    flex-direction: column;
    height: calc(100vh);
  }

  .ant-modal-body {
    flex: 1;
  }
}

.live {
  position: absolute;
  z-index: 99;
  left: 0;
  top: 100px;
  margin-left: 345px;
  text-align: center;
  width: 650px;
  height: 560px;
  background: #232323;
}
</style>