<template>
  <div id="DeviceTree" style="width: 100%; height: 100%; background-color: #ffffff; overflow: auto">
    <el-container>
      <el-header class="title">设备列表 <el-icon style="margin-left: 10px;cursor: pointer;" @click="reset"><Refresh /></el-icon></el-header>
      <el-main style="background-color: #ffffff">
        <div class="device-tree-main-box">
          <el-tree
            v-if="treeVisible"
            ref="gdTree"
            :props="defaultProps"
            :load="loadNode"
            lazy
            @node-click="handleNodeClick"
            @node-contextmenu="handleContextMenu"
            node-key="id"
            style="min-width: 100%; display: inline-block !important"
          >
            <template v-slot="{ node }">
              <span class="custom-tree-node" style="width: 100%">
                <span
                  v-if="node.data.type === 0 && node.data.onLine"
                  title="在线设备"
                  class="device-onLine iconfont icon-jiedianleizhukongzhongxin2"
                ><span class="isonline"></span>
              </span>
                <span
                  v-if="node.data.type === 0 && !node.data.onLine"
                  title="离线设备"
                  class="device-offline iconfont icon-jiedianleizhukongzhongxin2"
                ></span>
                <span
                  v-if="node.data.type === 2 && node.data.onLine"
                  title="目录"
                  class="device-onLine iconfont icon-jiedianleilianjipingtai"
                ><span class="isonline"></span></span>
                <span
                  v-if="node.data.type === 2 && !node.data.onLine"
                  title="目录"
                  class="device-offline iconfont icon-jiedianleilianjipingtai"
                ><span class="isonline"></span></span>
                <span
                  v-if="node.data.type === 3 && node.data.onLine"
                  title="在线通道"
                  class="device-onLine iconfont icon-shebeileijiankongdian"
                > </span>
                <span
                  v-if="node.data.type === 3 && !node.data.onLine"
                  title="在线通道"
                  class="device-offline iconfont icon-shebeileijiankongdian"
                ></span>
                <span
                  v-if="node.data.type === 4 && node.data.onLine"
                  title="在线通道-球机"
                  class="device-onLine iconfont icon-shebeileiqiuji"
                ><span class="isonline"></span></span>
                <span
                  v-if="node.data.type === 4 && !node.data.onLine"
                  title="在线通道-球机"
                  class="device-offline iconfont icon-shebeileiqiuji"
                ></span>
                <span
                  v-if="node.data.type === 5 && node.data.onLine"
                  title="在线通道-半球"
                  class="device-onLine iconfont icon-shebeileibanqiu"
                ><span class="isonline"></span></span>
                <span
                  v-if="node.data.type === 5 && !node.data.onLine"
                  title="在线通道-半球"
                  class="device-offline iconfont icon-shebeileibanqiu"
                ></span>
                <span
                  v-if="node.data.type === 6 && node.data.onLine"
                  title="在线通道-枪机"
                  class="device-onLine iconfont icon-shebeileiqiangjitongdao"
                ><span class="isonline"></span></span>
                <span
                  v-if="node.data.type === 6 && !node.data.onLine"
                  title="在线通道-枪机"
                  class="device-offline iconfont icon-shebeileiqiangjitongdao"
                ></span>
                <span v-if="node.data.onLine" style="padding-left: 1px" class="device-onLine">{{
                  node.label
                }}</span>
                <span v-if="!node.data.onLine" style="padding-left: 1px" class="device-offline">{{
                  node.label
                }}</span>
                <span>
                  <i
                    v-if="node.data.hasGPS && node.data.onLine"
                    style="color: #9d9d9d"
                    class="device-onLine iconfont icon-dizhi"
                  ></i>
                  <i
                    v-if="node.data.hasGPS && !node.data.onLine"
                    style="color: #9d9d9d"
                    class="device-offline iconfont icon-dizhi"
                  ></i>
                </span>
              </span>
            </template>
          </el-tree>
        </div>
      </el-main>
    </el-container>
  </div>
</template>

<script>
import DeviceService from '../components/service/DeviceService';

export default {
  name: 'DeviceTree',
  data() {
    return {
      deviceService: new DeviceService(),
      defaultProps: {
        children: 'children',
        label: 'name',
        isLeaf: 'isLeaf',
      },
      treeVisible: true,
    };
  },
  props: ['device', 'onlyCatalog', 'clickEvent', 'contextMenuEvent'],
  methods: {
    handleNodeClick(data, node, element) {
      let deviceNode = this.$refs.gdTree.getNode(data?.userData?.deviceId);
      if (typeof this.clickEvent == 'function') {
        this.clickEvent(deviceNode?.data?.userData, data.userData, data.type === 2);
      }
    },
    handleContextMenu(event, data, node, element) {
      console.log('右键点击事件');
      let deviceNode = this.$refs.gdTree.getNode(data?.userData?.deviceId);
      if (typeof this.contextMenuEvent == 'function') {
        this.contextMenuEvent(deviceNode?.data?.userData, event, data.userData, data.type === 2);
      }
    },
    loadNode: function (node, resolve) {
      console.log(this.device);
      if (node.level === 0) {
        if (this.device) {
          let node = {
            name: this.device.name || this.device.deviceId,
            isLeaf: false,
            id: this.device.deviceId,
            type: this.device.onLine,
            onLine: this.device.onLine,
            userData: this.device,
          };
          resolve([node]);
        } else {
          this.deviceService.getAllDeviceList(
              data => {
                if (data.length > 0) {
                  let nodeList = [];
                  for (let i = 0; i < data.length; i++) {
                    let node = {
                      name: data[i].name || data[i].deviceId,
                      isLeaf: false,
                      id: data[i].deviceId,
                      type: data[i].onLine,
                      onLine: data[i].onLine,
                      userData: data[i],
                    };
                    nodeList.push(node);
                  }
                  // nodeList.push({
                  //   name: '算法 Camera',
                  //   isLeaf: false,
                  //   id: '34020000000000000888',
                  //   type: true,
                  //   onLine: true,
                  //   userData: null,
                  // });
                  resolve(nodeList);
                } else {
                  resolve([]);
                }
              },
              list => {
                console.log('设备加载完成');
              },
          );
        }
      } else {
        if (!node.data.userData) {
          let data = [{
            id: "34020000000000000888",
            deviceId: "34020000000000000101",
            pid: "",
            name: "算法 Camera",
            parent: false,
            basicData: {
              id: "1831133324911882241",
              channelId: "34020000000000000888",
              deviceId: "34020000000000000101",
              name: "Mavic 3E Camera",
              manufacture: "DJI",
              model: "Camera",
              parental: 0,
              safetyWay: 0,
              registerWay: 0,
              certifiable: 0,
              errCode: 0,
              port: 0,
              ptzType: 0,
              createTime: "2024-09-04 08:53:00",
              updateTime: "2024-09-04 08:53:00",
              status: true,
              longitude: 0,
              latitude: 0,
              customLongitude: 0,
              customLatitude: 0,
              longitudeGcj02: 0,
              latitudeGcj02: 0,
              longitudeWgs84: 0,
              latitudeWgs84: 0,
              subCount: 0,
              hasAudio: false,
              channelType: 0,
              onLine: true,
            }
          }]
          let array = []
          array = array.concat(data);
          this.channelDataHandler(array, resolve);
          return;
        }

        let channelArray = [];
        this.deviceService.getTree(
            node.data.userData.deviceId,
            node.data.id,
            node.data.userData.deviceSn,
            this.onlyCatalog,
            catalogData => {
               //设备兼容
              if(node.data.userData.onLine){
                catalogData.forEach(item => {
                  item.basicData.status = true;
                  item.basicData.onLine = true;
                });
              }
              catalogData.forEach(item => {
                item.basicData.deviceId = node.data.userData.deviceId;
              });

              channelArray = channelArray.concat(catalogData);
              this.channelDataHandler(channelArray, resolve);
            },
            endCatalogData => {}
        );
      }
    },
    channelDataHandler: function (data, resolve) {
      if (data.length > 0) {
        let nodeList = [];
        for (let i = 0; i < data.length; i++) {
          let item = data[i];
          let type = 3;
          if (item.id?.length <= 10) {
            type = 2;
          } else {
            if (item.id?.length > 14) {
              let channelType = item.id.substring(10, 13);
              console.log('channelType: ' + channelType);
              if (channelType === '215' || channelType === '216') {
                type = 2;
              }
              console.log(type);
              if (item.basicData.ptzType === 1) {
                // 1-球机;2-半球;3-固定枪机;4-遥控枪机
                type = 4;
              } else if (item.basicData.ptzType === 2) {
                type = 5;
              } else if (item.basicData.ptzType === 3 || item.basicData.ptzType === 4) {
                type = 6;
              }
            } else {
              if (item.basicData.subCount > 0 || item.basicData.parental === 1) {
                type = 2;
              }
            }
          }
          let node = {
            name: item.name || item.basicData.channelId,
            isLeaf: type !== 2,
            id: item.id,
            deviceId: item.deviceId,
            type: type,
            onLine: item.basicData.onLine,
            hasGPS: item.basicData.longitude && item.basicData.latitude,
            userData: item.basicData,
          };
          nodeList.push(node);
        }
        resolve(nodeList);
      } else {
        resolve([]);
      }
    },
    reset() {
      this.treeVisible = false;

      this.$nextTick(() => {
        this.treeVisible = true;
      });
    },
  },
  destroyed() {
    // if (this.jessibuca) {
    //   this.jessibuca.destroy();
    // }
    // this.playing = false;
    // this.loaded = false;
    // this.performance = "";
  },
};
</script>

<style>
.device-tree-main-box {
  text-align: left;
}

.device-onLine {
  color: #1aad19;
}

.device-onLine::before {
  color: #1aad19;
  display: inline-block;
  width: 1em;
}

.isonline::after {
  content: "•";
  color: #1aad19;
  display: inline-block;
  width: 1em;
}

.device-offline {
  color: #727272;
}

.title {
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>