<template>
  <div>
    <div id="g-container"></div>
  </div>
</template>

<script setup>
import { onMounted, watch, computed } from 'vue';
import { deviceTsaUpdate } from '@/hooks/use-c-map-tsa';
import { EDeviceTypeName } from '../../types';
import store from '@/store';
import { getApp } from '@/root';
import { useGMapManage } from '@/hooks/use-c-map';

const osdVisible = computed(() => {
  return store.state.dock.osdVisible
})
const deviceTsaUpdateHook = deviceTsaUpdate();

onMounted(() => {
  const app = getApp();
  useGMapManage().globalPropertiesConfig(app);
  // Cesium.Ion.defaultAccessToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************.rNfX8CPs-BUkQ6RImFXniBSIaefxrr62a7y6iIQN34w'

  // viewer = new Cesium.Viewer("g-container", {
  //   timeline: false,
  //   geocoder: false,
  //   animation: false,
  //   homeButton: false,
  //   baseLayerPicker: false,
  //   sceneModePicker: false,
  //   fullscreenButton: false,
  //   navigationHelpButton: false,
  //   selectionIndicator: false,
  //   infoBox: false,
  //   terrainProvider: Cesium.createWorldTerrain({
  //     requestVertexNormals: true
  //   })
  // });
  // deviceTsaUpdateHook = deviceTsaUpdate(viewer);

  // viewer.camera.flyTo({
  //   // 从以度为单位的经度和纬度值返回笛卡尔3位置。
  //   destination: Cesium.Cartesian3.fromDegrees(120.23701451607589, 30.302147490000163, 200000),
  //   orientation: {
  //     // heading：默认方向为正北，正角度为向东旋转，即水平旋转，也叫偏航角。
  //     // pitch：默认角度为-90，即朝向地面，正角度在平面之上，负角度为平面下，即上下旋转，也叫俯仰角。
  //     // roll：默认旋转角度为0，左右旋转，正角度向右，负角度向左，也叫翻滚角
  //     heading: Cesium.Math.toRadians(0.0), // 正东，默认北
  //     pitch: Cesium.Math.toRadians(-90), // 向正下方看
  //     roll: 0.0, // 左右
  //   },
  //   duration: 1, // 飞行时间（s）
  // });

  // /** 加载高德地图 */
  // // 加载高德影像底图
  // const gdBasicLayer = new Cesium.ImageryLayer(new Cesium.UrlTemplateImageryProvider({
  //   url: 'https://webst02.is.autonavi.com/appmaptile?style=6&x={x}&y={y}&z={z}',
  //   style: 'default',
  //   format: 'image/png',
  //   maximumLevel: 18,
  //   tileMatrixSetID: 'GoogleMapsCompatible',
  // }));

  // // 加载高德地图影像地理标签
  // const gdLabelLayer = new Cesium.ImageryLayer(new Cesium.UrlTemplateImageryProvider({
  //   url: 'http://webst02.is.autonavi.com/appmaptile?x={x}&y={y}&z={z}&lang=zh_cn&size=1&scale=1&style=8',
  //   style: 'default',
  //   format: 'image/jpeg',
  //   maximumLevel: 18,
  //   tileMatrixSetID: 'GoogleMapsCompatible',
  // }));

  // viewer.imageryLayers.add(gdBasicLayer);
  // viewer.imageryLayers.add(gdLabelLayer);

});
watch(() => store.state.dock.deviceState,
  data => {
    if (store.state.common.jscShow) {
      return;
    }
    //遥控器
    if (data.currentType === EDeviceTypeName.Gateway && data.gatewayInfo[data.currentSn]) {
      const { longitude, latitude } = data.gatewayInfo[data.currentSn]
      if (longitude && latitude) {
        // ykqWorker.postMessage({deviceSn: data.currentSn, longitude: longitude || 0, latitude: latitude || 0 })
        // ykqWorker.onmessage = ({ data: { deviceSn,coordinate } }) => {
        deviceTsaUpdateHook.moveTo(data.currentSn, longitude, latitude, 1)
        // };
      }
      if (osdVisible.value.visible && osdVisible.value.gateway_sn !== '') {
        deviceInfo.gateway = data.gatewayInfo[osdVisible.value.gateway_sn]
      }
    }
    //无人机
    if (data.currentType === EDeviceTypeName.Aircraft && data.deviceInfo[data.currentSn]) {
      const { longitude, latitude } = data.deviceInfo[data.currentSn]
      if (longitude && latitude) {
        // wrjWorker.postMessage({ deviceSn: data.currentSn,longitude: longitude || 0, latitude: latitude || 0 })
        // wrjWorker.onmessage = ({ data: { deviceSn,coordinate } }) => {
        deviceTsaUpdateHook.moveTo(data.currentSn, longitude, latitude)
        //
        // };
      }
      if (osdVisible.value.visible && osdVisible.value.sn !== '') {
        deviceInfo.device = data.deviceInfo[osdVisible.value.sn]
      }
    }
    //机场数据
    if (data.currentType === EDeviceTypeName.Dock && data.dockInfo[data.currentSn]) {
      if (!data.dockInfo[data.currentSn]?.basic_osd) {
        return;
      }
      const { longitude, latitude } = data.dockInfo[data.currentSn]?.basic_osd;
      if (longitude && latitude) {
        deviceTsaUpdateHook.initMarker(EDeviceTypeName.Dock, EDeviceTypeName[EDeviceTypeName.Dock], data.currentSn, longitude, latitude)
      }
      if (osdVisible.value.visible && osdVisible.value.is_dock) {
        deviceInfo.dock = data.dockInfo[osdVisible.value.gateway_sn]
        deviceInfo.device = data.deviceInfo[deviceInfo.dock.basic_osd.sub_device?.device_sn ?? osdVisible.value.sn]
      }
    }
  }, {
  deep: true
})

</script>

<style scoped>
/* 隐藏左下角图标logo */
:deep(.cesium-viewer-bottom) {
  display: none;
}

#g-container {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
}
</style>
