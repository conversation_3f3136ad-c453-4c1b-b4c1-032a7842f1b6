<template>
  <div class="ml20 mt20 mr20 flex-row flex-align-center flex-justify-between">
    <div class="flex-row">
      <a-button type="primary" @click="sVisible = true" v-permission> 点击上传 </a-button>
      <common-modal
        title="导入固件文件"
        v-model:visible="sVisible"
        :btnDisabled="confirmLoading"
        @cancel="onCancel"
        @submit="onUpload"
      >
        <a-form :rules="rules" ref="formRef" :model="uploadParam" :label-col="{ span: 6 }">
          <a-form-item name="status" label="状态" required>
            <a-switch v-model:checked="uploadParam.status" />
          </a-form-item>
          <a-form-item name="device_name" label="设备名称" required>
            <a-select style="width: 300px" mode="multiple" v-model:value="uploadParam.device_name">
              <a-select-option v-for="k in DeviceNameEnum" :key="k" :value="k">
                {{ k }}
              </a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item name="release_note" label="创建说明" required>
            <a-textarea v-model:value="uploadParam.release_note" showCount :maxlength="300" />
          </a-form-item>
          <a-form-item label="文件" required>
            <a-upload
              :multiple="false"
              :before-upload="beforeUpload"
              :show-upload-list="true"
              :file-list="fileList"
              :remove="removeFile"
              @change="selectFile"
            >
              <a-button type="primary">
                <UploadOutlined />
                导入固件文件
              </a-button>
            </a-upload>
          </a-form-item>
        </a-form> 
      </common-modal>
    </div>
    <div class="flex-row">
      <div class="ml5">
        状态:
        <a-select
          style="width: 150px"
          v-model:value="param.firmware_status"
          @select="getAllFirmwares(pageParam)"
        >
          <a-select-option v-for="(key, value) in FirmwareStatusEnum" :key="key" :value="value">
            {{ key }}
          </a-select-option>
        </a-select>
      </div>
      <div class="ml5">
        设备品牌:
        <a-select
          style="width: 150px"
          v-model:value="param.device_name"
          @select="getAllFirmwares(pageParam)"
        >
          <a-select-option v-for="item in deviceNameList" :key="item.label" :value="item.value">
            {{ item.label }}
          </a-select-option>
        </a-select>
      </div>
      <div class="ml5" style="display: flex">
        <div style="line-height: 32px; margin-right: 4px">固件版本:</div>
        <a-input-search
          :enter-button="true"
          allowClear
          v-model:value="param.product_version"
          placeholder="请输入固件版本"
          style="width: 220px"
          @search="getAllFirmwares(pageParam)"
        />
      </div>
    </div>
  </div>

  <div class="table flex-display flex-column">
    <a-table
      :columns="columns"
      :data-source="data.firmware"
      :pagination="paginationProp"
      @change="refreshData"
      row-key="firmware_id"
      :rowClassName="(record, index) => (index % 2 === 0 ? 'table-striped' : null)"
      :scroll="{ x: '100%', y: 600 }"
    >
      <template #device_name="{ record }">
        <div v-for="text in record.device_name" :key="text">
          {{ text }}
        </div>
      </template>

      <template #file_size="{ record }">
        <div>{{ bytesToSize(record.file_size) }}</div>
      </template>

      <template #firmware_status="{ record }">
        <DeviceFirmwareStatus :firmware="record" />
      </template>

      <template v-for="col in ['file_name', 'release_note']" #[col]="{ text }" :key="col">
        <a-tooltip :title="text">
          <span>{{ text }}</span>
        </a-tooltip>
      </template>

      <template #action="{ record }">
        <a-popconfirm
          title="是否确定删除该固件版本数据?"
          ok-text="是"
          cancel-text="否"
          @confirm="handleDelete(record.firmware_id)"
        >
          <a-button type="danger" size="small" v-permission>删除</a-button>
        </a-popconfirm>
      </template>
    </a-table>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, reactive, ref } from 'vue';
import { getFirmwares, importFirmareFile, removeFirmware } from '@/api/firmwares/index';
import DeviceFirmwareStatus from './DeviceFirmwareStatus.vue';
import {
  Firmware,
  FirmwareQueryParam,
  FirmwareStatusEnum,
  DeviceNameEnum,
  FirmwareUploadParam,
} from '@/types/device-firmware';
import moment from 'moment';
import { IPage } from '@/api/http/type';
import { ELocalStorageKey } from '@/types';
import { commonColor } from '@/utils/color';
import { bytesToSize } from '@/utils/bytes';
import { UploadOutlined } from '@ant-design/icons-vue';
import { message, notification } from 'ant-design-vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { TableState } from 'ant-design-vue/lib/table/interface';
//
import { convertFileSizeUnit } from './fileUtil/until';
import { CHUNK_SIZE } from './fileUtil/index';
import cutFile from './fileUtil/cutFile';
import { MerkleTree } from './fileUtil/MerkleTree';
import { checkFileByMd5, initMultPartFile, mergeFileByMd5 } from './fileUtil/api';
import type { UploadFileInfoType } from './fileUtil/api/typing';

import { HttpCodeUploadEnum } from './fileUtil/enum';
import axios from 'axios';
import pLimit from 'p-limit';
import JSZip from 'jszip';
import CommonModal from '@/components/common-modal/index.vue';
import SparkMD5 from 'spark-md5'
import { getWorkspaceId } from '@/utils/storage'

interface FirmwareData {
  firmware: Firmware[];
}

const columns = [
  {
    title: '模型',
    dataIndex: 'device_name',
    width: 150,
    ellipsis: true,
    className: 'titleStyle',
    slots: { customRender: 'device_name' },
  },
  {
    title: '文件名',
    dataIndex: 'file_name',
    width: 220,
    ellipsis: true,
    className: 'titleStyle',
    slots: { customRender: 'file_name' },
  },
  { title: '固件版本', dataIndex: 'product_version', width: 130, className: 'titleStyle' },
  {
    title: '文件大小',
    dataIndex: 'file_size',
    width: 130,
    className: 'titleStyle',
    slots: { customRender: 'file_size' },
  },
  { title: '创建者', dataIndex: 'username', width: 150, className: 'titleStyle' },
  {
    title: '创建时间',
    dataIndex: 'create_time',
    width: 150,
    className: 'titleStyle',
  },
  {
    title: '发布日期',
    dataIndex: 'released_time',
    width: 150,
    sorter: (a: Firmware, b: Firmware) => a.released_time.localeCompare(b.released_time),
    className: 'titleStyle',
  },
  {
    title: '创建说明',
    dataIndex: 'release_note',
    ellipsis: true,
    className: 'titleStyle',
    slots: { customRender: 'release_note' },
  },
  {
    title: '状态',
    dataIndex: 'firmware_status',
    width: 100,
    className: 'titleStyle',
    slots: { customRender: 'firmware_status' },
  },
  {
    title: '操作',
    dataIndex: 'action',
    key: 'action',
    fixed: 'right',
    width: 120,
    slots: { customRender: 'action' },
  },
];

const data = reactive<FirmwareData>({
  firmware: [],
});

const paginationProp = reactive({
  pageSizeOptions: ['10', '20', '50', '100'],
  showQuickJumper: true,
  showSizeChanger: true,
  pageSize: 10,
  current: 1,
  total: 0,
});

const deviceNameList = ref<any[]>([{ label: '全部', value: '' }]);

type Pagination = TableState['pagination'];

const pageParam: IPage = {
  page: 1,
  total: 0,
  page_size: 10,
};
const workspaceId: string = getWorkspaceId();

const param = reactive<FirmwareQueryParam>({
  product_version: '',
  device_name: '',
  firmware_status: FirmwareStatusEnum.NONE,
});

onMounted(() => {
  getAllFirmwares(pageParam);
  for (const key in DeviceNameEnum) {
    const value = DeviceNameEnum[key];
    deviceNameList.value.push({ label: value, value: value });
  }
});

function refreshData(page: Pagination) {
  pageParam.page = page?.current!;
  pageParam.page_size = page?.pageSize!;
  getAllFirmwares(pageParam);
}

function getAllFirmwares(page: IPage) {
  getFirmwares(workspaceId, page, param).then(res => {
    const firmwareList: Firmware[] = res.data.list;
    data.firmware = firmwareList;
    paginationProp.total = res.data.pagination.total;
    paginationProp.current = res.data.pagination.page;
  });
}

const handleDelete = async firmwareId => {
  console.log('id', firmwareId);
  try {
    const res = await removeFirmware(workspaceId, firmwareId);
    ElMessage({
      type: 'success',
      message: '删除成功！',
    });
    getAllFirmwares(pageParam);
  } catch (e) {
    ElMessage({
      type: 'error',
      message: '删除失败！',
    });
  }
};

const sVisible = ref(false);
const uploadParam = reactive<FirmwareUploadParam>({
  device_name: [],
  release_note: '',
  status: true,
});

const rules = {
  status: [{ required: true }],
  device_name: [{ required: true, message: '请选择设备名称' }],
  release_note: [{ required: true, message: '请填写创建说明' }],
};

interface FileItem {
  uid: string;
  name?: string;
  status?: string;
  response?: string;
  url?: string;
}

const fileList = ref<FileItem[]>([]);

function beforeUpload(file: FileItem) {
  if (!file.name || !file.name?.endsWith('.zip')) {
    message.error('格式错误。请选择zip文件。');
    return false;
  }
  fileList.value = [file];
  return false;
}

const formRef = ref();

function removeFile(file: FileItem) {
  fileList.value = [];
}

function onCancel() {
  formRef.value.resetFields();
  fileList.value = [];
  sVisible.value = false;
  confirmLoading.value = true;
}

// const uploadFile = async () => {
//   if (fileList.value.length === 0) {
//     message.error('请至少选择一个文件!');
//   }
//   let uploading: string;
//   formRef.value.validate().then(async () => {
//     const file: FileItem = fileList.value[0];
//     const fileData = new FormData();
//     fileData.append('file', file as any, file.name);
//     Object.keys(uploadParam).forEach(key => {
//       const val = uploadParam[key as keyof FirmwareUploadParam];
//       if (val instanceof Array) {
//         val.forEach(value => {
//           fileData.append(key, value);
//         });
//       } else {
//         fileData.append(key, val.toString());
//       }
//     });
//     const timestamp = new Date().getTime();
//     uploading = (file.name ?? 'uploding') + timestamp;
//     notification.open({
//       key: uploading,
//       message: `上传中  ${moment().format('YYYY-MM-DD HH:mm:ss')}`,
//       description: `[${file.name}] 正在上传... `,
//       duration: null,
//     });
//     importFirmareFile(workspaceId, fileData)
//       .then(res => {
//         if (res.code === 0) {
//           notification.success({
//             message: `已上传  ${moment().format('YYYY-MM-DD HH:mm:ss')}`,
//             description: `[${file.name}] 文件上传成功。上传时间: ${moment
//               .duration(new Date().getTime() - timestamp)
//               .asSeconds()} s`,
//             duration: null,
//           });
//           getAllFirmwares(pageParam);
//         } else {
//           notification.error({
//             message: `上传失败 [${file.name}]. 请检查并重试`,
//             description: `错误信息: ${res.message} ${moment().format('YYYY-MM-DD HH:mm:ss')}`,
//             style: { color: commonColor.FAIL },
//             duration: null,
//           });
//         }
//       })
//       .finally(() => {
//         notification.close(uploading);
//       });
//     fileList.value = [];
//     formRef.value.resetFields();
//     sVisible.value = false;
//   });
// };
/**
 * 选择附件
 */
/** 分片上传时的 file 和上传地址 url */
const limit = pLimit(3);
type ChunkFileUrlType = {
  url: string;
  file: Blob;
};
const state = reactive<{ dataSource: FileTableDataType[] }>({
  dataSource: [],
});
/** 表格数据类型 */
type FileTableDataType = {
  uid?: string;
  name: string;
  size: number;
  unitSize: string;
  md5: string;
  md5Progress: number;
  progress: number;
  file: File;
  chunkCount: number;
  /** 当前文件分片集合 */
  chunkFileList: Blob[];
  /** 已上传的文件大小总和（计算进度条） */
  uploadedSize: number;
  /** 计算MD5中（加载中） | 等待上传 | 上传中  | 上传成功 | 上传失败 */
  status: 'preparation' | 'preupload' | 'uploading' | 'success' | 'error';
  fileName?: string;
};
// let file_name = ref('');
// loading 确认提交上传固件
let confirmLoading = ref(true);
function md5DigestAsHex(input){
  const hashArray = SparkMD5.ArrayBuffer.hash(input);
  return hashArray;
}
async function selectFile(_: any, fileItem: FileItem) {
  const file = _.file;
  if (!file) return;
  const fileName = await unzipReadFile(file);
  const reader = new FileReader();
  let md5='';
  reader.onload = function(e) {
    const arrayBuffer = e.target.result;
    md5 = md5DigestAsHex(arrayBuffer);
    // console.log(md5);
    confirmLoading.value = false;
  };
  reader.readAsArrayBuffer(file);
  if (!fileName) {
    message.error('文件格式错误，请选择正确的文件');
    fileList.value = [];
    return;
  }
  // message.success('文件解析完成');
  // file_name.value = fileName;
  const chunkCount = Math.ceil((file.size ?? 0) / CHUNK_SIZE);
  // 展示给 table的数据，部分参数用于初始化
  const dataItem: FileTableDataType = {
    // uid: fileItem.uid,
    name: file.name,
    size: file.size ?? 0,
    unitSize: convertFileSizeUnit(file.size),
    md5: '',
    md5Progress: 0,
    progress: 0,
    chunkCount,
    file: file,
    status: 'preparation',
    chunkFileList: [],
    uploadedSize: 0,
    fileName,
  };
  state.dataSource.push(dataItem);
  const i = state.dataSource.findIndex(item => item.uid == dataItem.uid);
  // 同步计算分片文件和 md5，实时更新计算进度
  // const { md5, chunkFileList } = await createChunkFileAndMd5(
  //   file as RcFile,
  //   chunkCount,
  //   (progress) => {
  //     state.dataSource[i].md5Progress = progress
  //   },
  // )

  // 采用多线程计算和默克尔树计算树根
  const chunks = await cutFile(file);
  // const merkleTree = new MerkleTree(chunks.map(chunk => chunk.hash));
  // const md5 = merkleTree.getRootHash();
  const chunkFileList = chunks.map(chunk => chunk.blob);
  // console.log(md5, chunkFileList)

  // 更新数据和状态
  state.dataSource[i] = {
    ...state.dataSource[i],
    md5,
    chunkFileList,
    status: 'preupload',
  };
}
/**
 * 解压文件
 */

async function unzipReadFile(file) {
  try {
    const zip = new JSZip();
    const zipData = await zip.loadAsync(file);
    const fileNames = Object.keys(zipData.files);
    for (const fileName of fileNames) {
      const file = zipData.files[fileName];
      console.log('fileName', file.name);
      if (file.name.indexOf('.pro.cfg.sig') > -1) {
        const name = file.name.split('/');
        return name.length > 1 ? name[1] : name[0];
      }
    }
  } catch (e) {
    console.log('error', e);
    //TODO handle the exception
  }
}
// 查询文件状态并上传
const onUpload = async () => {
  if (fileList.value.length === 0) {
    message.error('请至少选择一个文件!');
    return;
  }
  // if(!file_name.value){
  // message.error('请等待文件解析');
  // return
  // }
  formRef.value.validate().then(async () => {
    for (let i = 0; i < state.dataSource.length; i++) {
      // md5 未计算完成和正在上传的跳过（重复点击的情况）
      if (!state.dataSource[i].md5 || state.dataSource[i].status == 'uploading') continue;
      await uploadFileNext(i, state.dataSource[i]);
    }
    await onCancel();
  });
};

/**
 * 上传处理方法
 * @param index 如果直接修改 item，在上传过程中，item一直在被更改，而循环传入的 item 却一直是初始值，因此需要 index 确定当前 item 的最新值
 * @param item
 */
const uploadFileNext = async (index: number, item: FileTableDataType) => {
  const { code, data } = await checkFileByMd5(item.md5, item.name);
  state.dataSource[index].status = 'uploading';

  if (code === HttpCodeUploadEnum.SUCCESS) {
    //  上传成功
    state.dataSource[index].progress = 100;
    state.dataSource[index].status = 'success';
    message.info('该文件已经上传过，请重新选择！');
    return;
  } else if (code === HttpCodeUploadEnum.FAIL) {
    //  上传失败
    state.dataSource[index].status = 'error';
    return;
  } /*  else if (code === HttpCodeUploadEnum.UPLOADING) {
        // 上传中，返回已上传的文件数据和分片列表
      } else {
        // 未上传
      } */

  // 返回需要上传分片和对应地址
  const needUploadFile = await initSliceFile(item, data);
  console.log('需要上传的文件', needUploadFile);
  const totalSize = needUploadFile.reduce((pre, cur) => pre + cur.file.size, 0);
  const file: FileItem = fileList.value[0];
  const timestamp = new Date().getTime();
  const uploading = (file.name ?? 'uploding') + timestamp;
  // plimit 并发上传
  const uploadLimit = needUploadFile.map(n =>
    limit(() =>
      uploadChunkUrl(n, index, totalSize, item.file.type, uploading, file.name, timestamp)
    )
  );

  const results = await Promise.allSettled(uploadLimit);
  const errResults = results.filter(r => r.status === 'rejected');

  if (errResults.length > 0) {
    console.warn(item.name + ' 上传失败的分片-----', errResults);
    state.dataSource[index].status = 'error';
    return;
  }

  try {
    const { code, data } = await mergeFileByMd5(item.md5);
    state.dataSource = [];
    getAllFirmwares(pageParam);
    // if (code === 200) {
    //   console.log(data);
    //   state.dataSource[index].status = 'success';
    //   state.dataSource[index].progress = 100;
    // }
  } catch (error) {
    notification.error({
      message: `上传失败 [${file.name}]. 请检查并重试`,
      description: `错误信息: ${error.message} ${moment().format('YYYY-MM-DD HH:mm:ss')}`,
      style: { color: commonColor.FAIL },
      duration: null,
    });
    state.dataSource[index].status = 'error';
  }
};
// 初始化分片操作并将分片文件和其上传地址一一对应
const initSliceFile = async (item: FileTableDataType, initData: UploadFileInfoType) => {
  //  只有上传中的分片文件才会有 initData 数据，用 {} 做兜底
  const { upload_id, list_parts } = initData || {};
  let deviceFirmwareUploadParam = {};
  // 初始化分片参数
  const param: any = {
    upload_id,
    origin_file_name: item.name,
    size: item.size,
    chunk_size: CHUNK_SIZE,
    chunk_count: item.chunkCount,
    md5: item.md5,
    content_type: item.file.type,
    zip_file_name: item.fileName,
  };
  console.log('分片参数', uploadParam);
  Object.keys(uploadParam).forEach(key => {
    const val = uploadParam[key as keyof FirmwareUploadParam];
    deviceFirmwareUploadParam[key] = val;
  });
  param.device_firmware_upload_param = deviceFirmwareUploadParam;
  const needUploadFile: ChunkFileUrlType[] = [];

  const {
    data: { code, data },
  } = await initMultPartFile(param);
  if (code !== 200) return [];
  // 全部上传
  // 存放需要去上传的文件数据
  // if ((list_parts || []).length == 0) {
  // 若全都没有上传，一一对应，其中 urls 是所有分片上传的 url 集合
  item.chunkFileList.forEach((item, index) => {
    needUploadFile.push({ url: data.urls[index], file: item });
  });
  return needUploadFile;
  // }

  // 存在上传的，对比 minio 已上传的 listParts（序号），将已上传的过滤掉，只上传未上传的文件
  // item.chunkFileList.forEach((item, index) => {
  //   // listParts 索引是从 1 开始的
  //   const i = (list_parts || []).findIndex(v => index + 1 == v);
  //   if (i === -1) {
  //     needUploadFile.push({ url: data.urls[index], file: item });
  //   }
  // });

  return needUploadFile;
};
// 根据分片上传地址将分片直传至 minio
const uploadChunkUrl = (
  chunkItem: ChunkFileUrlType,
  i: number,
  totalSize: number,
  type: string,
  key: string,
  fileName: string,
  timestamp: number
): Promise<void> => {
  return new Promise((resolve, reject) => {
    axios
      .put(chunkItem.url, chunkItem.file, {
        headers: { 'Content-Type': type || 'application/octet-stream' },
      })
      .then(res => {
        if (res.status !== 200) {
          reject(chunkItem);
        } else {
          // 已上传的文件大小更新，上传进度更新
          const newUploaedSize = state.dataSource[i].uploadedSize + chunkItem.file.size;
          const progress = Math.floor((newUploaedSize / totalSize) * 100);
          state.dataSource[i] = {
            ...state.dataSource[i],
            uploadedSize: newUploaedSize,
            progress,
          };
          notification.open({
            key,
            message: `上传中  ${moment().format('YYYY-MM-DD HH:mm:ss')},进度：${progress}%`,
            description: `[${fileName}] 正在上传... `,
            duration: null,
          });
          if (progress == 100) {
            notification.success({
              key,
              message: `已上传  ${moment().format('YYYY-MM-DD HH:mm:ss')}`,
              description: `[${fileName}] 文件上传成功。上传时间: ${moment
                .duration(new Date().getTime() - timestamp)
                .asSeconds()} s`,
              duration: 2,
            });
          }
          resolve();
        }
      })
      .catch(err => {
        console.error(err);
        reject(chunkItem);
      });
  });
};
</script>

<style lang="scss" scoped>
.table {
  background-color: white;
  margin: 20px;
  padding: 20px;
  height: 76vh;
}

.table-striped {
  background-color: #f7f9fa;
}

.ant-table {
  border-top: 1px solid rgb(0, 0, 0, 0.06);
  border-bottom: 1px solid rgb(0, 0, 0, 0.06);
}

.ant-table-tbody tr td {
  border: 0;
}

.ant-table td {
  white-space: nowrap;
}

.ant-table-thead tr th {
  background: white !important;
  border: 0;
}

th.ant-table-selection-column {
  background-color: white !important;
}

.ant-table-header {
  background-color: white !important;
}

.action-area {
  &::v-deep {
    .ant-btn {
      margin-right: 10px;
      margin-bottom: 10px;
    }
  }
}

:deep(.ant-table-fixed-header > .ant-table-content > .ant-table-scroll > .ant-table-body) {
  height: 510px;
}
</style>
