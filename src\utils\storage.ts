import { <PERSON><PERSON><PERSON>age<PERSON><PERSON>, ELocal<PERSON><PERSON>age<PERSON>ey } from '@/types/enums'
import { consoleWarn } from './logger'

function getStorageData (key: EStorageKey, parse?: boolean): string | null
function getStorageData<T> (key: EStorageKey, parse?: boolean): T | null
function getStorageData (key: EStorageKey, parse?: boolean): any {
  const value = window.localStorage.getItem(key)
  if (parse && value) {
    try {
      const result = JSON.parse(value)
      return result
    } catch (e) {
      consoleWarn('appStorage.get failed, err:', e)
      return null
    }
  } else {
    return value
  }
}

function clearStorageData (key: EStorageKey | EStorageKey[]) {
  let keyList: EStorageKey[] = []
  if (Array.isArray(key)) {
    keyList = key
  } else {
    keyList = [key]
  }
  keyList.forEach(item => {
    window.localStorage.removeItem(item)
  })
}

const appStorage = {
  save (key: <PERSON><PERSON><PERSON>age<PERSON><PERSON>, value: string) {
    window.localStorage.setItem(key, value)
  },
  get: getStorageData,

  clear: clearStorageData,
}

export const getWorkspaceId = (): string => localStorage.getItem(ELocalStorageKey.WorkspaceId) || 'e3dea0f5-37f2-4d79-ae58-490af3228069'

export default appStorage
