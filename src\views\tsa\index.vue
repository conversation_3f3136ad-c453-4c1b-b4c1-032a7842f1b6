<template>
  <div class="project-app-wrapper">
    <div>
      <!-- <Sidebar /> -->
      <div class="main-content uranus-scrollbar dark">
        <!-- <router-view /> -->
        <div class="scrollbar">
          <a-collapse :bordered="false" expandIconPosition="right" accordion v-model:activeKey="activeKey"
            :class="thType ? 'airportBgD' : 'airportBgcode'">
            <a-collapse-panel key="1" header="机场">
              <div v-if="onlineDocks.data.length === 0" style="height: 150px; color: white">
                <a-empty :image-style="{ height: '60px' }" />
              </div>
              <div v-else class="fz12" style="color: white">
                <div v-for="dock in onlineDocks.data" :key="dock.sn"
                  style="background: #3c3c3c; height: 90px; width: 250px; margin-bottom: 10px">
                  <div style="border-radius: 2px; height: 100%; width: 100%"
                    class="flex-row flex-justify-between flex-align-center">
                    <div style="float: left; padding: 0px 5px 8px 8px; width: 88%">
                      <div style="width: 80%; height: 30px; line-height: 30px; font-size: 16px">
                        <a-tooltip :title="`${dock.gateway.callsign} - ${dock.callsign ?? 'No Drone'}`">
                          <div class="text-hidden" style="max-width: 200px">
                            {{ dock.gateway.callsign }} - {{ dock.callsign ?? 'No Drone' }}
                          </div>
                        </a-tooltip>
                      </div>
                      <div class="mt5 flex-align-center flex-row flex-justify-between" style="background: #595959">
                        <div class="flex-align-center flex-row">
                          <span class="ml5 mr5" @click.stop="getAirport('机场', dock.gateway.sn)" style="cursor: pointer;">
                            <RobotOutlined />
                          </span>
                          <div class="font-bold text-hidden" style="max-width: 80px" :style="dockInfo[dock.gateway.sn] &&
                              dockInfo[dock.gateway.sn].basic_osd?.mode_code !==
                              EDockModeCode.未连接
                              ? 'color: #00ee8b'
                              : 'color: red;'
                            ">
                            {{ dockInfo[dock.gateway.sn] ? EDockModeCode[dockInfo[dock.gateway.sn].basic_osd?.mode_code]
                              : EDockModeCode[EDockModeCode.未连接] }}

                          </div>
                        </div>
                        <div class="mr5 flex-align-center flex-row" style="width: 85px; margin-right: 0; height: 18px">
                          <div v-if="hmsInfo[dock.gateway.sn]" class="flex-align-center flex-row">
                            <div :class="hmsInfo[dock.gateway.sn][0].level === EHmsLevel.CAUTION
                                ? 'caution-blink'
                                : hmsInfo[dock.gateway.sn][0].level === EHmsLevel.WARN
                                  ? 'warn-blink'
                                  : 'notice-blink'
                              " style="width: 18px; height: 16px; text-align: center">
                              <span :style="hmsInfo[dock.gateway.sn].length > 99
                                  ? 'font-size: 11px'
                                  : 'font-size: 12px'
                                ">{{ hmsInfo[dock.gateway.sn].length }}</span>
                              <span class="fz10">{{
                                hmsInfo[dock.gateway.sn].length > 99 ? '+' : ''
                              }}</span>
                            </div>
                            <a-popover trigger="click" placement="bottom" color="black"
                              v-model:visible="hmsVisible[dock.gateway.sn]"
                              @visibleChange="readHms(hmsVisible[dock.gateway.sn], dock.gateway.sn)"
                              :overlayStyle="{ width: '200px', height: '300px' }">
                              <div :class="hmsInfo[dock.gateway.sn][0].level === EHmsLevel.CAUTION
                                  ? 'caution'
                                  : hmsInfo[dock.gateway.sn][0].level === EHmsLevel.WARN
                                    ? 'warn'
                                    : 'notice'
                                " style="margin-left: 3px; width: 62px; height: 16px">
                                <span class="word-loop">{{
                                  hmsInfo[dock.gateway.sn][0].message_zh
                                }}</span>
                              </div>
                              <template #content>
                                <a-collapse style="background: black; height: 300px; overflow-y: auto" :bordered="false"
                                  expand-icon-position="right" :accordion="true">
                                  <a-collapse-panel v-for="hms in hmsInfo[dock.gateway.sn]" :key="hms.hms_id"
                                    :showArrow="false" style="
                                      margin: 0 auto 3px auto;
                                      border: 0;
                                      width: 140px;
                                      border-radius: 3px;
                                    " :class="hms.level === EHmsLevel.CAUTION
                                        ? 'caution'
                                        : hms.level === EHmsLevel.WARN
                                          ? 'warn'
                                          : 'notice'
                                      ">
                                    <template #header>
                                      <div class="flex-row flex-align-center" style="width: 130px">
                                        <div style="width: 110px">
                                          <span class="word-loop">{{ hms.message_zh }}</span>
                                        </div>
                                        <div style="
                                            width: 20px;
                                            height: 15px;
                                            font-size: 10px;
                                            z-index: 2;
                                          " class="flex-row flex-align-center flex-justify-center" :class="hms.level === EHmsLevel.CAUTION
                                              ? 'caution'
                                              : hms.level === EHmsLevel.WARN
                                                ? 'warn'
                                                : 'notice'
                                            ">
                                          <DoubleRightOutlined :rotate="isActive ? 90 : 0" />
                                        </div>
                                      </div>
                                    </template>

                                    <a-tooltip :title="hms.create_time">
                                      <div style="color: white" class="text-hidden">
                                        {{ hms.create_time }}
                                      </div>
                                    </a-tooltip>
                                  </a-collapse-panel>
                                </a-collapse>
                              </template>
                            </a-popover>
                          </div>
                          <div v-else class="width-100" style="height: 90%; background: rgba(0, 0, 0, 0.35)"></div>
                        </div>
                      </div>
                      <div class="mt5 flex-align-center flex-row flex-justify-between" style="background: #595959">
                        <div class="flex-row">
                          <span class="ml5 mr5" @click.stop="getAirport('无人机', dock.sn)" style="cursor: pointer;">
                            <RocketOutlined />
                          </span>
                          <div class="font-bold text-hidden" style="max-width: 80px" :style="deviceInfo[dock.sn] &&
                              deviceInfo[dock.sn].mode_code !== EModeCode.未连接
                              ? 'color: #00ee8b'
                              : 'color: red;'
                            ">
                            {{
                              deviceInfo[dock.sn]
                                ? EModeCode[deviceInfo[dock.sn].mode_code]
                                : EModeCode[EModeCode.未连接]
                            }}

                          </div>
                        </div>
                        <div class="mr5 flex-align-center flex-row" style="width: 85px; margin-right: 0; height: 18px">
                          <div v-if="hmsInfo[dock.sn]" class="flex-align-center flex-row">
                            <div :class="hmsInfo[dock.sn][0].level === EHmsLevel.CAUTION
                                ? 'caution-blink'
                                : hmsInfo[dock.sn][0].level === EHmsLevel.WARN
                                  ? 'warn-blink'
                                  : 'notice-blink'
                              " style="width: 18px; height: 16px; text-align: center">
                              <span :style="hmsInfo[dock.sn].length > 99
                                  ? 'font-size: 11px'
                                  : 'font-size: 12px'
                                ">{{ hmsInfo[dock.sn].length }}</span>
                              <span class="fz10">{{
                                hmsInfo[dock.sn].length > 99 ? '+' : ''
                              }}</span>
                            </div>
                            <a-popover trigger="click" placement="bottom" color="black"
                              v-model:visible="hmsVisible[dock.sn]"
                              @visibleChange="readHms(hmsVisible[dock.sn], dock.sn)"
                              :overlayStyle="{ width: '200px', height: '300px' }">
                              <div :class="hmsInfo[dock.sn][0].level === EHmsLevel.CAUTION
                                  ? 'caution'
                                  : hmsInfo[dock.sn][0].level === EHmsLevel.WARN
                                    ? 'warn'
                                    : 'notice'
                                " style="margin-left: 3px; width: 62px; height: 16px">
                                <span class="word-loop">{{ hmsInfo[dock.sn][0].message_zh }}</span>
                              </div>
                              <template #content>
                                <a-collapse style="background: black; height: 300px; overflow-y: auto" :bordered="false"
                                  expand-icon-position="right" :accordion="true">
                                  <a-collapse-panel v-for="hms in hmsInfo[dock.sn]" :key="hms.hms_id" :showArrow="false"
                                    style="
                                      margin: 0 auto 3px auto;
                                      border: 0;
                                      width: 140px;
                                      border-radius: 3px;
                                    " :class="hms.level === EHmsLevel.CAUTION
                                        ? 'caution'
                                        : hms.level === EHmsLevel.WARN
                                          ? 'warn'
                                          : 'notice'
                                      ">
                                    <template #header>
                                      <div class="flex-row flex-align-center" style="width: 130px">
                                        <div style="width: 110px">
                                          <span class="word-loop">{{ hms.message_zh }}</span>
                                        </div>
                                        <div style="
                                            width: 20px;
                                            height: 15px;
                                            font-size: 10px;
                                            z-index: 2;
                                          " class="flex-row flex-align-center flex-justify-center" :class="hms.level === EHmsLevel.CAUTION
                                              ? 'caution'
                                              : hms.level === EHmsLevel.WARN
                                                ? 'warn'
                                                : 'notice'
                                            ">
                                          <DoubleRightOutlined :rotate="isActive ? 90 : 0" />
                                        </div>
                                      </div>
                                    </template>

                                    <a-tooltip :title="hms.create_time">
                                      <div style="color: white" class="text-hidden">
                                        {{ hms.create_time }}
                                      </div>
                                    </a-tooltip>
                                  </a-collapse-panel>
                                </a-collapse>
                              </template>
                            </a-popover>
                          </div>
                          <div v-else class="width-100" style="height: 90%; background: rgba(0, 0, 0, 0.35)"></div>
                        </div>
                      </div>
                    </div>
                    <div style="background: #595959; width: 40px;height: 100%; display: flex; flex-direction: column;">
                      <div style="height: 50%;" class="flex-row flex-justify-center flex-align-center">
                        <div class="fz16" @click.stop="
                          switchVisible(
                            $event,
                            dock,
                            true,
                            dockInfo[dock.gateway.sn] &&
                            dockInfo[dock.gateway.sn].basic_osd?.mode_code !==
                            EDockModeCode.未连接
                          )
                          ">
                          <a v-if="osdVisible.gateway_sn === dock.gateway.sn && osdVisible.visible">
                            <EyeOutlined />
                          </a>
                          <a v-else>
                            <EyeInvisibleOutlined />
                          </a>
                        </div>
                      </div>
                      <!-- 驾驶舱 -->
                      <div style=" height: 50%;border-top: 1px solid #3a3a3a;cursor: pointer;"
                        class="flex-row flex-justify-center flex-align-center"
                        @click.stop="openCockpit(dock,true, hmsVisible)">
                        <a-tooltip placement="bottom">
                          <template #title>
                            <span>驾驶舱</span>
                          </template>
                          <RocketOutlined class="jscRock" />
                        </a-tooltip>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </a-collapse-panel>
          </a-collapse>
          <a-collapse :bordered="false" expandIconPosition="right" accordion
            v-model:activeKey="EDeviceTypeName.Aircraft" :class="thType ? 'airportBgD' : 'airportBgcode'">
            <a-collapse-panel :key="EDeviceTypeName.Aircraft" header="设备">
              <div v-if="onlineDevices.data.length === 0" style="height: 150px; color: white">
                <a-empty :image-style="{ height: '60px' }" />
              </div>
              <div v-else class="fz12" style="color: white">
                <div v-for="device in onlineDevices.data" :key="device.sn"
                  style="background: #3c3c3c; height: 90px; width: 250px; margin-bottom: 10px">
                  <div class="battery-slide" v-if="deviceInfo[device.sn]">
                    <div style="background: #535759; width: 100%"></div>
                    <div class="capacity-percent"
                      :style="{ width: deviceInfo[device.sn]?.battery?.capacity_percent + '%' }"></div>
                    <div class="return-home" :style="{ width: deviceInfo[device.sn]?.battery?.return_home_power + '%' }">
                    </div>
                    <div class="landing" :style="{ width: deviceInfo[device.sn]?.battery?.landing_power + '%' }"></div>
                    <div class="battery" :style="{ left: deviceInfo[device.sn]?.battery?.capacity_percent + '%' }"></div>
                  </div>
                  <div style="
                      border-bottom: 1px solid #515151;
                      border-radius: 2px;
                      height: 50px;
                      width: 100%;
                    " class="flex-row flex-justify-between flex-align-center">
                    <div style="float: left; padding: 5px 5px 8px 8px; width: 88%">
                      <div style="width: 100%; height: 100%">
                        <a-tooltip>
                          <template #title>{{
                            device.model ? `${device.model} - ${device.callsign}` : 'No Drone'
                          }}</template>
                          <span class="text-hidden" style="max-width: 200px; display: block; height: 20px">{{
                            device.model ? `${device.model} - ${device.callsign}` : 'No Drone'
                          }}</span>
                        </a-tooltip>
                      </div>
                      <div class="mt5" style="display: flex;background: #595959">
                        <span class="mr5" @click.stop="getDeviceLoc('遥控器', device.gateway.sn)"
                          style="cursor: pointer;"><a-image
                            style="margin-left: 2px; margin-top: -2px; height: 20px; width: 20px" :src="rc"
                            :preview="false" /></span>
                        <a-tooltip>
                          <template #title>{{ device.gateway.model }} - {{ device.gateway.callsign }}
                          </template>
                          <div class="text-hidden" style="max-width: 200px">
                            {{ device.gateway.model }} - {{ device.gateway.callsign }}
                          </div>
                        </a-tooltip>
                      </div>
                    </div>
                    <div style="float: right; background: #595959; height: 50px; width: 40px"
                      class="flex-row flex-justify-center flex-align-center">
                      <div class="fz16" @click.stop="
                        switchVisible(
                          $event,
                          device,
                          false,
                          deviceInfo[device.sn] &&
                          deviceInfo[device.sn].mode_code !== EModeCode.未连接
                        )
                        ">
                        <a v-if="osdVisible.sn === device.sn && osdVisible.visible">
                          <EyeOutlined />
                        </a>
                        <a v-else>
                          <EyeInvisibleOutlined />
                        </a>
                      </div>
                    </div>
                  </div>
                  <div class="flex-row flex-justify-center flex-align-center" style="height: 40px">
                    <div class="flex-row" style="height: 20px; background: #595959; width: 82%; margin: 0 5px 0 8px;">
                      <span class="ml5 mr5" @click.stop="getDeviceLoc('无人机', device.sn)" style="cursor: pointer;">
                        <RocketOutlined />
                      </span>
                      <span class="font-bold"
                        :style="deviceInfo[device.sn] && deviceInfo[device.sn].mode_code !== EModeCode.未连接 ? 'color: #00ee8b' : 'color: red;'">
                        {{ deviceInfo[device.sn] ? EModeCode[deviceInfo[device.sn].mode_code] : EModeCode[EModeCode.未连接]
                        }}
                      </span>
                    </div>
                    <!-- 驾驶舱 -->
                    <div
                      style="float: right; background: #595959; height: 100%; width: 40px; border-top: 1px solid rgb(58, 58, 58);"
                      class="flex-row flex-justify-center flex-align-center">
                      <div style="width: 100%; height: 50%;cursor: pointer;"
                        class="flex-row flex-justify-center flex-align-center"
                        @click.stop="openCockpit(device,false, hmsVisible)">
                        <a-tooltip placement="bottom">
                          <template #title>
                            <span>驾驶舱</span>
                          </template>
                          <RocketOutlined class="jscRock" />
                        </a-tooltip>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </a-collapse-panel>
          </a-collapse>
        </div>
      </div>

    </div>

  </div>
  <div v-if="jscShow" class="custom-modal">
    <cockPit @closeCockpit="closeCockpit" :center="jcWrjLonlat" :cockpit_dock="COCKPITDOCK"></cockPit>
  </div>
</template>

<script lang="ts">
import * as $Icon from '@ant-design/icons-vue';
import { createVNode, defineComponent, onMounted, reactive, computed, ref } from 'vue';
import Sidebar from '@/components/common/sidebar.vue'

// import AMapLoader from '@amap/amap-jsapi-loader';
import { EDeviceTypeName, ELocalStorageKey } from '@/types';
import { ERouterName, EBizCode } from '@/api/enum/index';
import { getDeviceTopo, getUnreadDeviceHms, updateDeviceHms } from '@/api/manage/index';
import rc from '@/assets/rc.png'
import store from '@/store'
import EventBus from '@/event-bus'
import { wgs84togcj02 } from '@/vendors/coordtransform'
import { EModeCode, EDockModeCode } from '@/types/device'
import djiinfo from '../../components/djiinfo/djiinfo.vue'
import djimap from '../../components/djimap/djimap.vue'
import { useMapTool } from '@/hooks/use-map-tool';
import { message } from 'ant-design-vue'
import cockPit from '@/components/cockpit/cockpit.vue';
import { watch } from 'vue';
import {
  RocketOutlined,
  EyeInvisibleOutlined,
  EyeOutlined,
  RobotOutlined,
  DoubleRightOutlined, ThunderboltOutlined, CloseOutlined
} from '@ant-design/icons-vue';
import { EHmsLevel } from '@/types/enums';
import { setTheme } from '@/utils/util';
import { getWorkspaceId } from '@/utils/storage'

let map = null;

const osdVisible = computed(() => {
  return store.state.dock.osdVisible
})
const hmsVisible = new Map();
const deviceState = ref(store.state.dock.deviceState);
const jcLonlat = ref([]);//机场-机场
const jcWrjLonlat = ref([]);//机场-无人机
const sbWrjLonlat = ref([]);//设备-无人机
const sbYkqLonlat = ref([]);//设备-遥控器
const deviceInfo = computed(() => store.state.dock.deviceState.deviceInfo)
const dockInfo = computed(() => store.state.dock.deviceState.dockInfo)
const gatewayInfo = computed(() => store.state.dock.deviceState.gatewayInfo)
const hmsInfo = computed({
  get: () => store.state.dock.hmsInfo,
  set: (val) => {
    return val
  }
})
const Icon = props => {
  const { icon } = props;
  return createVNode($Icon[icon]);
};
const activeKey = ref('1')
export default defineComponent({
  components: {
    Icon,
    RocketOutlined,
    EyeInvisibleOutlined,
    EyeOutlined,
    RobotOutlined,
    DoubleRightOutlined, djiinfo, Sidebar, djimap,
    cockPit
  },
  setup() {
    const onlineDevices = reactive({
      data: [],
    });
    // const root = getRoot()

    //const map ={}
    const onlineDocks = reactive({
      data: [],
    });


    function goHome() {
      // root.$router.push('/' + ERouterName.MEMBERS)
    }
    function readHms(visiable, sn) {
      if (!visiable) {
        updateDeviceHms(getWorkspaceId(), sn).then(res => {
          if (res.code === 0) {
            delete hmsInfo.value[sn]
          }
        })
      }
    }
    let useMapToolHook = useMapTool();
    function switchVisible(e, device, isDock, isClick) {
      if (!isClick) {
        e.target.style.cursor = 'not-allowed'
        return
      }
      if (device.sn === osdVisible.value.sn) {
        osdVisible.value.visible = !osdVisible.value.visible
      } else {
        osdVisible.value.sn = device.sn
        osdVisible.value.callsign = device.callsign
        osdVisible.value.model = device.model
        osdVisible.value.visible = true
        osdVisible.value.gateway_sn = device.gateway.sn
        osdVisible.value.device_name = device.gateway.model
        osdVisible.value.is_dock = isDock
        osdVisible.value.gateway_callsign = device.gateway.callsign
        osdVisible.value.payloads = device.payload
      }

      store.commit('SET_OSD_VISIBLE_INFO', osdVisible)
    }
    //机场定位
    function getAirport(type, sn) {
      if (type == '机场') {
        if (dockInfo.value[sn]) {
          if (!dockInfo.value[sn].basic_osd.longitude || dockInfo.value[sn].basic_osd.longitude == 0) {
            message.warn('定位搜索中')
          }
          let point = [dockInfo.value[sn].basic_osd.longitude, dockInfo.value[sn].basic_osd.latitude]
          setPanTo(point);
        } else {
          message.error('设备未在线')
        }
      } else {
        if (deviceInfo.value[sn]) {
          if (!deviceInfo.value[sn].longitude || deviceInfo.value[sn].longitude == 0) {
            message.warn('定位搜索中')
          }
          let point = [deviceInfo.value[sn].longitude, deviceInfo.value[sn].latitude]
          setPanTo(point);
        } else {
          message.error('无人机未在线')
        }
      }
    }
    /**
     * 公共偏移
     */
    const setPanTo = (location) => {
      if (location[0] != 0 && location[1] != 0) {
        // const transPosition:any = wgs84togcj02(location[0], location[1]);
        useMapToolHook.panTo(location);
      } else {
        message.info('定位搜索中,请稍后')
      }
    }
    //设备定位
    function getDeviceLoc(type, sn) {
      if (type == '无人机') {
        if (deviceInfo.value[sn]) {
          if (!deviceInfo.value[sn].longitude || deviceInfo.value[sn].longitude == 0) {
            message.warn('定位搜索中')
          }
          let point = [deviceInfo.value[sn].longitude, deviceInfo.value[sn].latitude]
          setPanTo(point);
        } else {
          message.error('无人机未在线')
        }
      } else {
        if (gatewayInfo.value[sn]) {
          if (!gatewayInfo.value[sn].longitude || gatewayInfo.value[sn].longitude == 0) {
            message.warn('定位搜索中')
          }
          let point = [gatewayInfo.value[sn].longitude, gatewayInfo.value[sn].latitude]
          setPanTo(point);
        } else {
          message.error('设备未在线')
        }
      }
    }
    function getUnreadHms(sn) {
      getUnreadDeviceHms(getWorkspaceId(), sn).then(res => {
        if (res.data.length !== 0) {
          hmsInfo.value[sn] = res.data;
        }
      });
      // console.info(hmsInfo.value);
    }
    function getOnlineTopo() {
      getDeviceTopo(getWorkspaceId()).then(res => {
        if (res.data.code !== 0) {
          return;
        }
        onlineDevices.data = [];
        onlineDocks.data = [];
        res.data.data.forEach((gateway) => {
          const child = gateway.children
          const device = {
            model: child?.device_name,
            callsign: child?.nickname,
            sn: child?.device_sn,
            mode: EModeCode.未连接,
            gateway: {
              model: gateway?.device_name,
              callsign: gateway?.nickname,
              sn: gateway?.device_sn,
              domain: gateway?.domain
            },
            payload: []
          }
          child?.payloads_list?.forEach((payload) => {
            device.payload.push({
              index: payload.index,
              model: payload.model,
              payload_name: payload.payload_name,
              payload_sn: payload.payload_sn,
              control_source: payload.control_source,
              payload_index: payload.payload_index
            })
          })
          if (EDeviceTypeName.Dock === gateway.domain) {
            hmsVisible.set(device.sn, false)
            hmsVisible.set(device.gateway.sn, false)
            onlineDocks.data.push(device)
            // console.log('机场列表数据' + JSON.stringify(onlineDocks))
          }

          if (gateway.status && EDeviceTypeName.Gateway === gateway.domain) {
            onlineDevices.data.push(device)
            // 更新 Vuex 中的 deviceInfo
            if (device.sn) {
              store.commit('UPDATE_DEVICE_INFO', {
                sn: device.sn,
                model: device.model,
                callsign: device.callsign
              })
            }
          }
        })
      });
    }
    function getOnlineDeviceHms() {
      const snList = Object.keys(dockInfo.value)
      if (snList.length === 0) {
        return
      }
      snList.forEach(sn => {
        getUnreadHms(sn)
      })
      const deviceSnList = Object.keys(deviceInfo.value)
      if (deviceSnList.length === 0) {
        return
      }
      deviceSnList.forEach(sn => {
        getUnreadHms(sn)
      })
    }
    const jscShow = ref(false);
    /**设备信息（包含机场和飞飞行器） */
    let COCKPITDOCK = ref({})
    /**
     * 打开驾驶舱
     * @param dock
     * @param hms
     */
    function openCockpit(dock,isDock ,hms) {
      console.log('openCockpit', dock, isDock);
      
      COCKPITDOCK.value = {
        ...dock,
        is_dock: isDock
      }

      if (jcWrjLonlat.value.length == 0 && jcLonlat.value.length !== 0) {
        jcWrjLonlat.value = jcLonlat.value;
      }
      if (deviceInfo.value[dock.sn] && deviceInfo.value[dock.sn].longitude && deviceInfo.value[dock.sn].latitude) {
        jcWrjLonlat.value = [deviceInfo.value[dock.sn].longitude, deviceInfo.value[dock.sn].latitude]
      } else if (dockInfo.value[dock.gateway.sn] && dockInfo.value[dock.gateway.sn].basic_osd.longitude && dockInfo.value[dock.gateway.sn].basic_osd.latitude) {
        jcWrjLonlat.value = [dockInfo.value[dock.gateway.sn].basic_osd.longitude, dockInfo.value[dock.gateway.sn].basic_osd.latitude]
      } else if(gatewayInfo.value[dock.gateway.sn]){
        jcWrjLonlat.value = [gatewayInfo.value[dock.gateway.sn].longitude, gatewayInfo.value[dock.gateway.sn].latitude]
      }else {
        //默认位置
        jcWrjLonlat.value = [120.23670203353453,30.301917939866275]
      }

      osdVisible.value.sn = dock.sn
      osdVisible.value.callsign = dock.callsign
      osdVisible.value.model = dock.model
      osdVisible.value.visible = true
      osdVisible.value.gateway_sn = dock.gateway.sn
      osdVisible.value.is_dock = isDock
      osdVisible.value.gateway_callsign = dock.gateway.callsign
      osdVisible.value.payloads = dock.payload
      store.commit('SET_OSD_VISIBLE_INFO', osdVisible)
      jscShow.value = true;
      store.commit("COCKPIT_SET", true)
      // }else{
      // 	message.error('无人机未在线')
      // }
    }
    const closeCockpit = () => {
      jscShow.value = false;
      store.commit("COCKPIT_SET", false)
    }
    const thType = ref(false)
    onMounted(() => {
      getOnlineTopo()
      setTimeout(() => {
        watch(() => store.state.deviceStatusEvent,
          data => {
            // 如果驾驶舱打开，则返回
            if (jscShow.value) {
              return
            }
            getOnlineTopo()
            if (data.deviceOnline.sn) {
              getUnreadHms(data.deviceOnline.sn)
            }
            // 监听设备离线事件
            if (data.deviceOffline.sn) {
              getOnlineTopo()
            }
          },
          {
            deep: true
          }
        )
        getOnlineDeviceHms()
      }, 3000)
      if (store.state.common.themeName == 'default') {
        thType.value = true;
      } else {
        thType.value = false;
        setTheme(store.state.common.themeName);
      }
      store.commit('MAP_TYPE', false);
      store.commit('LAYER_MAP_TYPE', false);
    });
    const str = '--'
    return {
      EHmsLevel,
      getOnlineDeviceHms,
      switchVisible,
      rc,
      EModeCode,
      EDockModeCode,
      EDeviceTypeName,
      onlineDevices,
      osdVisible,
      hmsVisible,
      deviceInfo,
      dockInfo,
      gatewayInfo,
      hmsInfo,
      getOnlineTopo,
      onlineDocks,
      goHome,
      getUnreadHms,
      str,
      readHms,
      getAirport,
      getDeviceLoc,
      thType,
      jscShow,
      openCockpit,
      closeCockpit,//无人机
      jcWrjLonlat,
      activeKey,
      COCKPITDOCK
    };
  },
});
</script>

<style lang="scss">
@import '@/styles/dij/index.scss';

.airportBgD {
  background-color: #000000 !important;
  color: #ffffff !important;
}

.project-app-wrapper {
  display: flex;
  transition: width 0.2s ease;
  height: 100%;
  width: 100%;

  .left {
    display: flex;
    width: 285px;
    flex: 0 0 285px;
    // background-color: #232323;

    .main-content {
      flex: 1;
      color: #fff;
      width: 285px;
    }
  }

  .right {
    flex-grow: 1;
    position: relative;

    .map-wrapper {
      width: 100%;
      height: 100%;
    }

    .media-wrapper,
    .task-wrapper {
      position: absolute;
      top: 0;
      bottom: 0;
      left: 0;
      right: 0;
      z-index: 100;
      background: #f6f8fa;
    }
  }
}

.demo-project-sidebar-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 50px;
  border-right: 1px solid #4f4f4f;
  color: #fff;
  // flex: 1;
  overflow: hidden;

  .menu-item {
    width: 100%;
    padding: 16px 0px;
    display: flex;
    flex-direction: column;
    align-items: center;
    color: #fff;
    cursor: pointer;

    &.selected {
      background-color: #101010;
      color: #fff;
    }

    &.disabled {
      pointer-events: none;
      opacity: 0.45;
    }
  }

  .filling {
    flex: 1;
  }

  .setting-icon {
    font-size: 24px;
    margin-bottom: 24px;
    color: #fff;
  }
}

.ant-tooltip-open {
  border: 0;
}

.project-tsa-wrapper> :first-child {
  height: 50px;
  line-height: 50px;
  align-items: center;
  border-bottom: 1px solid #4f4f4f;
}

.project-tsa-wrapper {
  height: 100%;

  .scrollbar {
    overflow: auto;
  }

  ::-webkit-scrollbar {
    display: none;
  }
}

.ant-collapse>.ant-collapse-item>.ant-collapse-header {
  color: inherit;
  border: 0;
  padding-left: 14px;
}

.text-hidden {
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  white-space: nowrap;
  -o-text-overflow: ellipsis;
}

.font-bold {
  font-weight: 700;
}

.battery-slide {
  width: 100%;

  .capacity-percent {
    background: #00ee8b;
  }

  .return-home {
    background: #ff9f0a;
  }

  .landing {
    background: #f5222d;
  }

  .battery {
    background: white;
    border-radius: 1px;
    width: 8px;
    height: 4px;
    margin-top: -3px;
  }
}

.battery-slide>div {
  position: relative;
  margin-top: -2px;
  min-height: 2px;
  border-radius: 2px;
  white-space: nowrap;
}

.disable {
  cursor: not-allowed;
}

.notice-blink {
  background: #313130;
  animation: blink 500ms infinite;
}

.caution-blink {
  background: orange;
  animation: blink 500ms infinite;
}

.warn-blink {
  background: red;
  animation: blink 500ms infinite;
}

.notice {
  background: #313130;
  overflow: hidden;
  cursor: pointer;
}

.caution {
  background: orange;
  cursor: pointer;
  overflow: hidden;
}

.warn {
  background: red;
  cursor: pointer;
  overflow: hidden;
}

.word-loop {
  white-space: nowrap;
  display: inline-block;
  animation: 10s loop linear infinite normal;
}

@keyframes blink {
  from {
    opacity: 1;
  }

  50% {
    opacity: 0.35;
  }

  to {
    opacity: 1;
  }
}

@keyframes loop {
  0% {
    transform: translateX(20px);
    -webkit-transform: translateX(20px);
  }

  100% {
    transform: translateX(-100%);
    -webkit-transform: translateX(-100%);
  }
}

.g-map-wrapper {
  height: 100%;
  width: 100%;

  .g-action-panel {
    position: absolute;
    top: 16px;
    right: 16px;

    .g-action-item {
      width: 28px;
      height: 28px;
      background: white;
      color: rgb(81, 81, 81);
      border-radius: 2px;
      line-height: 28px;
      text-align: center;
      margin-bottom: 2px;
    }

    .g-action-item:hover {
      border: 1px solid rgb(81, 81, 81);
      border-radius: 2px;
    }
  }

  .selection {
    border: 1px solid rgb(81, 81, 81);
    border-radius: 2px;
  }

  // antd button 光晕
  &:deep(.ant-btn) {
    &::after {
      display: none;
    }
  }
}

.osd-panel {
  position: absolute;
  margin-left: 10px;
  left: 0;
  top: 0;
  width: 480px;
  background: #000;
  color: #fff;
  border-radius: 2px;
  opacity: 0.8;
}

.osd>div:not(.dock-control-panel) {
  margin-top: 5px;
  padding-left: 5px;
}

.circle {
  border-radius: 50%;
  width: 10px;
  height: 10px;
}

.battery-slide {
  .capacity-percent {
    background: #00ee8b;
  }

  .return-home {
    background: #ff9f0a;
  }

  .landing {
    background: #f5222d;
  }

  .white-point {
    width: 4px;
    height: 4px;
    border-radius: 50%;
    background: white;
    bottom: -0.5px;
  }

  .battery {
    background: #141414;
    color: #00ee8b;
    margin-top: -10px;
    height: 20px;
    width: auto;
    border-left: 1px solid #00ee8b;
    padding: 0 5px;
  }
}

.battery-slide>div {
  position: absolute;
  min-height: 2px;
  border-radius: 2px;
}

.liveview {
  position: absolute;
  color: #fff;
  z-index: 1;
  left: 0;
  margin-left: 10px;
  top: 10px;
  text-align: center;
  width: 800px;
  height: 720px;
  background: #232323;
}

.jscRock {
  font-size: 16px;
  color: #3a3a3a;
}

.jscRock:hover {
  color: #4290f7;
}

.custom-modal {
  position: fixed;
  top: 0%;
  left: 0%;
  width: 100%;
  height: 100%;
  background: #ffffff;
  z-index: 111;
}
</style>
