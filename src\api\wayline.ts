import Qs from 'qs'
import request from '@/axios'
import {ELocalStorageKey} from "@/types"
import {WaylineType} from '@/types/wayline'
import {TaskType, TaskStatus, OutOfControlAction} from '@/types/task'

const HTTP_PREFIX = '/hztech-flight-core/wayline/api/v1'
const DRC_API_PREFIX = '/hztech-flight-core/control/api/v1'

export interface CreatePlan {
  name: string,
  file_id: string,
  dock_sn: string,
  task_type: TaskType, // 任务类型
  wayline_type: WaylineType, // 航线类型
  task_days: number[] // 执行任务的日期（秒）
  task_periods: number[][] // 执行任务的时间点（秒）
  rth_altitude: number // 相对机场返航高度 20 - 500
  out_of_control_action: OutOfControlAction // 失控动作
  min_battery_capacity?: number, // The minimum battery capacity of aircraft.
  min_storage_capacity?: number, // The minimum storage capacity of dock and aircraft.
}

export interface Task {
  job_id: string,
  job_name: string,
  task_type: TaskType, // 任务类型
  file_id: string, // 航线文件id
  file_name: string, // 航线名称
  wayline_type: WaylineType, // 航线类型
  dock_sn: string,
  dock_name: string,
  workspace_id: string,
  username: string,
  begin_time: string,
  end_time: string,
  execute_time: string,
  completed_time: string,
  status: TaskStatus, // 任务状态
  progress: number, // 执行进度
  code: number, // 错误码
  rth_altitude: number // 相对机场返航高度 20 - 500
  out_of_control_action: OutOfControlAction // 失控动作
  media_count: number // 媒体数量
  uploading: boolean // 是否正在上传媒体
  uploaded_count: number // 已上传媒体数量
}

export enum UpdateTaskStatus {
  Suspend = 0, // 暂停
  Resume = 1, // 恢复
}
const getWorkspaceId = (): string => localStorage.getItem(ELocalStorageKey.WorkspaceId) || ''

// 查询航线
export const getWaylineFiles = (wid: any, params: any) => {
  return request({
    url: `${HTTP_PREFIX}/workspaces/${wid}/waylines`,
    method: 'get',
    // 去除传参数组[],多同名属性名
    paramsSerializer: function (params) {
      return Qs.stringify(params, {arrayFormat: 'repeat'})
    },
    params: {
      ...params
    }
  })
}

// 下载航线文件
export const downloadWaylineFile = async function (workspaceId: any, waylineId: any) {
  let url = `/hztech-flight-core/media/api/v1/files/workspaces/${getWorkspaceId()}/waylines/${waylineId}/getInputStream`
  const response = await request.get(url, {
    headers: {
      'content-type': 'application/octet-stream',
    },
    responseType: 'blob'
  })
  return response.data
}

// 删除航线
export const deleteWaylineFile = async function (workspaceId: any, waylineId: any) {
  const url = `${HTTP_PREFIX}/workspaces/${getWorkspaceId()}/waylines/${waylineId}`
  const result = await request.delete(url)
  return result.data
}

// 新增航线
export const addWayline = async function (params: any) {
  const url = `${HTTP_PREFIX}/WaylineDraw/workspaces/${getWorkspaceId()}/createWayLineKmz`
  const result = await request.post(url, params)
  return result.data
}

// 航线编辑
export const rebuildWayLine = async function (params: any, id: any) {
  const url = `${HTTP_PREFIX}/WaylineDraw/workspaces/${getWorkspaceId()}/reBuildWayLine?id=${id}`
  const result = await request.post(url, params)
  return result.data
}

// 面状航线预览
export const preCreateMapping2dWayLine = async function (params: any) {
  const url = `${HTTP_PREFIX}/WaylineDraw/workspaces/${getWorkspaceId()}/preCreateMapping2dWayLine`
  const result = await request.post(url, params)
  return result.data
}

// 面状航线新增
export const createWayLineKmzMapping2 = async function (params: any) {
  const url = `${HTTP_PREFIX}/WaylineDraw/workspaces/${getWorkspaceId()}/createWayLineKmzMapping2`
  const result = await request.post(url, params)
  return result.data
}

// 面状航线编辑
export const reBuildWayLineMapping2d = async function (params: any, id: any) {
  const url = `${HTTP_PREFIX}/WaylineDraw/workspaces/${getWorkspaceId()}/reBuildWayLineMapping2d?id=${id}`
  const result = await request.post(url, params)
  return result.data
}

// 航线详情预览
export const getWayLineDetail = async function (id: any) {
  const url = `${HTTP_PREFIX}/WaylineDraw/workspaces/${getWorkspaceId()}/wayLineDetail?id=${id}`
  const result = await request.get(url)
  return result.data
}

// 航线距离时间计算接口
export const wayLineCount = async function (params: any) {
  const url = `${HTTP_PREFIX}/WaylineDraw/workspaces/${getWorkspaceId()}/wayLineCount`
  const result = await request.post(url, params)
  return result.data
}

// 航线暂停
export const flighttaskPause = async function (workspace_id: any, gateWaySn: any) {
  const url = `${DRC_API_PREFIX}/workspaces/${getWorkspaceId()}/drc/${gateWaySn}/flighttaskPause`
  const result = await request.post(url)
  return result.data
}

// 航线恢复
export const flighttaskRecovery = async function (workspace_id: any, gateWaySn: any) {
  const url = `${DRC_API_PREFIX}/workspaces/${getWorkspaceId()}/drc/${gateWaySn}/flighttaskRecovery`
  const result = await request.post(url)
  return result.data
}

// 创建航线任务
export const createPlan = async function (workspaceId: any, plan: any) {
  const url = `${HTTP_PREFIX}/workspaces/${getWorkspaceId()}/flight-tasks`
  const result = await request.post(url, plan)
  return result.data
}

// 获取航线任务
export const getWaylineJobs = async function (workspaceId: any, page: any) {
  const url = `${HTTP_PREFIX}/workspaces/${getWorkspaceId()}/jobs?page=${page.page}&page_size=${page.page_size}`
  const result = await request.get(url)
  return result.data
}

// 删除机场任务
export async function deleteTask(workspaceId: any, params: any) {
  const url = `${HTTP_PREFIX}/workspaces/${getWorkspaceId()}/jobs`
  const result = await request.delete(url, {
    params: params
  })
  return result.data
}

// 更新机场任务状态
export async function updateTaskStatus(workspaceId: any, body: any) {
  const url = `${HTTP_PREFIX}/workspaces/${getWorkspaceId()}/jobs/${body.job_id}`
  const result = await request.put(url, {
    status: body.status
  })
  return result.data
}

// 媒体立即上传
export const uploadMediaFileNow = async function (workspaceId: any, jobId: any) {
  const url = `${HTTP_PREFIX}/workspaces/${getWorkspaceId()}/jobs/${jobId}/media-highest`
  const result = await request.post(url)
  return result.data
}

// 查询航线
export const getHistoryTrajectory = (params: any) => {
  return request({
    url: `${HTTP_PREFIX}/rcdroneflightdata/list`,
    method: 'get',
    params: {
      ...params
    }
  })
}

// 断点续飞
export const breakPointJob = async (job_id: any) => {
  const url = `${HTTP_PREFIX}/workspaces/${getWorkspaceId()}/breakPointJob/${job_id}`
  const result = await request.post(url)
  return result.data
}

// 航线导入
export const importFirmareFile = async (fileData: any) => {
  const url = `${HTTP_PREFIX}/workspaces/${getWorkspaceId()}/waylines/file/upload`
  const result = await request.post(url, fileData)
  return result.data
}

//航线执行录像视频校验播放
export const getCheckFlightMp4 = (id: any) => {
  return request({
    url: `/api/hztech-mediaServer/api/cloud/record/checkFlightMp4?wayLineTaskId=${id}`,
    method: 'get',
  })
}
/**
 * 获取正在航行的路线
 * @param workspaceId
 */
export const geNowWayLineJob = (sn) => {
  return request({
    url: `${HTTP_PREFIX}/workspaces/${getWorkspaceId()}/geNowWayLineJob`,
    method: 'get',
    params:{
      sn
    }
  })
}

// 获取航线类型
export const getRouteTypeOptions = async () => {
  const url = `/hztech-system/dict/dictionary?code=route_type_options`
  const result = await request.get(url)
  return result.data
}

