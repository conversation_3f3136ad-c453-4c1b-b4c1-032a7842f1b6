<template>
  <basic-container>
    <avue-crud :option="option" :table-loading="loading" :data="data" v-model:page="page" :permission="permissionList"
      :before-open="beforeOpen" v-model="form" ref="crud" @row-update="rowUpdate" @row-save="rowSave" @row-del="rowDel"
      @search-change="searchChange" @search-reset="searchReset" @selection-change="selectionChange"
      @current-change="currentChange" @size-change="sizeChange" @refresh-change="refreshChange" @on-load="onLoad">
      <template #menu-left>
        <el-button type="danger" icon="el-icon-delete" plain v-if="permission.post_delete" @click="handleDelete">删 除
        </el-button>
      </template>
      <template #menu="{ row }">
        <el-button text type="primary" icon="el-icon-link" @click="getCreateCode(row)">获取授权码
        </el-button>
      </template>
      <template #category="{ row }">
        <el-tag>{{ row.categoryName }}</el-tag>
      </template>
      <template #ifOpen="{ row }">
        <el-tag :type="row.ifOpen === 1 ? 'success' : 'danger'" @click="toggleIfOpen(row)" style="cursor: pointer">
          {{ row.ifOpen === 1 ? '启用' : '禁用' }}
        </el-tag>
      </template>
    </avue-crud>

    <!-- 授权码弹窗 -->
    <el-dialog title="授权码" v-model="dialogVisible" width="550px" center>
      <div class="auth-link-container">
        <p class="auth-link-label">授权链接：</p>
        <p class="auth-link-content">{{ authLink }}</p>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">关 闭</el-button>
          <el-button type="primary" @click="copyAuthLink">复制链接</el-button>
        </span>
      </template>
    </el-dialog>
  </basic-container>
</template>

<script>
import { getList, getDetail, add, update, remove, createCode } from '@/api/system/authorization';
import { mapGetters } from 'vuex';
import website from '@/config/website';

export default {
  data() {
    return {
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      option: {
        height: 'auto',
        calcHeight: 32,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        index: true,
        viewBtn: true,
        selection: true,
        dialogClickModal: false,
        column: [
          {
            label: '所属租户',
            prop: 'tenantId',
            type: 'tree',
            dicUrl: '/hztech-system/tenant/select',
            addDisplay: false,
            editDisplay: false,
            viewDisplay: website.tenantMode,
            span: 24,
            props: {
              label: 'tenantName',
              value: 'tenantId',
            },
            hide: !website.tenantMode,
            rules: [
              {
                required: true,
                message: '请输入所属租户',
                trigger: 'click',
              },
            ],
          },
          {
            label: '用户Id',
            prop: 'userId',
            display: false,
          },
          {
            label: '账号',
            prop: 'account',
            display: false,
          },
          {
            label: '用户名称',
            prop: 'userName',
            type: 'input',
            display: false,
            rules: [
              {
                required: true,
                message: '请输入用户名称',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '用户',
            prop: 'user',
            hide: true,
            search: true,
            dicUrl: '/hztech-system/user/page?current=1&size=-1&deptId=',
            filterable: true,
            dicFormatter: (res) => { return res.data.records },
            props: {
              label: 'realName',
              value: 'id',
            },
            change: (val) => {
              if (val?.item) {
                this.form.tenantId = val?.item?.tenantId;
                this.form.userName = val?.item?.realName;
                this.form.userId = val?.item?.id;
              }
            },
            type: 'select',
            rules: [
              {
                required: true,
                message: '请输入用户Id',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '是否启用',
            prop: 'ifOpen',
            type: 'switch',
            search: true,
            value: 1,
            dicData: [
              {
                label: '禁用',
                value: 2,
              },
              {
                label: '启用',
                value: 1,
              },
            ],
            rules: [
              {
                required: true,
                message: '请输入是否开启',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '结束时间',
            prop: 'endAuthTime',
            type: "datetime",
            format: 'YYYY-MM-DD HH:mm:ss',
            valueFormat: 'YYYY-MM-DD HH:mm:ss',
            rules: [
              {
                required: true,
                message: '请输入结束时间',
                trigger: 'blur',
              },
            ],
          },
        ],
      },
      data: [],
      dialogVisible: false,
      authLink: '',
    };
  },
  computed: {
    ...mapGetters(['permission']),
    permissionList() {
      return {
        addBtn: this.validData(this.permission.post_add, false),
        viewBtn: this.validData(this.permission.post_view, false),
        delBtn: this.validData(this.permission.post_delete, false),
        editBtn: this.validData(this.permission.post_edit, false),
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach(ele => {
        ids.push(ele.id);
      });
      return ids.join(',');
    },
  },
  methods: {
    toggleIfOpen(row) {
      const newStatus = row.ifOpen === 1 ? 2 : 1;
      const updateData = {
        ...row,
        ifOpen: newStatus
      };

      this.$confirm(
        `确定要${newStatus === 1 ? '启用' : '禁用'}此授权吗?`,
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )
        .then(() => {
          return update(updateData);
        })
        .then(() => {
          row.ifOpen = newStatus;
          this.$message({
            type: 'success',
            message: '操作成功!'
          });
        })
        .catch(() => { });
    },
    getCreateCode(row) {
      createCode(row.id).then(res => {
        const code = res.data.data;
        const url = 'https://flight.hzdssoft.com';
        this.authLink = `${url}/auth?c=${encodeURIComponent(code)}`;
        this.dialogVisible = true;
      });
    },
    rowSave(row, done, loading) {
      add(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: 'success',
            message: '操作成功!',
          });
          done();
        },
        error => {
          window.console.log(error);
          loading();
        }
      );
    },
    rowUpdate(row, index, done, loading) {
      update(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: 'success',
            message: '操作成功!',
          });
          done();
        },
        error => {
          window.console.log(error);
          loading();
        }
      );
    },
    rowDel(row) {
      this.$confirm('确定将选择数据删除?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          return remove(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: 'success',
            message: '操作成功!',
          });
        });
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning('请选择至少一条数据');
        return;
      }
      this.$confirm('确定将选择数据删除?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          return remove(this.ids);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: 'success',
            message: '操作成功!',
          });
          this.$refs.crud.toggleSelection();
        });
    },
    beforeOpen(done, type) {
      // if (['edit', 'view'].includes(type)) {
      //   getDetail(this.form.id).then(res => {
      //     this.form = res.data.data;
      //   });
      // }
      this.form.user = this.form.userName
      done();
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      if (params.user) {
        params.userId = params.user
        delete params.user
      }
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      this.loading = true;
      getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    },
    copyAuthLink() {
      const input = document.createElement('input');
      input.value = this.authLink;
      document.body.appendChild(input);
      input.select();
      document.execCommand('copy');
      document.body.removeChild(input);
      this.$message({
        type: 'success',
        message: '链接已复制到剪贴板'
      });
      this.dialogVisible = false;
    },
  },
};
</script>

<style>
.authorization-dialog .el-message-box {
  width: 500px;
  max-width: 90%;
}

.auth-link-container {
  padding: 10px 20px;
}

.auth-link-label {
  font-weight: bold;
  margin-bottom: 10px;
  font-size: 16px;
}

.auth-link-content {
  background-color: #f5f7fa;
  padding: 15px;
  border-radius: 4px;
  word-break: break-all;
  border: 1px solid #e4e7ed;
  color: #606266;
  font-size: 14px;
}
</style>
