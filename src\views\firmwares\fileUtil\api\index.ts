import http from '../enum'
import type { FilesType, UploadFileInfoType, UploadUrls } from './typing.d.ts'
import { ELocalStorageKey } from '@/api/enum/index'
import request from '@/axios';
import { getWorkspaceId } from '@/utils/storage'

/**
 * 根据 md5 检查文件是否上传，若上传一半，则返回已上传的文件序号 listParts
 * @param md5
 * @returns
 */
export const checkFileByMd5 = (md5: string, name: string) => {
  // return http.post<UploadFileInfoType>(`/api/hztech-flight-core/${getWorkspaceId()}/firmwares/file/checkUpload`, {
  //   md5,
  //   originalFilename: name
  // })
  return request({
    url: `/hztech-flight-core/manage/api/v1/workspaces/${getWorkspaceId()}/firmwares/file/checkUpload`,
    method: 'post',
    params: {
      md5,
      originalFilename: name
    },
    data:{
      md5,
      originalFilename: name
    }
  });
}

/**
 * 根据文件信息初始化分片上传地址
 * @param data
 * @returns
 */
export const initMultPartFile = (data: UploadFileInfoType) => {
  // return http.post<UploadUrls>(`/api/hztech-flight-core/${getWorkspaceId()}/firmwares/file/multipart-init`, data)
  return request({
    url: `/hztech-flight-core/manage/api/v1/workspaces/${getWorkspaceId()}/firmwares/file/multipart-init`,
    method: 'post',
    data
  });
}

/**
 * 合并文件
 * @param md5
 * @returns
 */
export const mergeFileByMd5 = (md5: string) => {
  // return http.post<string>(`/api/hztech-flight-core/manage/api/v1/workspaces/${getWorkspaceId()}/firmwares/file/multipart/merge/${md5}`)
  return request({
    url: `/hztech-flight-core/manage/api/v1/workspaces/${getWorkspaceId()}/firmwares/file/multipart/merge/${md5}`,
    method: 'post',
  });
}

/**
 * 分片下载
 * @param filename
 * @param bytes
 * @returns
 */
export const chunkDownloadFile = (id: number, bytes: string) => {
  return http.get(
    `/files/download/${id}`,
    {},
    {
      responseType: 'blob',
      headers: {
        Range: bytes
      }
    }
  )
}

/**
 * 获取数据库文件列表
 */
export const fetchFileList = () => {
  return http.get<FilesType[]>(`files/list`)
}
