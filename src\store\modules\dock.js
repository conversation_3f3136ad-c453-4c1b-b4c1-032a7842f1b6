import { EDeviceTypeName } from '../../types'
import { getLayers } from '@/api/layer'
import { LayerType } from '@/types/mapLayer'
import { setStore, getStore } from '@/utils/store';
import { gcj02towgs84, wgs84togcj02 } from '@/vendors/coordtransform';

const layerType = {
  Normal: 0,
  Default: 1,
  Share: 2,
  Reconstruction: 3
}
const  DeviceFirmwareStatusEnum ={
  None : 1, // 无需升级
  ToUpgraded : 2, // 待升级
  ConsistencyUpgrade : 3, // 一致性升级
  DuringUpgrade : 4, // 升级中
}
// cmd ws 消息状态
const DeviceCmdExecuteStatus = {
  Sent : 'sent', // 已下发
  InProgress :'in_progress', // 执行中
  OK : 'ok', // 执行成功
  Failed : 'failed', // 失败
  Canceled : 'canceled', // 取消
  Timeout : 'timeout' // 超时
}

 const initStateFunc = () => ({
  Layers: [
    {
      name: 'default',
      id: '',
      is_distributed: true,
      elements: [],
      is_check: false,
      is_select: false,
      type: 1
    },
    {
      name: 'share',
      id: '',
      is_distributed: true,
      elements: [],
      is_check: false,
      is_select: false,
      type: 2
    }
  ],
  layerBaseInfo: {},
  drawVisible: false,
  livestreamOthersVisible: false,
  livestreamAgoraVisible: false,
  coverMap: {},
  wsEvent: {
    mapElementCreat: {},
    mapElementUpdate: {},
    mapElementDelete: {}
  },
  deviceStatusEvent: {
    deviceOnline: getStore({ name: 'device_Online' }) || {} ,
    deviceOffline: {}
  },
  markerInfo: {
    coverMap: {},
    pathMap: {}
  },
  deviceState: {
    gatewayInfo: {
      capacity_percent: '',
      transmission_signal_quality: '',
      longitude: '',
      latitude: '',
    },
    deviceInfo: {
      longitude: null,
      latitude: null,
      gear: null,
      mode_code: null,
      height: null,
      home_distance: null,
      horizontal_speed: null,
      vertical_speed: null,
      wind_speed: null,
      wind_direction: null,
      elevation: '',
      position_state: {
        gps_number: '',
        is_fixed: null,
        rtk_number: ''
      },
      battery: {
        capacity_percent: '',
        landing_power: '',
        remain_flight_time: null,
        return_home_power: '',
      },
      night_lights_state:{
        CLOSE : 0, // 0-关闭
        OPEN :1, // 1-打开
      },
      // 夜航灯开关
      height_limit: 0,// 限高设置
      distance_limit_status: {
        UNSET : 0, // 0-未设置
        SET : 1, // 1-已设置
      },
      // 限远开关
      obstacle_avoidance: {
        horizon:0,// 水平避障开关
        upside: 1,// 上行方向避障开关
        downside:2// 下行方向避障开关
      },
      // 飞行器避障开关设置
      cameras:{
        camera_mode: {
          Photo:0,
          Video:1
        },
        payload_index: '',
        photo_state: 0,
        record_time: 0,
        recording_state: 0,
        remain_photo_num: 0,
        remain_record_duration: 0,
        liveview_world_region: {
          bottom: 0,
          left: 0,
          right: 0,
          top: 0,
        }
      }
    },
    dockInfo: {},
    currentSn: '',
    currentType: -1
  },
  osdVisible: { // osd 显示设备相关信息
    sn: '',
    callsign: '',
    model: '',
    visible: false,
    gateway_sn: '',
    is_dock: false,
    payloads: null,
    gateway_callsign:''
  },
  waylineInfo: {
    id: '',
    name: '',
    drone_model_key: '',
    payload_model_keys:[],
    template_types:[0,1],
    update_time: null,
    user_name: '',
  },
  dockInfo: {
    device_name: '',
    device_sn: '',
    nickname: '',
    firmware_version: '',
    firmware_status: DeviceFirmwareStatusEnum,
    status: '',
    workspace_name: '',
    bound_time: '',
    login_time: '',
    children: [],
    domain: null,
    type: null,
    firmware_progress: '', // 升级进度
  },
  hmsInfo: {
    hms_id:'',
    tid: '',
    bid: '',
    sn: '',
    level: '',
    module: '',
    key: '',
    message_en: '',
    message_zh: '',
    create_time: '',
    update_time: '',
    domain: ''
  },
  // 机场指令执行状态信息
  devicesCmdExecuteInfo: {
    biz_code: '',
  timestamp: null,
  sn: '',
  bid: '',
  output:{
    status: DeviceCmdExecuteStatus,
    progress: {
      percent: null,
      step_key: '',
      step_result: ''
    },
    ext: {
      rate: ''
    }
  },
  result: null,
  },
  mqttState: {
    mqttConnected: false
  },
  clientId: '',
  cameraFollow: false, // 添加相机跟随状态，默认开启
})


const getters= {
}
const mutations = {
  SET_LAYER_INFO (state, info) {
    state.Layers = info
  },
  SET_DEVICE_INFO (state, info) {
    state.deviceState.deviceInfo[info.sn] = {
      ...state.deviceState.deviceInfo[info.sn],
      ...info.host
    }
    state.deviceState.currentSn = info.sn
    state.deviceState.currentType = EDeviceTypeName.Aircraft
  },
  UPDATE_DEVICE_INFO (state, info) {
    if (state.deviceState.deviceInfo[info.sn]) {
      state.deviceState.deviceInfo[info.sn] = {
        ...state.deviceState.deviceInfo[info.sn],
        ...info
      }
    } else {
      state.deviceState.deviceInfo[info.sn] = info
    }
  },
  SET_GATEWAY_INFO (state, info) {
    state.deviceState.gatewayInfo[info.sn] = info.host
    state.deviceState.currentSn = info.sn
    state.deviceState.currentType = EDeviceTypeName.Gateway
  },
  SET_DOCK_INFO (state, info) {
    // console.log("机场osd")
    // console.log(info)
    if (Object.keys(info.host).length === 0) {
      return
    }
    if (!state.deviceState.dockInfo[info.sn]) {
      state.deviceState.dockInfo[info.sn] = { }
    }
    state.deviceState.currentSn = info.sn
    state.deviceState.currentType = EDeviceTypeName.Dock
    const dock = state.deviceState.dockInfo[info.sn]
    if (info.host.mode_code !== undefined) {
      dock.basic_osd = info.host
      // if (dock.basic_osd && dock.basic_osd.longitude && dock.basic_osd.latitude) {
      //   const [lng, lat] = gcj02towgs84(dock.basic_osd.longitude, dock.basic_osd.latitude)
      //   // console.log(info.sn)
      //   // console.log(dock.basic_osd.longitude, dock.basic_osd.latitude)
      //   // console.log(lng, lat)
      //   dock.basic_osd.longitude = lng
      //   dock.basic_osd.latitude = lat
      // }
      return
    }
    if (info.host.wireless_link) {
      dock.link_osd = info.host
      return
    }
    if (info.host.job_number !== undefined) {
      dock.work_osd = info.host
    }
  },
  SET_DRAW_VISIBLE_INFO (state, bool) {
    state.drawVisible = bool
  },
  SET_LIVESTREAM_OTHERS_VISIBLE (state, bool) {
    state.livestreamOthersVisible = bool
  },
  SET_LIVESTREAM_AGORA_VISIBLE (state, bool) {
    state.livestreamAgoraVisible = bool
  },
  SET_MAP_ELEMENT_CREATE (state, info) {
    state.wsEvent.mapElementCreat = info
  },
  SET_MAP_ELEMENT_UPDATE (state, info) {
    state.wsEvent.mapElementUpdate = info
  },
  SET_MAP_ELEMENT_DELETE (state, info) {
    state.wsEvent.mapElementDelete = info
  },
  SET_DEVICE_ONLINE (state, info) {
    // console.log("赋值online-----")
    console.log(info);
    state.deviceStatusEvent.deviceOnline = info
    setStore({ name: 'device_Online', content: info });
  },
  SET_DEVICE_OFFLINE (state, info) {
    state.deviceStatusEvent.deviceOffline = info
    setStore({ name: 'device_Online', content: null });
    delete state.deviceState.gatewayInfo[info.sn]
    delete state.deviceState.deviceInfo[info.sn]
    delete state.deviceState.dockInfo[info.sn]
    delete state.hmsInfo[info.sn]
    // delete state.markerInfo.coverMap[info.sn]
    // delete state.markerInfo.pathMap[info.sn]
  },
  SET_OSD_VISIBLE_INFO (state, info) {
    state.osdVisible = info
  },
  SET_OSD_VISIBLE_ClOSE (state) {
    state.osdVisible.visible = false;
  },
  SET_SELECT_WAYLINE_INFO (state, info) {
    state.waylineInfo = info
  },
  SET_SELECT_DOCK_INFO (state, info) {
    state.dockInfo = info
  },
  SET_DEVICE_HMS_INFO (state, info) {
    const hmsList = state.hmsInfo[info.sn]
    state.hmsInfo[info.sn] = info.host.concat(hmsList ?? [])
  },
  SET_DEVICES_CMD_EXECUTE_INFO (state, info) { // 保存设备指令ws消息推送
    if (!info.sn) {
      return
    }
    if (state.devicesCmdExecuteInfo[info.sn]) {
      const index = state.devicesCmdExecuteInfo[info.sn].findIndex(cmdExecuteInfo => cmdExecuteInfo.biz_code === info.biz_code)
      if (index >= 0) {
        // 丢弃前面的消息
        if (state.devicesCmdExecuteInfo[info.sn][index].timestamp > info.timestamp) {
          return
        }
        state.devicesCmdExecuteInfo[info.sn][index] = info
      } else {
        state.devicesCmdExecuteInfo[info.sn].push(info)
      }
    } else {
      state.devicesCmdExecuteInfo[info.sn] = [info]
    }
  },
  SET_MQTT_STATE (state, mqttState) {
    state.mqttState = mqttState
  },
  SET_CLIENT_ID (state, clientId) {
    state.clientId = clientId
  },
  SET_CAMERA_FOLLOW (state, isFollow) {
    state.cameraFollow = isFollow;
  },
}

const actions = {
  async getAllElement ({ commit }) {
    const result = await getLayers({
      groupId: '',
      isDistributed: true
    })
    commit('SET_LAYER_INFO', result.data?.list)
    console.log(result)
  },
  updateElement (state, content) {
    const key = content.id.replaceAll('resource__', '')
    const type = content.type
    const layers = state.Layers
    const layer = layers.find(item => item.id === key)
    if (layer) {
      layer[type] = content.bool
    }
  },
  setLayerInfo ({ state }, layers) {
    // const layers = state.Layers
    const obj= {}
    layers.forEach(layer => {
      if (layer.type === LayerType.Default) {
        obj.default = layer.id
      } else {
        if (layer.type === LayerType.Share) {
          obj.share = layer.id
        }
      }
    })
    state.layerBaseInfo = obj
    // console.log('state.layerBaseInfo', state.layerBaseInfo)
  },
  getLayerInfo ({ state }, id) {
    return state.layerBaseInfo[id]
  }
}

 const storeOptions= {
  state: initStateFunc,
  getters,
  mutations,
  actions
}

export default storeOptions