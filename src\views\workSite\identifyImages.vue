<template>
    <div class="identify-images-container">
        <div class="header">
            <h3>识别图片</h3>
            <div class="dropdown">
                <select v-model="algoId">
                    <option v-for="item in algorithmList" :key="item.algoId" :value="item.algoId">{{ item.algoName }}
                    </option>
                </select>
                <span class="dropdown-arrow">▼</span>
            </div>
        </div>
        <div class="images-grid" ref="imagesGridRef" @scroll="handleScroll">
            <div class="image-item" v-for="(item, index) in imageList" :key="index">
                <div class="image-placeholder">
                    <div class="image-content">
                        <el-image :src="item.picUrl" :preview-src-list="allImageUrls" :initial-index="index" fit="cover"
                            preview-teleported />
                    </div>
                </div>
            </div>
            <div class="loading-more" v-if="loading">加载中...</div>
            <div class="no-more-data" v-if="noMoreData">没有更多数据</div>
        </div>
    </div>
</template>

<script setup>
import { getList2, getAlgorithmList } from '@/api/workSite/index.js';
import { ref, watch, onMounted, computed, onUnmounted } from 'vue'
import { ElImage } from 'element-plus'

const imageList = ref([]);
const current = ref(1);
const size = ref(10);
const total = ref(0);
const loading = ref(false);
const noMoreData = ref(false);
const algoId = ref('');
const algorithmList = ref([]);
const imagesGridRef = ref(null);

// 计算所有图片URL列表，用于全部预览
const allImageUrls = computed(() => {
    return imageList.value.map(item => item.picUrl);
});

const props = defineProps({
    cockpit_dock: {
        type: Object,
        default: () => ({})
    }
})
// 监听算法ID变化，刷新图片列表
watch(algoId, () => {
    refreshList();
});

// 刷新列表数据
const refreshList = async () => {
    try {
        current.value = 1;
        noMoreData.value = false;
        imageList.value = [];
        await getImageList(true);
    } catch (error) {
        console.error('刷新列表失败', error);
    }
};

// 获取图片列表
const getImageList = async (isRefresh = false) => {
    if (loading.value) return;

    loading.value = true;
    try {
        const params = {
            algoId: algoId.value,
            cid: props.cockpit_dock.sn
        };

        const response = await getList2(current.value, size.value, params);
        const { records, total: totalCount } = response.data.data;

        if (isRefresh) {
            imageList.value = records;
        } else {
            imageList.value.push(...records);
        }

        total.value = totalCount;
        noMoreData.value = imageList.value.length >= totalCount;

        // 加载成功后增加页码
        if (!isRefresh && records.length > 0) {
            current.value += 1;
        }
    } catch (error) {
        console.error('获取图片列表失败', error);
    } finally {
        loading.value = false;
    }
};

// 处理滚动事件，检测是否滚动到底部
const handleScroll = () => {
    if (!imagesGridRef.value || loading.value || noMoreData.value) return;

    const { scrollTop, scrollHeight, clientHeight } = imagesGridRef.value;
    // 当距离底部20px时触发加载
    if (scrollTop + clientHeight >= scrollHeight - 20) {
        loadMore();
    }
};

// 加载更多数据
const loadMore = () => {
    if (!loading.value && !noMoreData.value) {
        getImageList();
    }
};

// 获取算法列表
const fetchAlgorithmList = async () => {
    try {
        const res = await getAlgorithmList(1, 100, { algoType: 5 });
        algorithmList.value = res.data.data.records;
        if (algorithmList.value.length > 0) {
            algoId.value = algorithmList.value[0].algoId;
        }
    } catch (error) {
        console.error('获取算法列表失败', error);
    }
};

// 轮询定时器
let pollingTimer = null;

// 轮询更新数据
const pollingUpdate = async () => {
    try {
        const params = {
            algoId: algoId.value,
            cid: props.cockpit_dock.sn
        };

        const response = await getList2(1, size.value, params);
        const { records } = response.data.data;

        if (records && records.length > 0) {
            const existingImagesMap = new Map();
            imageList.value.forEach((item, index) => {
                existingImagesMap.set(item.id, { item, index });
            });

            const newItems = [];

            for (const record of records) {
                const existingRecord = existingImagesMap.get(record.id);

                if (!existingRecord) {
                    newItems.push(record);
                } else {
                    imageList.value[existingRecord.index] = record;
                }
            }

            if (newItems.length > 0) {
                imageList.value.unshift(...newItems);
            }
        }
    } catch (error) {
        console.error('轮询更新失败', error);
    }
};

// 启动轮询
const startPolling = () => {
    if (pollingTimer) {
        clearInterval(pollingTimer);
    }

    pollingTimer = setInterval(() => {
        pollingUpdate();
    }, 30000);
};

// 组件卸载时清除定时器
onUnmounted(() => {
    if (pollingTimer) {
        clearInterval(pollingTimer);
    }
});

// 组件挂载时获取数据
onMounted(async () => {
    await fetchAlgorithmList();
    await getImageList();

    // 启动轮询
    startPolling();
});
</script>

<style lang="scss" scoped>
.identify-images-container {
    position: absolute;
    bottom: 0;
    right: 0;
    width: 74vw;
    height: 28vh;
    background-color: #0F1622;
    color: white;
    padding: 15px;
    border-radius: 5px;

    .header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;

        h3 {
            margin: 0;
            font-size: 18px;
            color: #fff;
        }

        .dropdown {
            position: relative;

            select {
                appearance: none;
                background-color: rgba(30, 40, 60, 0.6);
                color: white;
                border: 1px solid #4a5568;
                padding: 8px 35px 8px 12px;
                border-radius: 4px;
                cursor: pointer;
                font-size: 14px;
                min-width: 160px;
                transition: all 0.3s ease;

                &:hover {
                    background-color: rgba(40, 50, 70, 0.8);
                    border-color: #718096;
                }

                &:focus {
                    outline: none;
                    border-color: #63b3ed;
                    box-shadow: 0 0 0 2px rgba(99, 179, 237, 0.3);
                }
            }

            .dropdown-arrow {
                position: absolute;
                right: 12px;
                top: 50%;
                transform: translateY(-50%);
                pointer-events: none;
                font-size: 10px;
                color: #a0aec0;
                transition: transform 0.2s ease;
            }

            &:hover .dropdown-arrow {
                color: #e2e8f0;
            }
        }
    }

    .images-grid {
        display: grid;
        grid-template-columns: repeat(5, 1fr);
        gap: 1%;
        overflow-y: auto;
        padding: 5px 2px 15px;
        max-height: calc(28vh - 70px);
        scroll-behavior: smooth;

        /* 自定义滚动条 */
        &::-webkit-scrollbar {
            width: 6px;
        }

        &::-webkit-scrollbar-track {
            background: rgba(30, 40, 60, 0.2);
            border-radius: 3px;
        }

        &::-webkit-scrollbar-thumb {
            background: rgba(100, 116, 139, 0.5);
            border-radius: 3px;

            &:hover {
                background: rgba(100, 116, 139, 0.8);
            }
        }

        .image-item {
            transition: transform 0.2s ease;

            &:hover {
                transform: translateY(-3px);
            }

            .image-placeholder {
                background-color: rgba(15, 23, 42, 0.6);
                border: 1px solid #2d3748;
                border-radius: 6px;
                padding: 6px;
                transition: all 0.3s ease;

                &:hover {
                    border-color: #4a5568;
                    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                }

                .image-content {
                    position: relative;
                    background-color: #1a202c;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    flex: 1;
                    width: 100%;
                    height: 100%;
                    border-radius: 4px;
                    overflow: hidden;

                    :deep(.el-image) {
                        width: 210px;
                        height: 140px;
                    }

                    :deep(.el-image__inner) {
                        transition: transform 0.3s ease;

                        &:hover {
                            transform: scale(1.03);
                        }
                    }
                }
            }
        }

        .loading-more,
        .no-more-data {
            text-align: center;
            padding: 10px;
            color: #a0aec0;
            font-size: 14px;
            font-style: italic;
        }
    }
}
</style>