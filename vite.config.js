import {defineConfig, loadEnv} from 'vite';
import viteSvgIcons from 'vite-plugin-svg-icons'
import PkgConfig from 'vite-plugin-package-config'
import path from 'path'

const {resolve} = require('path');
import createVitePlugins from './vite/plugins';
// https://vitejs.dev/config/
export default ({mode, command}) => {
  const env = loadEnv(mode, process.cwd());
  const {VITE_APP_BASE} = env;
  return defineConfig({
    base: VITE_APP_BASE,
    server: {
      port: 2888,
      proxy: {
        '/api': {
          target: 'https://flight.hzdssoft.com/api', //正式
          // target: 'http://192.168.3.213:18901', ///本地
          changeOrigin: true,
          rewrite: path => path.replace(/^\/api/, ''),
        },
        '/showmode': {
          target: 'http://192.168.3.198:9000/hztech-flight/', //本地
          changeOrigin: true,
          rewrite: path => path.replace(/^\/showmode/, ''),
        },
      },
    },
    resolve: {
      alias: {
        '~': resolve(__dirname, './'),
        '@': resolve(__dirname, './src'),
        components: resolve(__dirname, './src/components'),
        styles: resolve(__dirname, './src/styles'),
        utils: resolve(__dirname, './src/utils'),
      },
    },
    css: {
      preprocessorOptions: {
        scss: {
          additionalData: '@import "./src/styles/dij/variables";'
        },
      }
    },
    plugins: [
      createVitePlugins(env, command === 'build'),
      PkgConfig(),
      viteSvgIcons({
        // 指定需要缓存的图标文件夹
        iconDirs: [path.resolve(process.cwd(), 'src/assets')],
        // 指定symbolId格式
        symbolId: 'icon-[dir]-[name]',
      }),
    ],
    build: {
      sourcemap: false
    },
    assetsInclude: ['**/*.glb']
  });
};