<template>
  <div id="map-controls">
    <el-button v-if="isDrawing && startPoint.length > 0" style="width: 90px;" type="primary" @click="setTakeoffPoint">
      重设起飞点
    </el-button>
    <!-- <div v-if="aerobatModel?.routeType === 'waypoint' && isDrawing" class="keyboard-tip">
      使用WASD键移动，C键上升，Z键下降，空格键添加点位
    </div> -->
    <div class="airline-list">
      <div class="airline-item" v-if="airline?.length > 1" v-for="(item, index) in airline" :key="index"
        :class="{ active: index === activeIndex }" @click="handleAirlineClick(item, index)">
        {{ index + 1 }}
      </div>
      <div class="airline-info" v-if="airline?.length > 1">
        <div class="info-item">
          <div class="info-label">航线长度</div>
          <div class="info-value">{{ parseInt(saData[activeIndex].distance) + 'm' || '' }}</div>
        </div>
        <div class="info-item">
          <div class="info-label">预计时间</div>
          <div class="info-value">{{ formatSeconds(parseInt(saData[activeIndex].time)) || '' }}</div>
        </div>
        <div class="info-item">
          <div class="info-label">照片数</div>
          <div class="info-value">{{ parseInt(saData[activeIndex].pcount) || '' }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getRoot } from '@/root';
import EventBus from '@/event-bus';
import DrawTool from './DrawTool';

export default {
  data() {
    return {
      points: [], // 存储点位数据
      startPoint: [], // 存储起飞点数据
      isDrawing: false, // 绘制状态-true表示结束绘制 false表示开始绘制
      texts: [], // 航段中点距离标签
      overlay: null, // 起飞点图层
      polyline: null, // 存储折线数据
      overlayGroups: [], // 航点群体标识
      startPolyline: null, // 存储起飞点折线数据
      handler: null, // Cesium事件处理器
      dragHandler: null, // 拖拽事件处理器
      keyboardHandler: null, // 键盘事件处理器
      selectedEntity: null, // 当前选中的实体
      drawTool: null, // 绘制工具,
      airline: null, // 航线数据,
      activeIndex: 0, // 当前选中的航线
      endMarker: null, // 控制点位的end图标
      moveSpeed: 0.000005, // 移动速度
    };
  },
  props: {
    /**缩放级别 */
    zoom: Number,
    /**地图中心点 */
    center: Array,
    /**地图绘制状态 */
    isSet: Boolean,
    /**航线点位数据 */
    placemark: Array,
    /**起飞点数据 */
    take0ffPoint: Array,
    /**面状航线点位数据 */
    Polygon: String,
    /**航线模型数据 */
    aerobatModel: Object,
    /**3D航线数据 */
    saData: Object
  },
  watch: {
    isSet: {
      handler(val) {
        this.isDrawing = val
        console.log('this.isDrawing', this.isDrawing);

        const root = getRoot()
        let viewer = root.$viewer

        // 处理拖拽功能
        if (val) { // isSet为true，开启拖拽和点击添加功能
          // 初始化添加点位的点击事件
          console.log('this.aerobatModel', this.aerobatModel?.routeType);

          if (this.aerobatModel?.routeType === 'waypoint') {
            if (this.startPoint.length === 0) {
              // 如果没有起飞点，先添加起飞点的鼠标事件
              this.initTakeoffPointHandler();
            } else {
              this.initClickControl(viewer);
            }

            // 初始化拖拽功能
            if (!this.dragHandler && viewer) {
              this.initDragHandler();
            }
          } else if (this.aerobatModel?.routeType === 'mapping2d' || this.aerobatModel?.routeType === 'mapping3d') {
            if (this.startPoint.length === 0) {
              // 如果没有起飞点，先添加起飞点的鼠标事件
              this.initTakeoffPointHandler();
            } else {
              // 如果已有起飞点，直接初始化 DrawTool
              this.initDrawTool(viewer);
            }
          }
        } else { // isSet为false，关闭拖拽和点击添加功能
          // 销毁点击事件处理器
          this.destroyHandlers();
        }

        this.updateMapline()
      }, deep: true
    },
    placemark: {
      handler(val) {
        this.airline = val

        this.points = val[0] || []
        this.activeIndex = 0

        this.setZoom(this.points)
      }, deep: true
    },
    take0ffPoint: {
      handler(val) {
        this.startPoint = val
      }, deep: true
    },
    startPoint: {
      handler() {
        this.updateMapline()

        // 当 startPoint 被设置后，如果是面状航线模式且正在绘制状态，初始化 DrawTool
        if ((this.aerobatModel?.routeType === 'mapping2d' || this.aerobatModel?.routeType === 'mapping3d') && this.isDrawing && this.startPoint.length > 0) {
          const root = getRoot()
          let viewer = root.$viewer

          // 确保之前的 handler 已销毁
          if (this.handler) {
            this.handler.destroy();
            this.handler = null;
          }

          // 初始化 DrawTool
          if (!this.drawTool && viewer) {
            this.initDrawTool(viewer);
          }
        }
      }, deep: true
    },
    points: {
      handler() {
        this.updateMapline()
      }, deep: true
    },
    Polygon: {
      handler(newValue) {
        // 当Polygon改变时，需要根据是否处于绘制状态更新图层
        // 如果是空字符串且在绘制状态，且是面状航线模式，需要重新初始化DrawTool
        if (this.isDrawing && (this.aerobatModel?.routeType === 'mapping2d' || this.aerobatModel?.routeType === 'mapping3d')) {
          if ((!newValue || newValue.trim() === '') && this.drawTool) {
            // Polygon被清空了，需要销毁当前DrawTool并重新初始化
            this.drawTool.clearAll();
            this.drawTool = null;

            const root = getRoot();
            const viewer = root.$viewer;
            if (viewer) {
              this.initDrawTool(viewer);
            }
          }
        }

        // 更新地图图层显示
        this.updateMapline();
      }, deep: true
    }
  },
  mounted() {
    // 使用具名函数引用注册事件监听器
    EventBus.on('lineClose', this.handleLineClose);
  },
  computed: {
  },
  beforeUnmount() {
    this.clearWayLine()
    this.points = []
    this.startPoint = []
    // 使用相同的具名函数引用移除事件监听器
    EventBus.off('lineClose', this.handleLineClose)
    this.destroyHandlers();
  },
  methods: {
    sampleTerrain(lng, lat, delay = 1000) {
      const root = getRoot();
      const viewer = root.$viewer;

      // 如果viewer不存在或没有地形提供者，返回失败的Promise
      if (!viewer || !viewer.terrainProvider) {
        console.log('地形提供者不可用');
        return Promise.resolve(0);
      }

      if (this.selectedEntity) {
        return Promise.resolve(0);
      }
      const cartographic = Cesium.Cartographic.fromDegrees(lng, lat);

      // 添加延时功能
      return new Promise((resolve, reject) => {
        // 使用setTimeout添加延时
        setTimeout(() => {
          Cesium.sampleTerrainMostDetailed(viewer.terrainProvider, [cartographic])
            .then(updatedPositions => {
              resolve(updatedPositions[0].height);
            })
            .catch(error => {
              console.error('获取地形高度失败:', error);
              resolve(0); // 失败时返回默认高度0
            });
        }, delay);
      });
    },
    // 销毁所有事件处理器
    destroyHandlers() {
      if (this.handler) {
        this.handler.destroy();
        this.handler = null;
      }
      if (this.dragHandler) {
        this.dragHandler.destroy();
        this.dragHandler = null;
      }
      if (this.keyboardHandler) {
        this.keyboardHandler.destroy();
        this.keyboardHandler = null;
      }
      // 清除end图标
      this.removeEndMarker();
      // 销毁 drawTool
      if (this.drawTool) {
        this.drawTool.clearAll();
        this.drawTool = null;
      }
    },
    // 提取为具名方法
    handleLineClose() {
      this.clearWayLine()
      this.points = []
      this.startPoint = []

      if (this.drawTool) {
        this.drawTool.clearAll();
        this.drawTool = null;
      }
    },
    // 初始化拖拽处理器
    initDragHandler() {
      const root = getRoot();
      const viewer = root.$viewer;

      if (!viewer) return;

      // 如果已经存在拖拽处理器，先销毁它
      if (this.dragHandler) {
        this.dragHandler.destroy();
        this.dragHandler = null;
      }

      this.dragHandler = new Cesium.ScreenSpaceEventHandler(viewer.scene.canvas);

      // 鼠标左键按下事件
      this.dragHandler.setInputAction((movement) => {
        // 确保只在绘制模式下处理拖拽
        if (!this.isDrawing) return;

        const pickedObject = viewer.scene.pick(movement.position);

        if (Cesium.defined(pickedObject) && pickedObject.id) {
          // 检查是否点击到了航点
          const entity = pickedObject.id;
          if (entity.pointIndex !== undefined) {
            this.selectedEntity = entity;
            viewer.scene.screenSpaceCameraController.enableRotate = false;
            viewer.scene.screenSpaceCameraController.enableTranslate = false;
            viewer.scene.screenSpaceCameraController.enableZoom = false;
            console.log('Selected point index:', entity.pointIndex); // 添加调试信息
          }
        }
      }, Cesium.ScreenSpaceEventType.LEFT_DOWN);

      // 鼠标移动事件
      this.dragHandler.setInputAction((movement) => {
        // 确保只在绘制模式下处理拖拽
        if (!this.isDrawing) return;

        if (this.selectedEntity) {
          const cartesian = viewer.camera.pickEllipsoid(
            movement.endPosition,
            viewer.scene.globe.ellipsoid
          );
          if (cartesian) {
            const cartographic = Cesium.Cartographic.fromCartesian(cartesian);
            const lng = Cesium.Math.toDegrees(cartographic.longitude);
            const lat = Cesium.Math.toDegrees(cartographic.latitude);

            // 更新点位坐标
            const pointIndex = this.selectedEntity.pointIndex;
            if (pointIndex !== undefined && this.points[pointIndex]) {
              // 保留原始高度数据
              const originalHeight = this.points[pointIndex][2] || 0;
              this.points[pointIndex] = [lng, lat, originalHeight];
              // 只更新地图显示，不发送接口请求
              this.updateMapline();
            }
          }
        }
      }, Cesium.ScreenSpaceEventType.MOUSE_MOVE);

      // 鼠标左键释放事件
      this.dragHandler.setInputAction(() => {
        // 确保只在绘制模式下处理拖拽
        if (!this.isDrawing) return;

        if (this.selectedEntity) {
          // 拖拽结束后，发送一次接口请求
          const newArray = this.points.map(item => ({
            // 确保包含高度数据
            point: `${item[0]},${item[1]},${item[2] || 0}${item[3] ? ',' + item[3] : ''}`
          }));
          this.selectedEntity = null;
          this.$emit('allPoints', newArray);

          viewer.scene.screenSpaceCameraController.enableRotate = true;
          viewer.scene.screenSpaceCameraController.enableTranslate = true;
          viewer.scene.screenSpaceCameraController.enableZoom = true;
        }
      }, Cesium.ScreenSpaceEventType.LEFT_UP);
    },
    //更新航线
    handleMapClick(e) {
      console.log('handleMapClick', e);
      if (this.startPoint.length == 0) {
        this.addTakeoffPoint(e.lnglat);
      } else {
        this.addPoint(e.lnglat);
      }
    },
    pretreatment(e) {
      console.log('pretreatment', e);
      if (!e || e.length === 0) {
        // 如果传入空数组，清空Polygon数据
        this.$emit('pretreatment', '');

        // 清空points数组并通知父组件
        this.points = [];
        const newArray = [];
        this.$emit('allPoints', newArray);

        // 强制Cesium重新渲染
        const root = getRoot();
        const viewer = root.$viewer;
        if (viewer) {
          // 清除所有图层
          this.clearWayLine();
          // 强制重绘
          viewer.scene.requestRender();
        }
        return;
      }
      const Polygon = e.map(item => `${item[0]},${item[1]},0`).join('\n')
      this.$emit('pretreatment', Polygon)
    },
    handleAirlineClick(item, index) {
      this.points = item
      this.activeIndex = index
    },
    //地图航线图层更新
    updateMapline() {
      const root = getRoot()
      let viewer = root.$viewer

      //删除所有图层
      this.clearWayLine()

      //添加起飞点
      if (this.startPoint && this.startPoint.length > 0) {
        const startAltitude = this.startPoint.length > 2 ? this.startPoint[2] : 0;
        const position = Cesium.Cartesian3.fromDegrees(this.startPoint[0], this.startPoint[1], startAltitude);
        this.overlay = viewer.entities.add({
          position: position,
          billboard: {
            image: '/img/start.png', // 需要添加起飞点图标
            verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
            scale: 0.7,
            width: 38,
            disableDepthTestDistance: Number.POSITIVE_INFINITY,
            // clampToGround: true,
            // heightReference: Cesium.HeightReference.RELATIVE_TO_GROUND,
          },
          label: {
            text: '起飞点',
            font: '14px sans-serif',
            style: Cesium.LabelStyle.FILL_AND_OUTLINE,
            outlinewidth: 4,
            verticalOrigin: Cesium.VerticalOrigin.TOP,
            pixelOffset: new Cesium.Cartesian2(0, -10),
            disableDepthTestDistance: Number.POSITIVE_INFINITY,
            // clampToGround: true,
            // heightReference: Cesium.HeightReference.RELATIVE_TO_GROUND,
          }
        });
      }

      // 如果points数组为空，则不添加连接线和多边形，只保留起飞点
      if (!this.points || this.points.length === 0) {
        // 强制Cesium重新渲染
        if (viewer) {
          viewer.scene.requestRender();
        }
        return;
      }

      //添加起飞点距离第一个航线的连线
      this.createStartPolyline(viewer);

      if (this.aerobatModel?.routeType === 'waypoint') {
        //添加航线折线数据
        if (this.points.length > 1) {
          console.log(this.points);

          const positions = this.points.map(point =>
            Cesium.Cartesian3.fromDegrees(point[0], point[1], point[2] || 0)
          );

          this.polyline = viewer.entities.add({
            polyline: {
              positions: positions,
              width: 4,
              material: Cesium.Color.RED,
              clampToGround: false // 设置为false，确保折线不贴地
            }
          });
        }
        //添加航线之间距离数据
        this.createDistancesText(viewer)
        //添加航点标点
        this.createPointIcont(viewer)
      } else if (this.aerobatModel?.routeType === 'mapping2d' || this.aerobatModel?.routeType === 'mapping3d') {
        // 面状航线逻辑 - 无论drawTool是否存在都创建基于points的线条
        if (this.points.length > 0) {
          const positions = this.points.map(point =>
            Cesium.Cartesian3.fromDegrees(point[0], point[1], point[2] || 0)
          );

          this.polyline = viewer.entities.add({
            polyline: {
              positions: positions,
              width: 4,
              material: Cesium.Color.LIME,
              clampToGround: false // 设置为false，确保折线不贴地
            }
          });

          const point = this.points[0]
          const altitude = point.length > 2 ? point[2] : 0;
          const position = Cesium.Cartesian3.fromDegrees(point[0], point[1], altitude);

          // 不再使用高度参考系统，直接使用坐标中的高度值
          const entity = viewer.entities.add({
            position: position,
            billboard: {
              image: (() => {
                const canvas = document.createElement('canvas');
                canvas.width = 36;
                canvas.height = 36;
                const context = canvas.getContext('2d');
                
                context.beginPath();
                context.moveTo(18, 30);  // 底部中点
                context.lineTo(3, 6);    // 左上角
                context.lineTo(33, 6);   // 右上角
                context.closePath();
                
                context.fillStyle = '#2d8cf0';
                context.fill();
                
                context.lineWidth = 2;
                context.strokeStyle = '#ffffff';
                context.stroke();
                
                context.font = 'bold 14px sans-serif';
                context.fillStyle = '#ffffff';
                context.textAlign = 'center';
                context.textBaseline = 'middle';
                context.fillText('S', 18, 14);
                
                return canvas;
              })(),
              verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
              scale: 1.2,
              width: 36,
              height: 36,
              disableDepthTestDistance: Number.POSITIVE_INFINITY
            },
            altitude: altitude
          });

          this.overlayGroups.push(entity);
        }

        // 仅在DrawTool不存在时创建外部多边形
        if (!this.drawTool && this.Polygon && typeof this.Polygon === 'string' && this.Polygon.trim() !== '') {
          this.createOuterRing(viewer);
        }
      }
    },
    createOuterRing(viewer) {
      if (!this.Polygon || typeof this.Polygon !== 'string') return;

      // 解析多边形字符串
      const positionArray = this.Polygon.split(',0')
        .filter(line => line.trim() !== '')
        .map(line => {
          const parts = line.split(',');
          const lng = parseFloat(parts[0]);
          const lat = parseFloat(parts[1]);
          // 读取高度值，如果存在的话
          const height = parts.length > 2 ? parseFloat(parts[2]) : 0;
          return [lng, lat, height];
        });

      if (positionArray.length < 3) return; // 至少需要3个点才能构成多边形

      // 创建Cesium多边形顶点数组
      const positions = positionArray.map(point =>
        Cesium.Cartesian3.fromDegrees(point[0], point[1], point[2] || 0)
      );

      // 添加多边形实体
      const polygonEntity = viewer.entities.add({
        polygon: {
          hierarchy: new Cesium.PolygonHierarchy(positions),
          material: new Cesium.ColorMaterialProperty(
            Cesium.Color.fromCssColorString('#2d8cf0').withAlpha(0.3)
          ),
          outline: true,
          outlineColor: Cesium.Color.fromCssColorString('#2d8cf0'),
          outlinewidth: 4,
          heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
          extrudedHeight: 0, // 添加拉伸高度，默认为0
          clampToGround: true
        },
        polyline: {
          positions: positions,
          clampToGround: true,
          width: 4,
          material: new Cesium.ColorMaterialProperty(
            Cesium.Color.fromCssColorString('#2d8cf0')
          ),
          depthFailMaterial: new Cesium.ColorMaterialProperty(
            Cesium.Color.fromCssColorString('#2d8cf0')
          ),
          classificationType: Cesium.ClassificationType.BOTH,
          shadows: Cesium.ShadowMode.DISABLED,
          distanceDisplayCondition: new Cesium.DistanceDisplayCondition(0, 9999999)
        }
      });

      // 将多边形实体添加到覆盖组
      this.overlayGroups.push(polygonEntity);
    },
    //添加航点标点
    createPointIcont(viewer) {
      if (this.points.length > 0) {
        this.points.forEach((point, index) => {
          const altitude = point.length > 2 ? Number(point[2]) : 0;
          const position = Cesium.Cartesian3.fromDegrees(point[0], point[1], altitude);

          const entity = viewer.entities.add({
            position: position,
            billboard: {
              image: '/img/mid.png', // 需要添加航点图标
              verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
              scale: 0.7,
              width: 38,
              height: 60,
              disableDepthTestDistance: Number.POSITIVE_INFINITY
            },
            label: {
              text: (index + 1).toString(),
              font: '24px sans-serif',
              style: Cesium.LabelStyle.FILL_AND_OUTLINE,
              outlinewidth: 4,
              verticalOrigin: Cesium.VerticalOrigin.TOP,
              pixelOffset: new Cesium.Cartesian2(0, -10),
              disableDepthTestDistance: Number.POSITIVE_INFINITY
            },
            pointIndex: index, // 添加点位索引标识
            // 添加高度属性以便于后续操作
            altitude: altitude
          });

          this.overlayGroups.push(entity);

          // 在waypoint模式下，添加高度标签
          if (this.aerobatModel?.routeType === 'waypoint') {
            // 添加高度标签
            const heightLabel = viewer.entities.add({
              position: position,
              label: {
                text: `height: ${altitude.toFixed(1)}m`,
                font: '12px sans-serif',
                style: Cesium.LabelStyle.FILL,
                fillColor: Cesium.Color.WHITE,
                backgroundColor: Cesium.Color.BLACK,
                showBackground: true,
                backgroundPadding: new Cesium.Cartesian2(7, 5),
                verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
                horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
                pixelOffset: new Cesium.Cartesian2(0, -45),
                disableDepthTestDistance: Number.POSITIVE_INFINITY
              }
            });
            this.overlayGroups.push(heightLabel);

            // 为每个航点添加一条垂直到地面的白色线条
            if (altitude > 0) {
              // 使用封装后的地形采样方法
              this.sampleTerrain(point[0], point[1])
                .then(terrainHeight => {
                  const verticalLine = viewer.entities.add({
                    polyline: {
                      positions: [
                        // 航点位置
                        Cesium.Cartesian3.fromDegrees(point[0], point[1], altitude),
                        // 对应地面位置（使用实际地形高度）
                        Cesium.Cartesian3.fromDegrees(point[0], point[1], terrainHeight)
                      ],
                      width: 1,
                      material: Cesium.Color.WHITE,
                      clampToGround: false
                    }
                  });
                  this.overlayGroups.push(verticalLine);
                });
            }
          }
        });
      }
    },
    createDistancesText(viewer) {
      // 如果没有起飞点或航点，直接返回
      if (this.startPoint.length === 0 || this.points.length === 0) return;

      // 创建距离标签的通用方法
      const createDistanceLabel = (position, distance, horizontalOrigin = Cesium.HorizontalOrigin.CENTER, pixelOffset = new Cesium.Cartesian2(0, 0)) => {
        const entity = viewer.entities.add({
          position: position,
          label: {
            text: distance.toFixed(2) + ' m',
            font: '14px sans-serif',
            style: Cesium.LabelStyle.FILL,
            fillColor: Cesium.Color.WHITE,
            backgroundColor: Cesium.Color.BLACK,
            showBackground: true,
            backgroundPadding: new Cesium.Cartesian2(7, 5),
            verticalOrigin: Cesium.VerticalOrigin.CENTER,
            horizontalOrigin: horizontalOrigin,
            pixelOffset: pixelOffset,
            translucencyByDistance: new Cesium.NearFarScalar(1.5e2, 1.0, 8.0e6, 0.0),
            disableDepthTestDistance: Number.POSITIVE_INFINITY
          }
        });
        this.texts.push(entity);
      };

      // 处理起飞点到第一个航点的线段
      const startAlt = this.startPoint.length > 2 ? this.startPoint[2] : 0;
      const endAlt = this.points[0].length > 2 ? this.points[0][2] : 0;

      // 处理垂直线段
      const vertDist = Math.abs(endAlt - startAlt);
      if (vertDist > 0.1) {
        createDistanceLabel(
          // 放置在起飞点正上方，精确位于垂直线段中点
          Cesium.Cartesian3.fromDegrees(this.startPoint[0], this.startPoint[1], startAlt + vertDist / 2),
          vertDist,
          Cesium.HorizontalOrigin.CENTER,
        );
      }

      // 处理水平线段
      const horizDist = Cesium.Cartesian3.distance(
        Cesium.Cartesian3.fromDegrees(this.startPoint[0], this.startPoint[1], endAlt),
        Cesium.Cartesian3.fromDegrees(this.points[0][0], this.points[0][1], endAlt)
      );

      if (horizDist > 0.1) {
        const midPoint = this.getMidPoints([this.startPoint[0], this.startPoint[1]], [this.points[0][0], this.points[0][1]]);
        createDistanceLabel(
          Cesium.Cartesian3.fromDegrees(midPoint[0], midPoint[1], endAlt),
          horizDist
        );
      }

      // 处理剩余航点之间的线段
      const points = [this.startPoint, ...this.points];
      for (let i = 1; i < points.length - 1; i++) {
        const p1 = points[i];
        const p2 = points[i + 1];

        const alt1 = p1.length > 2 ? p1[2] : 0;
        const alt2 = p2.length > 2 ? p2[2] : 0;
        const midAlt = (alt1 + alt2) / 2;

        const dist = Cesium.Cartesian3.distance(
          Cesium.Cartesian3.fromDegrees(p1[0], p1[1], alt1),
          Cesium.Cartesian3.fromDegrees(p2[0], p2[1], alt2)
        );

        const midPoint = this.getMidPoints(p1, p2);
        createDistanceLabel(
          Cesium.Cartesian3.fromDegrees(midPoint[0], midPoint[1], midAlt),
          dist
        );
      }
    },
    //添加起飞点距离第一个航线的连线
    createStartPolyline(viewer) {
      if (this.startPoint.length > 0 && this.points.length > 0) {
        // 提取起飞点和第一个航点的高度值
        const startAltitude = this.startPoint.length > 2 ? this.startPoint[2] : 0;
        const endAltitude = this.points[0].length > 2 ? this.points[0][2] : 0;

        // 检查当前是否为2D模式
        const is2DMode = viewer.scene.mode === 2;

        // 根据模式创建不同的路径
        let positions;

        if (is2DMode) {
          // 2D模式：直接连接起飞点和第一个航点
          positions = [
            // 起飞点
            Cesium.Cartesian3.fromDegrees(this.startPoint[0], this.startPoint[1], startAltitude),
            // 第一个航点
            Cesium.Cartesian3.fromDegrees(this.points[0][0], this.points[0][1], endAltitude)
          ];
        } else {
          // 3D模式：使用L形路径（先垂直上升再水平移动）
          positions = [
            // 起飞点
            Cesium.Cartesian3.fromDegrees(this.startPoint[0], this.startPoint[1], startAltitude),
            // 垂直上升点（与起飞点经纬度相同，但高度为第一个航点的高度）
            Cesium.Cartesian3.fromDegrees(this.startPoint[0], this.startPoint[1], endAltitude),
            // 第一个航点
            Cesium.Cartesian3.fromDegrees(this.points[0][0], this.points[0][1], endAltitude)
          ];
        }

        this.startPolyline = viewer.entities.add({
          polyline: {
            positions: positions,
            width: 4,
            material: Cesium.Color.LIME,
            clampToGround: false, // 设置为false，确保连线不贴地,
            horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
            verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
          }
        });
      }
    },
    // 添加航点
    addPoint(position) {
      let waylinePoint = [position.lng, position.lat];

      // 添加高度信息
      if (position.alt !== undefined) {
        waylinePoint.push(position.alt);
      } else {
        waylinePoint.push(0); // 默认高度为0
      }

      // 计算距离信息
      let distance = 0;
      const currentCartesian = Cesium.Cartesian3.fromDegrees(position.lng, position.lat, waylinePoint[2] || 0);

      if (this.points.length > 0) {
        // 从前一个点计算距离
        const prevPoint = this.points[this.points.length - 1];
        const prevCartesian = Cesium.Cartesian3.fromDegrees(prevPoint[0], prevPoint[1], prevPoint[2] || 0);
        distance = Cesium.Cartesian3.distance(prevCartesian, currentCartesian);
      } else if (this.startPoint.length > 0) {
        // 从起飞点计算距离
        const startCartesian = Cesium.Cartesian3.fromDegrees(this.startPoint[0], this.startPoint[1], this.startPoint[2] || 0);
        distance = Cesium.Cartesian3.distance(startCartesian, currentCartesian);
      }

      // 将距离信息添加到点位数据中
      waylinePoint.push(distance);

      this.points.push(waylinePoint);

      // 发送点位数据给父组件
      const newArray = this.points.map(item => ({
        point: `${item[0]},${item[1]},${item[2]},${item[3]}`
      }));

      this.$emit('allPoints', newArray);
    },
    // 设置起飞点
    addTakeoffPoint(position) {
      this.sampleTerrain(position.lng, position.lat)
        .then(altitude => {
          console.log('altitude', altitude);

          // 更新起飞点的高度信息
          this.startPoint = [position.lng, position.lat, altitude || 0];
          this.$emit('takeoffPoint', this.startPoint);

          // 更新地图显示
          this.updateMapline();
        })
    },
    // 重设起飞点
    setTakeoffPoint() {
      this.startPoint = [];
      this.initTakeoffPointHandler();
    },
    clearWayLine() {
      const root = getRoot()
      let viewer = root.$viewer

      if (this.overlay) {
        viewer.entities.remove(this.overlay);
        this.overlay = null;
      }

      //删除航点标识
      this.overlayGroups.forEach(entity => {
        viewer.entities.remove(entity);
      });
      this.overlayGroups = [];

      //删除航线
      if (this.polyline) {
        viewer.entities.remove(this.polyline);
        this.polyline = null;
      }

      //删除起飞点到第一条线
      if (this.startPolyline) {
        viewer.entities.remove(this.startPolyline);
        this.startPolyline = null;
      }

      //删除距离文字
      this.texts.forEach(entity => {
        viewer.entities.remove(entity);
      });
      this.texts = [];

      // 强制Cesium重新渲染
      if (viewer) {
        viewer.scene.requestRender();
      }
    },
    // 动态设置地图缩放等级和中心点
    setZoom(val) {
      if (this.isDrawing) return;
      const root = getRoot()
      let viewer = root.$viewer

      if (val.length > 0) {
        // 计算所有点的边界
        let minLng = Infinity;
        let maxLng = -Infinity;
        let minLat = Infinity;
        let maxLat = -Infinity;

        val.forEach(([lng, lat]) => {
          minLng = Math.min(minLng, lng);
          maxLng = Math.max(maxLng, lng);
          minLat = Math.min(minLat, lat);
          maxLat = Math.max(maxLat, lat);
        });

        // 计算中心点
        const centerLng = (minLng + maxLng) / 2;
        const centerLat = (minLat + maxLat) / 2;

        // // 计算合适的距离（根据经纬度范围）
        let lngRange = maxLng - minLng;
        let latRange = maxLat - minLat;

        // 确保地图显示区域有一个最小范围
        const MIN_RANGE = 0.001; // 大约100米的经纬度范围
        lngRange = Math.max(lngRange, MIN_RANGE);
        latRange = Math.max(latRange, MIN_RANGE);

        // 计算距离，并留出边距
        const distance = Math.max(lngRange, latRange) * 111000 * 2; // 转换为米，并留出一定边距

        // 设置一个最小和最大的距离范围
        const MIN_DISTANCE = 200;
        const MAX_DISTANCE = 10000;
        const adjustedDistance = Math.min(Math.max(distance, MIN_DISTANCE), MAX_DISTANCE);

        // 设置相机位置
        viewer.camera.setView({
          destination: Cesium.Cartesian3.fromDegrees(centerLng, centerLat, adjustedDistance),
          orientation: {
            heading: Cesium.Math.toRadians(0.0), // 正东，默认北
            pitch: Cesium.Math.toRadians(-90), // 向正下方看
            roll: 0.0, // 左右
          }
        });
      }
    },
    // 计算航线两点之间中心点
    getMidPoints(pointA, pointB) {
      let lat1 = pointA[1];
      let lon1 = pointA[0];
      let lat2 = pointB[1];
      let lon2 = pointB[0];

      lat1 = lat1 * Math.PI / 180;
      lon1 = lon1 * Math.PI / 180;
      lat2 = lat2 * Math.PI / 180;
      lon2 = lon2 * Math.PI / 180;

      let midLat = (lat1 + lat2) / 2;
      let midLon = (lon1 + lon2) / 2;

      midLat = midLat * 180 / Math.PI;
      midLon = midLon * 180 / Math.PI;

      return [midLon, midLat];
    },
    // 只清理外部环形区域
    clearOuterRingOnly() {
      const root = getRoot()
      let viewer = root.$viewer

      // 查找并删除多边形实体
      const polygonEntities = this.overlayGroups.filter(entity => entity.polygon);
      polygonEntities.forEach(entity => {
        viewer.entities.remove(entity);

        // 从overlayGroups数组中移除
        const index = this.overlayGroups.indexOf(entity);
        if (index !== -1) {
          this.overlayGroups.splice(index, 1);
        }
      });
    },
    // 添加新方法，统一初始化DrawTool的逻辑
    initDrawTool(viewer) {
      // 无论如何，先清理已存在的资源
      if (this.drawTool) {
        this.drawTool.clearAll();
        this.drawTool = null;
      }

      // 销毁所有事件处理器
      this.destroyHandlers();

      // 如果是回显多边形，只清理外部环形区域
      if (this.Polygon && typeof this.Polygon === 'string' && this.Polygon.trim() !== '') {
        this.clearOuterRingOnly();
      } else {
        // 否则清理所有图层
        this.clearWayLine();
      }

      if (viewer) {
        this.drawTool = new DrawTool(viewer);

        // 检查是否有多边形数据需要回显
        if (this.Polygon && typeof this.Polygon === 'string' && this.Polygon.trim() !== '') {
          // 解析多边形字符串数据
          console.log('this.Polygon', this.Polygon);
          const positionArray = this.Polygon.split(',0')
            .filter(line => line.trim() !== '')
            .map(line => {
              const [lng, lat] = line.split(',');
              return [parseFloat(lng), parseFloat(lat)];
            });

          // 如果有足够的点位形成多边形（至少3个点），则使用现有数据激活工具
          if (positionArray.length >= 3) {
            this.drawTool.activateWithPolygon('Polygon', this.pretreatment, positionArray);
          } else {
            // 点位不足，正常激活工具
            this.drawTool.activate('Polygon', this.pretreatment);
          }
        } else {
          // 没有多边形数据，正常激活工具
          this.drawTool.activate('Polygon', this.pretreatment);
        }
      }
    },
    // 初始化键盘控制
    initKeyboardControl(viewer) {
      if (!viewer) return;

      // 先销毁现有控制器
      if (this.keyboardHandler) {
        this.keyboardHandler.destroy();
        this.keyboardHandler = null;
      }

      // 移除现有的end图标
      this.removeEndMarker();

      // 创建键盘事件处理器
      this.keyboardHandler = new Cesium.ScreenSpaceEventHandler(viewer.scene.canvas);

      // 初始化end图标位置（使用当前相机位置或起飞点附近）
      this.createEndMarker(viewer);

      // 监听键盘按键
      document.addEventListener('keydown', this.handleKeyDown);
    },

    // 处理键盘按键
    handleKeyDown(event) {
      if (!this.isDrawing || this.aerobatModel?.routeType !== 'waypoint' || !this.endMarker) return;

      const root = getRoot();
      const viewer = root.$viewer;
      if (!viewer) return;

      // 获取当前end图标位置
      const position = this.endMarker.position.getValue(Cesium.JulianDate.now());
      const cartographic = Cesium.Cartographic.fromCartesian(position);
      let lng = Cesium.Math.toDegrees(cartographic.longitude);
      let lat = Cesium.Math.toDegrees(cartographic.latitude);
      let height = cartographic.height;

      // 根据按键移动位置
      switch (event.key.toLowerCase()) {
        case 'w': // 向北移动
          lat += this.moveSpeed;
          break;
        case 's': // 向南移动
          lat -= this.moveSpeed;
          break;
        case 'a': // 向西移动
          lng -= this.moveSpeed;
          break;
        case 'd': // 向东移动
          lng += this.moveSpeed;
          break;
        case 'c': // 上升
          height += 1;
          break;
        case 'z': // 下降
          height = Math.max(0, height - 1);
          break;
        case ' ': // 空格键，添加点位
          this.addPoint({ lng, lat, alt: height });
          break;
        default:
          return;
      }

      // 更新end图标位置
      if (event.key.toLowerCase() !== ' ') {
        this.endMarker.position = Cesium.Cartesian3.fromDegrees(lng, lat, height);
      }
    },

    // 创建end图标
    createEndMarker(viewer) {
      // 确定初始位置：如果有起飞点则使用起飞点附近，否则使用相机中心点
      let position;
      if (this.startPoint.length > 0) {
        const point = this.points.length > 0 ? this.points[this.points.length - 1] : this.startPoint;
        position = Cesium.Cartesian3.fromDegrees(
          point[0],
          point[1],
          point[2] || 0
        );
      } else {
        // 使用当前相机中心点
        const center = new Cesium.Cartesian2(
          viewer.canvas.clientWidth / 2,
          viewer.canvas.clientHeight / 2
        );
        const centerPosition = viewer.camera.pickEllipsoid(
          center,
          viewer.scene.globe.ellipsoid
        );

        if (centerPosition) {
          position = centerPosition;
        } else {
          // 默认位置，如果无法获取相机中心
          position = Cesium.Cartesian3.fromDegrees(116.397, 39.908, 0);
        }
      }

      // 创建end图标实体
      this.endMarker = viewer.entities.add({
        position: position,
        billboard: {
          image: '/img/end.png', // 使用end图标
          verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
          scale: 0.7,
          width: 38,
          disableDepthTestDistance: Number.POSITIVE_INFINITY
        },
        label: {
          text: '控制点',
          font: '14px sans-serif',
          style: Cesium.LabelStyle.FILL_AND_OUTLINE,
          outlinewidth: 4,
          verticalOrigin: Cesium.VerticalOrigin.TOP,
          pixelOffset: new Cesium.Cartesian2(0, -10),
          disableDepthTestDistance: Number.POSITIVE_INFINITY
        }
      });
    },

    // 移除end图标
    removeEndMarker() {
      if (this.endMarker) {
        const root = getRoot();
        const viewer = root.$viewer;
        if (viewer) {
          viewer.entities.remove(this.endMarker);
        }
        this.endMarker = null;
      }

      // 移除键盘事件监听
      document.removeEventListener('keydown', this.handleKeyDown);
    },
    formatSeconds(seconds) {
      const hours = Math.floor(seconds / 3600);
      const minutes = Math.floor((seconds % 3600) / 60);
      const remainingSeconds = seconds % 60;

      if (hours > 0) {
        return `${hours}h ${minutes}m ${remainingSeconds}s`;
      } else {
        return `${minutes}m ${remainingSeconds}s`;
      }
    },
    // 初始化点击控制
    initClickControl(viewer) {
      if (!viewer) return;

      // 先销毁现有控制器
      if (this.handler) {
        this.handler.destroy();
        this.handler = null;
      }

      // 创建点击事件处理器
      this.handler = new Cesium.ScreenSpaceEventHandler(viewer.scene.canvas);

      // 添加点击事件监听
      this.handler.setInputAction((movement) => {
        // 确保只在绘制模式下处理点击
        if (!this.isDrawing) return;

        // 获取点击位置
        const cartesian = viewer.camera.pickEllipsoid(
          movement.position,
          viewer.scene.globe.ellipsoid
        );

        if (cartesian) {
          const cartographic = Cesium.Cartographic.fromCartesian(cartesian);
          const lng = Cesium.Math.toDegrees(cartographic.longitude);
          const lat = Cesium.Math.toDegrees(cartographic.latitude);

          // 确定高度：使用上一个点的高度或默认高度
          let height = 0;

          if (this.points.length > 0) {
            // 使用上一个点的高度
            const lastPoint = this.points[this.points.length - 1];
            height = lastPoint.length > 2 ? lastPoint[2] : 0;
          } else if (this.startPoint.length > 0) {
            height = this.aerobatModel.globalHeight
          }

          // 添加航点
          this.addPoint({ lng, lat, alt: height });
        }
      }, Cesium.ScreenSpaceEventType.LEFT_CLICK);
    },
    // 初始化起飞点添加处理器
    initTakeoffPointHandler() {
      const root = getRoot();
      const viewer = root.$viewer;

      if (!viewer) return;

      // 先移除现有控制
      this.destroyHandlers();

      // 重新添加鼠标点击事件只用于设置起飞点
      this.handler = new Cesium.ScreenSpaceEventHandler(viewer.scene.canvas);
      this.handler.setInputAction((movement) => {
        const cartesian = viewer.camera.pickEllipsoid(
          movement.position,
          viewer.scene.globe.ellipsoid
        );
        if (cartesian) {
          const cartographic = Cesium.Cartographic.fromCartesian(cartesian);
          const lng = Cesium.Math.toDegrees(cartographic.longitude);
          const lat = Cesium.Math.toDegrees(cartographic.latitude);

          // 添加起飞点
          this.addTakeoffPoint({ lng, lat });

          // 销毁添加起飞点的事件处理器
          if (this.handler) {
            this.handler.destroy();
            this.handler = null;
          }

          // 根据航线类型初始化不同的控制器
          if (this.aerobatModel?.routeType === 'waypoint') {
            this.initClickControl(viewer);
            this.initDragHandler();
          } else if (this.aerobatModel?.routeType === 'mapping2d' || this.aerobatModel?.routeType === 'mapping3d') {
            this.initDrawTool(viewer);
          }
        }
      }, Cesium.ScreenSpaceEventType.LEFT_CLICK);
    },
  }
}
</script>

<style scoped lang="scss">
#map-controls {
  position: fixed;
  top: 10px;
  left: 350px;
  z-index: 1;
}

button {
  font-size: 14px;
  cursor: pointer;
}

.keyboard-tip {
  margin-top: 10px;
  padding: 8px;
  background-color: rgba(0, 0, 0, 0.6);
  color: white;
  border-radius: 4px;
  font-size: 14px;
}

.airline-list {
  position: fixed;
  top: 50%;
  left: 350px;
  transform: translateY(-50%);
  z-index: 1;
  cursor: pointer;
}

.airline-info {
  height: 100%;
  position: absolute;
  right: -100px;
  top: 50%;
  transform: translateY(-50%);
  color: #fff;
  display: flex;
  flex-direction: column;
  padding: 5px 0;
  border-radius: 4px;
  width: 100px;
  box-sizing: border-box;
}

.info-item {
  margin-bottom: 10px;
}

.info-label {
  color: #CCC;
  text-align: center;
  font-size: 16px;
  margin-bottom: 5px;
}

.info-value {
  text-align: center;
  font-size: 18px;
  font-weight: 500;
}

.airline-item {
  width: 40px;
  height: 40px;
  background-color: #3c3c3c;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
}

.active {
  background-color: #2d8cf0;
}
</style>
