<template>
  <div class="controlContainer">
    <div style="width: 100%;display: flex;height: 100%;">
      <div class="conetntSection" :style="{ 'background-color': wrjVisible ? '#3c3c3c' : '' }">
        <div class="contentItem uavItem" v-if="wrjVisible && wrj">
          <div class="subTabSection noTab">
            <div class="left">
              <span>无人机控制</span>
              <span v-if="!flightController" v-permission>
                <a-tooltip placement="bottom" @click="enterFlightControl">
                  <template #title>
                    <span>已锁定，点击解锁</span>
                  </template>
                  <LockOutlined class="iconfont" :style="{ color: '#ff0000', 'margin-left': '3px' }" />
                </a-tooltip>
              </span>
              <span v-else v-permission>
                <a-tooltip placement="bottom" @click="exitFlightCOntrol">
                  <template #title>
                    <span>已经解锁</span>
                  </template>
                  <LockOutlined class="iconfont" :style="{ color: '#19be6b', 'margin-left': '3px' }" />
                </a-tooltip>
              </span>
              <!-- <div class="mask"></div> -->
            </div>
            <!-- <div class="right1">
              <div class="openRouter">
                <span>选择航线</span>
              </div>
            </div> -->
          </div>
          <div class="subContentSection">
            <div class="subContentItem uavControlContainer">
              <div class="uavControlLeft">
                <div class="uavControlTop" :class="{ 'uavControlTopActive': !flightController }">
                  <div class="uavControlItem">
                    <div class="uavControlButton uavItem1" style="--top: 40%;"
                      @mousedown="onMouseDown(KeyCode.ARROW_UP)" @onmouseup="onMouseUp(KeyCode.ARROW_UP)">
                      <span class="buttonText">上升←</span>
                    </div>
                    <div class="uavControlButton uavItem2" style="--top: 40%;"
                      @mousedown="onMouseDown(KeyCode.ARROW_DOWN)" @onmouseup="onMouseUp(KeyCode.ARROW_DOWN)">
                      <span class="buttonText">→下降</span>
                    </div>
                    <div class="uavControlButton uavItem3" style="--top: 40%;" @mousedown="onMouseDown(KeyCode.KEY_Q)"
                      @onmouseup="onMouseUp(KeyCode.KEY_Q)">
                      <span class="buttonText">左旋Q</span>
                    </div>
                    <div class="uavControlButton uavItem4" style="--top: 40%;" @mousedown="onMouseDown(KeyCode.KEY_E)"
                      @onmouseup="onMouseUp(KeyCode.KEY_E)">
                      <span class="buttonText">E右旋</span>
                    </div>
                  </div>
                  <span @click="handleEmergencyStop">
                    <div class="buttonInner">
                      <span class="buttonText red">停止</span>
                    </div>
                  </span>
                  <!-- <span @click="onShowTakeoffToPointPopover">
                  <div class="buttonInner" style="color: rgb(255, 255, 255);">
                    <span class="buttonText">降&nbsp;&nbsp;落</span>
                  </div>
                </span> -->
                  <div class="uavControlItem">
                    <div class="uavControlButton uavItem5" style="--top: 40%;" @mousedown="onMouseDown(KeyCode.KEY_W)"
                      @onmouseup="onMouseUp(KeyCode.KEY_W)">
                      <span class="buttonText">前进<label class="en_a">W</label></span>
                    </div>
                    <div class="uavControlButton uavItem6" style="--top: 40%;" @mousedown="onMouseDown(KeyCode.KEY_S)"
                      @onmouseup="onMouseUp(KeyCode.KEY_S)">
                      <span class="buttonText"><label class="en_a">S</label>后退</span>
                    </div>
                    <div class="uavControlButton uavItem7" style="--top: 40%;" @mousedown="onMouseDown(KeyCode.KEY_A)"
                      @onmouseup="onMouseUp(KeyCode.KEY_A)">
                      <span class="buttonText">左移A</span>
                    </div>
                    <div class="uavControlButton uavItem8" style="--top: 40%;" @mousedown="onMouseDown(KeyCode.KEY_D)"
                      @onmouseup="onMouseUp(KeyCode.KEY_D)">
                      <span class="buttonText">D右移</span>
                    </div>
                  </div>
                </div>
                <!-- 起飞 -->
                <div class="uavControlBottom" style="margin-top: 20px;">
                  <!-- 起飞 -->
                  <DroneControlPopover :visible="takeoffToPointPopoverData.visible"
                    :loading="takeoffToPointPopoverData.loading" @confirm="($event) => onTakeoffToPointConfirm(true)"
                    @cancel="($event) => onTakeoffToPointConfirm(false)">
                    <template #formContent>
                      <div class="form-content">
                        <div>
                          <span class="form-label">目标点纬度:</span>
                          <a-input-number v-model:value="takeoffToPointPopoverData.latitude" />
                        </div>
                        <div>
                          <span class="form-label">目标点经度:</span>
                          <a-input-number v-model:value="takeoffToPointPopoverData.longitude" />
                        </div>
                        <div>
                          <span class="form-label">飞行最快速度(m/s):</span>
                          <a-input-number v-model:value="takeoffToPointPopoverData.maxSpeed" :max="15" :min="1"
                            :precision="2" />
                        </div>
                        <div>
                          <!--目标点高度（椭球高），使用 WGS84 模型，飞行器到点后默认行为：悬停-->
                          <a-tooltip title="目标点高度（椭球高），使用 WGS84 模型，飞行器到点后默认行为：悬停">
                            <span class="form-label">
                              <InfoCircleOutlined />目标点高度(m):
                            </span>
                          </a-tooltip>
                          <a-input-number v-model:value="takeoffToPointPopoverData.height" />
                        </div>
                        <div>
                          <!--相对(机场)起飞点的高度（ALT），飞行器先升到特定的高度，然后再飞向目标点。-->
                          <a-tooltip title="相对(机场)起飞点的高度（ALT），飞行器先升到特定的高度，然后再飞向目标点。">
                            <span class="form-label">
                              <InfoCircleOutlined />安全起飞高度(m):
                            </span>
                          </a-tooltip>
                          <a-input-number v-model:value="takeoffToPointPopoverData.securityTakeoffHeight" />
                        </div>
                        <div>
                          <!--相对(机场)起飞点的高度，相对高 ALT-->
                          <a-tooltip title="相对(机场)起飞点的高度，相对高 ALT">
                            <span class="form-label">
                              <InfoCircleOutlined />返回机场高度(m):
                            </span>
                          </a-tooltip>
                          <a-input-number v-model:value="takeoffToPointPopoverData.rthAltitude" />
                        </div>
                        <div>
                          <span class="form-label">远程控制失联动作:</span>
                          <a-select v-model:value="takeoffToPointPopoverData.rcLostAction" style="width: 120px"
                            :options="LostControlActionInCommandFLightOptions"></a-select>
                        </div>
                        <!-- <div>
                          <span class="form-label">航线失联动作:</span>
                          <a-select v-model:value="takeoffToPointPopoverData.exitWaylineWhenRcLost" style="width: 120px"
                            :options="WaylineLostControlActionInCommandFlightOptions"></a-select>
                        </div> -->
                        <div>
                          <span class="form-label">返回机场模式:</span>
                          <a-select v-model:value="takeoffToPointPopoverData.rthMode" style="width: 120px"
                            :disabled="true" :options="RthModeInCommandFlightOptions"></a-select>
                        </div>
                        <div>
                          <span class="form-label">指点飞行失控动作:</span>
                          <a-select v-model:value="takeoffToPointPopoverData.commanderModeLostAction"
                            style="width: 120px" :options="CommanderModeLostActionInCommandFlightOptions"></a-select>
                        </div>
                        <div>
                          <span class="form-label">指点飞行模式:</span>
                          <a-select v-model:value="takeoffToPointPopoverData.commanderFlightMode" style="width: 120px"
                            :disabled="true" :options="CommanderFlightModeInCommandFlightOptions"></a-select>
                        </div>
                        <div>
                          <!--相对(机场)起飞点的高度，相对高 ALT-->
                          <a-tooltip title="相对(机场)起飞点的高度，相对高 ALT">
                            <span class="form-label">
                              <InfoCircleOutlined />指点飞行高度(m):
                            </span>
                          </a-tooltip>
                          <a-input-number v-model:value="takeoffToPointPopoverData.commanderFlightHeight" />
                        </div>
                      </div>
                    </template>
                    <span @click="onShowTakeoffToPointPopover" v-permission>
                      <div class="buttonInner" style="color: rgb(255, 255, 255);">
                        <span class="buttonText">起飞</span>
                      </div>
                    </span>
                  </DroneControlPopover>
                  <!-- 飞向目标点 -->
                  <DroneControlPopover :visible="flyToPointPopoverData.visible" :loading="flyToPointPopoverData.loading"
                    @confirm="($event) => onFlyToConfirm(true)" @cancel="($event) => onFlyToConfirm(false)">
                    <template #formContent>
                      <div class="form-content">
                        <div>
                          <span class="form-label">目标点纬度:</span>
                          <a-input-number v-model:value="flyToPointPopoverData.latitude" />
                        </div>
                        <div>
                          <span class="form-label">目标点经度:</span>
                          <a-input-number v-model:value="flyToPointPopoverData.longitude" />
                        </div>
                        <div>
                          <!--目标点高度（椭球高），使用 WGS84 模型-->
                          <span class="form-label">目标点高度(m):</span>
                          <a-input-number v-model:value="flyToPointPopoverData.height" />
                        </div>
                      </div>
                    </template>
                    <span @click="onShowFlyToPopover" v-permission>
                      <div class="buttonInner" style="color: rgb(255, 255, 255);">
                        <span class="buttonText">飞向目标点</span>
                      </div>
                    </span>
                  </DroneControlPopover>
                  <span @click="onStopFlyToPoint" v-permission>
                    <div class="buttonInner" style="color: rgb(255, 255, 255);">
                      <span class="buttonText">停止飞向目标点</span>
                    </div>
                  </span>
                </div>
                <div class="uavControlBottom">
                  <span @click="flighttask('暂停')" v-permission>
                    <div class="buttonInner" style="color: rgb(255, 255, 255);">
                      <span class="buttonText">暂停航线</span>
                    </div>
                  </span>
                  <span @click="flighttask('恢复')" v-permission>
                    <div class="buttonInner" style="color: rgb(255, 255, 255);">
                      <span class="buttonText">恢复航线</span>
                    </div>
                  </span>
                  <span @click="sendControlCmd(cmdList[0], 0)" v-permission>
                    <div class="buttonInner" style="color: rgb(255, 255, 255);">
                      <span class="buttonText">返回机场</span>
                    </div>
                  </span>
                  <span @click="sendControlCmd(cmdList[1], 1)" v-permission>
                    <div class="buttonInner" style="color: rgb(255, 255, 255);">
                      <span class="buttonText">取消返回机场</span>
                    </div>
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="contentItem" v-if="wrjVisible && xj">
          <div class="subTabSection noTab">
            <div class="left">
              <span>相机控制</span>
            </div>
          </div>
          <div class="subContentSection">
            <div class="subContentItem uavControlContainer">
              <div class="uavControlLeft" style="width: 100%;">
                <div class="uavControlBottom" style="display: flow;">
                  <div class="row">
                    <a-select ref="select" v-model:value="payloadSelectInfo.value" style="width: 100%"
                      :options="payloadSelectInfo.options" @change="handlePayloadChange">
                    </a-select>
                  </div>
                  <div class="row">
                    <div class="drone-control">
                      <a-button type="primary" @click="onAuthPayload" v-permission>负载控制</a-button>
                    </div>
                    <template v-if="!videoStorageDisabled.isDisabled">
                      <a-button type="primary" size="small" @click="onStartCameraRecording">
                        <span>开始录像</span>
                      </a-button>
                      <a-button type="primary" size="small" @click="onStopCameraRecording">
                        <span>结束录像</span>
                      </a-button>
                    </template>
                    <a-button type="primary" size="small" @click="onTakeCameraPhoto"
                      v-if="!photoStorageDisabled.isDisabled">
                      <span>拍照({{ photoCount }})</span>
                    </a-button>
                    <div v-if="thermometricPopoverData.thermometricType == '1'">测温点：{{ temperature }}度</div>
                  </div>
                  <!-- 云台重置 -->
                  <div class="row">
                    <commonTips :title="tipsDisabled.title" content="云台重置"></commonTips>
                    <a-radio-group v-model:value="gimbalResetPopoverData.resetMode" :disabled="tipsDisabled.isDisabled"
                      @change="onGimbalResetConfirm(true)">
                      <a-radio v-for="item in GimbalResetModeOptions" :value="item.value" :key="item.value">{{
                        item.label }}</a-radio>
                    </a-radio-group>
                  </div>
                  <!-- 切换相机模式 -->
                  <div class="row">
                    <commonTips :title="tipsDisabled.title" content="切换相机模式"></commonTips>
                    <a-radio-group v-model:value="switchCameraModePopoverData.cameraMode"
                      :disabled="tipsDisabled.isDisabled" @change="onSwitchCameraModeConfirm(true)">
                      <a-radio v-for="item in CameraModeOptions" :value="item.value" :key="item.value">{{
                        item.label }}</a-radio>
                    </a-radio-group>
                  </div>
                  <!-- 相机曝光模式 -->
                  <!-- <div class="row">
                    <commonTips :title="tipsTitleCamera.title" content="相机曝光模式"></commonTips>
                    <a-radio-group v-model:value="cameraExposurePopoverData.cameraMode"
                      :disabled="tipsTitleCamera.isDisabled" @change="onCameraExposureConfirm(true)">
                      <a-radio v-for="item in CameraExposureModeOptions" :value="item.value" :key="item.value">{{
                        item.label }}</a-radio>
                    </a-radio-group>
                  </div> -->
                  <!-- 曝光值调节 -->
                  <!-- <div class="row">
                    <commonTips :title="tipsTitleCamera.title" content="曝光值调节"></commonTips>
                    <a-select :disabled="tipsTitleCamera.isDisabled"
                      v-model:value="cameraExposureValuePopoverData.cameraValue"
                      @change="onCameraExposureValueConfirm(true)" style="width: 120px"
                      :options="CameraExposureValueOptions"></a-select>
                  </div> -->
                  <!-- 相机对焦模式 -->
                  <!-- <div class="row">
                    <commonTips :title="tipsTitleCamera.title" content="相机对焦模式"></commonTips>
                    <a-radio-group v-model:value="cameraFocusPopoverData.cameraMode"
                      :disabled="tipsTitleCamera.isDisabled" @change="onCameraFocusConfirm(true)">
                      <a-radio v-for="item in CameraFocusModeOptions" :value="item.value" :key="item.value">
                        {{ item.label }}
                      </a-radio>
                    </a-radio-group>
                  </div> -->
                  <!-- 对焦值调节 -->
                  <!-- <div class="row">
                    <commonTips :title="tipsDisabled.title" content="对焦值调节"></commonTips>
                    <a-input-number v-model:value="cameraFocusValuePopoverData.cameraValue"
                      :disabled="tipsDisabled.isDisabled" :min="0" :max="50"
                      @change="onCameraFocusValueConfirm(true)" />
                  </div> -->
                  <!-- 测温模式 -->
                  <div class="row">
                    <commonTips :title="temperatureDisabled.title" content="测温模式"></commonTips>
                    <a-radio-group v-model:value="thermometricPopoverData.thermometricType"
                      :disabled="temperatureDisabled.isDisabled" @change="onThermometricConfirm(true)">
                      <a-radio v-for="item in ThermometricTypesOptions" :value="item.value" :key="item.value">
                        {{ item.label }}
                      </a-radio>
                    </a-radio-group>
                  </div>
                  <!-- 照片存储 -->
                  <div class="row">
                    <commonTips :title="photoStorageDisabled.title" content="照片存储"></commonTips>
                    <a-checkbox-group v-model:value="photoStoragePopoverData.photoStorage"
                      :disabled="photoStorageDisabled.isDisabled" @change="onPhotoStorageConfirm">
                      <a-checkbox v-for="item in photoStorage" :value="item.value" :key="item.value"
                        style="color: #FFF;">
                        {{ item.label }}
                      </a-checkbox>
                    </a-checkbox-group>
                  </div>
                  <!-- 视频存储 -->
                  <div class="row">
                    <commonTips :title="videoStorageDisabled.title" content="视频存储"></commonTips>
                    <a-checkbox-group v-model:value="videoStoragePopoverData.videoStorage"
                      :disabled="videoStorageDisabled.isDisabled" @change="onVideoStorageConfirm">
                      <a-checkbox v-for="item in photoStorage" :value="item.value" :key="item.value"
                        style="color: #FFF;">
                        {{ item.label }}
                      </a-checkbox>
                    </a-checkbox-group>
                  </div>
                  <!-- 分屏 -->
                  <div class="row">
                    <commonTips :title="splitScreenDisabled.title" content="分屏"></commonTips>
                    <a-radio-group v-model:value="splitScreenPopoverData.splitScreen"
                      :disabled="splitScreenDisabled.isDisabled" @change="onSplitscreenConfirm(true)">
                      <a-radio v-for="item in splitScreenOptions" :value="item.value" :key="item.value">
                        {{ item.label }}
                      </a-radio>
                    </a-radio-group>
                  </div>
                  <!-- 联动变焦-->
                  <div class="row">
                    <commonTips :title="linkageZoomDisabled.title" content="联动变焦"></commonTips>
                    <a-radio-group v-model:value="linkageZoomPopoverData.linkageZoom"
                      :disabled="linkageZoomDisabled.isDisabled" @change="onLinkagezoomConfirm(true)">
                      <a-radio v-for="item in splitScreenOptions" :value="item.value" :key="item.value">
                        {{ item.label }}
                      </a-radio>
                    </a-radio-group>
                  </div>
                  <DroneControlPopover :visible="thermometricAreaPopoverData.visible"
                    :loading="thermometricAreaPopoverData.loading"
                    @confirm="($event) => onThermometricAreaConfirm(true)"
                    @cancel="($event) => onThermometricAreaConfirm(false)">
                    <template #formContent>
                      <div class="form-content">
                        <div>
                          <span class="form-label">x:</span>
                          <el-input-number v-model="thermometricAreaPopoverData.x" :min="0" :max="1" :precision="2" />
                        </div>
                        <div>
                          <span class="form-label">y:</span>
                          <el-input-number v-model="thermometricAreaPopoverData.y" :min="0" :max="1" :precision="2" />
                        </div>
                        <div>
                          <span class="form-label">宽度:</span>
                          <el-input-number v-model="thermometricAreaPopoverData.width" :min="0" :max="1"
                            :precision="2" />
                        </div>
                        <div>
                          <span class="form-label">高度:</span>
                          <el-input-number v-model="thermometricAreaPopoverData.height" :min="0" :max="1"
                            :precision="2" />
                        </div>
                      </div>
                    </template>
                    <a-button type="primary" size="small" @click="($event) => onShowThermometricAreaPopover()"
                      v-if="thermometricPopoverData.thermometricType == '2'">
                      <span class="word">测温区域平均温度({{ areaTemperature }})</span>
                    </a-button>
                  </DroneControlPopover>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="contentItem" v-if="wrjVisible && hhq">
          <div class="shoutListConItem">
            <div class="shoutTopbar">
              <div style="width: 2em;"></div>
              <div class="voiceValue">
                <SoundOutlined />
                <div style="width: 100%;margin-bottom: 3px;">
                  <a-slider id="test" v-model:value="sliderValue" />
                </div>
              </div>
              <div title="循环播放" class="repeatPlay"></div>
            </div>
            <div class="shoutInputText">
              <a-textarea autocomplete="off" v-model:value="textareaValue" placeholder="请输入喊话内容"
                class="el-textarea__inner" />
            </div>
            <div class="buttonList">
              <div class="buttonItem">发送</div>
            </div>
          </div>
        </div>
        <div class="contentItem" v-if="wrjVisible && sf">
          <div class="algoList">
            <div class="algoSection">
              <div class="algoSectionTitle">已激活算法</div>
              <div class="algoItem" v-for="item in activatedAlgoList" :key="item.algoId">
                <div class="algoItemIcon" @click="onAlgoItemClick(item.algoId, false)">-</div>
                <div class="algoItemContent">
                  <div class="algoItemName">{{ item.algoName || item.name }}</div>
                </div>
              </div>
            </div>
            <div class="algoSection">
              <div class="algoSectionTitle">可激活算法</div>
              <div class="algoItem" v-for="item in availableAlgoList" :key="item.algoId">
                <div class="algoItemIcon" @click="onAlgoItemClick(item.algoId, true)">+</div>
                <div class="algoItemContent">
                  <div class="algoItemName">{{ item.algoName || item.name }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="tabSection">
        <div class="tabItem" :class="wrj ? 'active' : ''" @click="toggleActive(1)">
          <ClusterOutlined class="toIcon" />
        </div>
        <div class="tabItem" :class="xj ? 'active' : ''" @click="toggleActive(2)">
          <CameraOutlined class="toIcon" />
        </div>
        <div class="tabItem" :class="sf ? 'active' : ''" @click="toggleActive(4)">
          <!-- <VideoCameraAddOutlined class="toIcon" /> -->
          <img class="toIconImg" src="@/assets/ai.png" />
        </div>
        <!-- <div class="tabItem" :class="hhq?'active':''"  @click="toggleActive(3)">
          <NotificationOutlined class="toIcon"/>
        </div> -->
        <!-- <div class="changeItem " @click="toggleShs">
          <MenuUnfoldOutlined :style="{ fontSize: '16px' }" v-if="shs" />
          <MenuFoldOutlined :style="{ fontSize: '16px' }" v-else />
        </div> -->
      </div>
    </div>

  </div>
</template>

<script setup lang="ts">
import {
  LockOutlined, ClusterOutlined, CameraOutlined, NotificationOutlined, MenuUnfoldOutlined,
  MenuFoldOutlined, SoundOutlined, InfoCircleOutlined, VideoCameraAddOutlined
} from '@ant-design/icons-vue';
import { defineProps, reactive, ref, watch, computed, onMounted, onBeforeUnmount } from 'vue';
import { message } from 'ant-design-vue';
import { postDrcEnter, postDrcExit } from '@/api/drc';
import { ControlSource, DeviceOsdCamera } from '@/types/device';
import { useMqtt, DeviceTopicInfo } from '@/components/g-map/use-mqtt';
import { useDroneControlWsEvent } from '@/components/g-map/use-drone-control-ws-event';
import { useManualControl, KeyCode } from '@/components/g-map/use-manual-control_new';
import store from '@/store';
import {
  postFlightAuth, LostControlActionInCommandFLight, WaylineLostControlActionInCommandFlight,
  ERthMode, ECommanderModeLostAction, ECommanderFlightMode
} from '@/api/drone-control/drone';
import { noDebugCmdList as baseCmdList, DeviceCmdItem } from '@/types/device-cmd';
import { useDockControl } from '@/components/g-map/use-dock-control';
import { usePayloadControl } from '@/components/g-map/use-payload-control';
import {
  GimbalResetMode,
  GimbalResetModeOptions,
  LostControlActionInCommandFLightOptions,
  WaylineLostControlActionInCommandFlightOptions,
  RthModeInCommandFlightOptions,
  CommanderModeLostActionInCommandFlightOptions,
  CommanderFlightModeInCommandFlightOptions
} from '@/types/drone-control';
import DroneControlPopover from '@/components/g-map/DroneControlPopover.vue';
import {
  CameraMode, CameraType, CameraModeOptions, splitScreenOptions, CameraExposureModeOptions,
  photoStorage, CameraExposureValueOptions, CameraFocusModeOptions, ThermometricTypesOptions
} from '@/types/live-stream';
import { ELocalStorageKey } from '@/api/enum';
import { flighttaskPause, flighttaskRecovery } from '@/api/wayline';
import EventBus from '@/event-bus';
import { useDroneControl } from '@/components/g-map/use-drone-control';
const { flyToPoint, stopFlyToPoint, takeoffToPoint } = useDroneControl();
import { getRoot } from '@/root';
import { gcj02towgs84 } from '@/vendors/coordtransform';
import commonTips from '@/components/common/common-tips.vue';
import { getWorkspaceId } from '@/utils/storage'
import { getPlayAlgoList, getAlgorithmList, activateAlgo, deactivateAlgo } from '@/api/workSite/index.js';

const clientId = computed(() => {
  return store.state.dock.clientId;
});
const osdVisible = computed(() => {
  return store.state.dock.osdVisible;
});

/**提示文案 */
const tipsDisabled = computed(() => {
  let isDisabled = payloadControlSource.value !== 'A';
  let title = isDisabled ? '请先控制负载' : ''
  return { isDisabled, title }
})
/**镜头 */
let lens = ref('')
/** 根据镜头的提示文案 */
const tipsTitleCamera = computed(() => {
  const isDisabled = !(payloadControlSource.value == 'A' && ['wide', 'zoom'].includes(lens.value))
  let title = isDisabled ? '请先控制负载,且需要选择镜头为广角或变焦' : ''
  return { isDisabled, title }
})
/** 根据文图的提示文案 */
const temperatureDisabled = computed(() => {
  const isDisabled = !(payloadControlSource.value == 'A' && ['ir'].includes(lens.value))
  let title = isDisabled ? '请先控制负载,且需要选择镜头为红外' : ''
  return { isDisabled, title }
})
/** 照片存储提示文案 */
const photoStorageDisabled = computed(() => {
  const isDisabled = !(payloadControlSource.value === 'A' && switchCameraModePopoverData.cameraMode !== null && switchCameraModePopoverData.cameraMode !== undefined && [0, 2, 3].includes(switchCameraModePopoverData.cameraMode))
  let title = isDisabled ? '请先控制负载，且需要选择相机模式为拍照或者智能低光' : ''
  return { isDisabled, title }
})
/** 视频存储提示文案 */
const videoStorageDisabled = computed(() => {
  const isDisabled = !(payloadControlSource.value == 'A' && switchCameraModePopoverData.cameraMode === 1)
  let title = isDisabled ? '请先控制负载' : ''
  return { isDisabled, title }
})
/** 分屏提示文案 */
const splitScreenDisabled = computed(() => {
  const isDisabled = !(payloadControlSource.value == 'A' && ['ir'].includes(lens.value))
  let title = isDisabled ? '请先控制负载,且需要选择镜头为红外' : ''
  return { isDisabled, title }
})
/** 联动变焦提示文案 */
const linkageZoomDisabled = computed(() => {
  const isDisabled = !(payloadControlSource.value == 'A')
  let title = isDisabled ? '红外联动变焦' : ''
  return { isDisabled, title }
})
const props = defineProps({
  deviceInfoAttrs: Object,
  cockpit_dock: Object
})
const deviceTopicInfo: DeviceTopicInfo = reactive({
  sn: osdVisible.value.gateway_sn,
  pubTopic: '',
  subTopic: ''
});
const root = getRoot();
let markerPoint = reactive({
  flyToPoint: null,
  takeOffPoint: null,
});

const isClickProcessing = ref(true);
let clickTimer: NodeJS.Timeout | null = null;

const handleClick = () => {
  // 清除之前的定时器
  if (clickTimer) {
    clearTimeout(clickTimer);
  }

  // 设置新的定时器
  clickTimer = setTimeout(() => {
    isClickProcessing.value = true;
  }, 3000);

  // 设置处理状态
  isClickProcessing.value = false;
};

onMounted(() => {
  photoCount.value = 0;
  // 地图点击显示操作上的经纬度
  setTimeout(() => {
    let viewer = root.$cockpitMap;
    // Cesium点击事件实现
    const handler = new Cesium.ScreenSpaceEventHandler(viewer.scene.canvas);
    handler.setInputAction((movement) => {
      // 通过点击位置获取地球上的点
      const ray = viewer.camera.getPickRay(movement.position);
      const cartesian = viewer.scene.globe.pick(ray, viewer.scene);

      // 如果未找到位置，则返回
      if (!Cesium.defined(cartesian)) {
        return;
      }

      // 将笛卡尔坐标转换为经纬度
      const cartographic = Cesium.Cartographic.fromCartesian(cartesian);
      const lng = Cesium.Math.toDegrees(cartographic.longitude);
      const lat = Cesium.Math.toDegrees(cartographic.latitude);

      // 坐标转换 - 如果需要的话
      // const coordinate = gcj02towgs84(lng, lat);

      /*目标点*/
      if (flyToPointPopoverData.visible) {
        setMarkerPoint('flyToPoint', [lng, lat]);
        flyToPointPopoverData.latitude = lat;
        flyToPointPopoverData.longitude = lng;
      }
      /*起飞*/
      if (takeoffToPointPopoverData.visible) {
        setMarkerPoint('takeOffPoint', [lng, lat]);
        takeoffToPointPopoverData.latitude = lat;
        takeoffToPointPopoverData.longitude = lng;
      }
    }, Cesium.ScreenSpaceEventType.LEFT_CLICK);
  }, 0);

  // 添加全局点击事件监听
  document.addEventListener('click', handleClick);

  // 视频点击获取显示镜头中心x，y
  EventBus.on('cockpit_video_control', (data) => {
    // 点测温
    if (thermometricPointPopoverData.visible) {
      thermometricPointPopoverData.x = data.x;
      thermometricPointPopoverData.y = data.y;
      onThermometricPointConfirm(true)
    }
    // 区域测温
    if (thermometricAreaPopoverData.visible) {
      thermometricAreaPopoverData.x = data.x;
      thermometricAreaPopoverData.y = data.y;
    }
  });
  // 监听到镜头选中了
  EventBus.on('setLensSelected', (data) => {
    if (data) {
      lens.value = data;
    }
  });

  // 监听框选事件，自动设置测温区域
  EventBus.on('rectangle_selection', (data) => {
    if (data && payloadSelectInfo.value) {
      // 更新热成像区域数据
      thermometricAreaPopoverData.x = data.x;
      thermometricAreaPopoverData.y = data.y;
      thermometricAreaPopoverData.width = data.width;
      thermometricAreaPopoverData.height = data.height;

      // 自动调用确认函数提交更改
      onThermometricAreaConfirm(true);
    }
  });
});

const algoList = ref([]);
const activatedAlgoList = ref([]);
const availableAlgoList = ref([]);

const initAlgoList = async (sn) => {
  const res = await getAlgorithmList(1, 100, { algoType: 5 })
  algoList.value = res.data.data.records;

  const res2 = await getPlayAlgoList(1, 100, { sn })
  activatedAlgoList.value = res2.data.data.map(item => ({
    ...item,
    algoId: Number(item.algoId) || item.algoId
  }));
  if (props.cockpit_dock.sn) {
    availableAlgoList.value = algoList.value.filter(item => !activatedAlgoList.value.some(algo => String(algo.algoId) === String(item.algoId)));
  }
}

const onAlgoItemClick = (algoId, isActivate) => {
  console.log(algoId, isActivate);
  const params = {
    sn: props.cockpit_dock.sn,
    algoId: algoId
  }
  const action = isActivate ? activateAlgo : deactivateAlgo;
  action(params).then(res => {
    console.log(res);
    message.success('操作成功');
    initAlgoList(props.cockpit_dock.sn);
    // 添加EventBus事件通知，发送算法状态变更事件
    EventBus.emit('algoStatusChanged', {
      sn: props.cockpit_dock.sn,
      isActivate,
      algoId
    });
  })
}
/**
 * 打开起飞点，和目标点的时候  设置点位
 */
const setMarkerPoint = (type, point) => {
  let viewer = root.$cockpitMap;

  if (!point) {
    // 如果存在标记，移除它
    if (markerPoint[type]) {
      viewer.entities.remove(markerPoint[type]);
      markerPoint[type] = null;
    }
    return;
  }

  // 如果已经有标记，先移除
  if (markerPoint[type]) {
    viewer.entities.remove(markerPoint[type]);
  }

  // 获取设备像素比以适应高分辨率屏幕
  const pixelRatio = window.devicePixelRatio || 1;
  // 基础点大小和线宽
  const basePixelSize = 5;
  const baseOutlineWidth = 1;

  // 添加新的点位实体
  const [lng, lat] = point;
  markerPoint[type] = viewer.entities.add({
    position: Cesium.Cartesian3.fromDegrees(lng, lat),
    point: {
      pixelSize: basePixelSize * pixelRatio,
      color: Cesium.Color.RED,
      outlineColor: Cesium.Color.WHITE,
      outlineWidth: baseOutlineWidth * pixelRatio,
      heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
    }
  });
}

onBeforeUnmount(() => {
  EventBus.off('setLensSelected')
  EventBus.off('cockpit_video_control')
  EventBus.off('rectangle_selection')

  // 清除定时器
  if (clickTimer) {
    clearTimeout(clickTimer);
  }

  // 移除事件监听
  document.removeEventListener('click', handleClick);
})
useMqtt(deviceTopicInfo);

const isHeartbeatActive = ref(false);
const flightController = ref(false);

//进入飞行控制
async function enterFlightControl() {
  try {
    const { code, data } = await postDrcEnter({
      client_id: clientId.value,
      dock_sn: osdVisible.value.gateway_sn
    });
    if (code === 0) {
      flightController.value = true;
      if (data.sub && data.sub.length > 0) {
        deviceTopicInfo.subTopic = data.sub[0];
      }
      if (data.pub && data.pub.length > 0) {
        deviceTopicInfo.pubTopic = data.pub[0];
      }
      // 获取飞行控制权
      if (droneControlSource.value !== ControlSource.A) {
        await postFlightAuth(osdVisible.value.gateway_sn);
      }
      const mqtt = useMqtt(deviceTopicInfo);
      isHeartbeatActive.value = mqtt.isHeartbeatActive;
      message.success('Get flight control successfully');
      EventBus.emit('cockpit_control', '手动模式控制中');
    }
  } catch (error: any) {
  }
}

// 退出飞行控制
async function exitFlightCOntrol() {
  try {
    const { code } = await postDrcExit({
      client_id: clientId.value,
      dock_sn: osdVisible.value.gateway_sn
    });
    // if (code === 0) {
    flightController.value = false;
    deviceTopicInfo.subTopic = '';
    deviceTopicInfo.pubTopic = '';
    message.success('Exit flight control');
    EventBus.emit('cockpit_control', '非手动控制模式');
    // }
  } catch (error: any) {
    console.log(error);
  }
}

const { handleKeyup, handleEmergencyStop, resetControlState, drcLinkageZoomSet } = useManualControl(deviceTopicInfo, flightController);

//无人机方向键操作
function onMouseDown(type: KeyCode) {
  EventBus.emit('cockpit_control', `${type}-手动控制模式`);
  handleKeyup(type)
}

//无人机方向键操作
function onMouseUp(type: KeyCode) {
  resetControlState(type);
}

//起飞
const MAX_SPEED = 14;
const flyToPointPopoverData = reactive({
  visible: false,
  loading: false,
  latitude: null as null | number,
  longitude: null as null | number,
  height: null as null | number,
  maxSpeed: MAX_SPEED
});
/**
 * 显示飞向目标点弹窗
 */
function onShowFlyToPopover() {
  flyToPointPopoverData.visible = !flyToPointPopoverData.visible;
  flyToPointPopoverData.loading = false;
  flyToPointPopoverData.latitude = null
  flyToPointPopoverData.longitude = null
  flyToPointPopoverData.height = null;
  setMarkerPoint('flyToPoint', null);
}

async function onStopFlyToPoint() {
  await stopFlyToPoint(osdVisible.value.gateway_sn);
}

async function onFlyToConfirm(confirm: boolean) {
  if (confirm) {
    if (!flyToPointPopoverData.height || !flyToPointPopoverData.latitude || !flyToPointPopoverData.longitude) {
      message.error('Input error');
      return;
    }
    try {
      await flyToPoint(osdVisible.value.gateway_sn, {
        max_speed: flyToPointPopoverData.maxSpeed,
        points: [
          {
            latitude: flyToPointPopoverData.latitude,
            longitude: flyToPointPopoverData.longitude,
            height: flyToPointPopoverData.height
          }
        ]
      });
    } catch (error) {
    }
  }
  flyToPointPopoverData.visible = false;
  setMarkerPoint('flyToPoint', null);
}

//起飞
const takeoffToPointPopoverData = reactive({
  visible: false,
  loading: false,
  latitude: null as null | number,
  longitude: null as null | number,
  height: null as null | number,
  securityTakeoffHeight: null as null | number,
  maxSpeed: MAX_SPEED,
  rthAltitude: null as null | number,
  rcLostAction: LostControlActionInCommandFLight.RETURN_HOME,
  exitWaylineWhenRcLost: WaylineLostControlActionInCommandFlight.EXEC_LOST_ACTION,
  rthMode: ERthMode.SETTING,
  commanderModeLostAction: ECommanderModeLostAction.EXEC_LOST_ACTION,
  commanderFlightMode: ECommanderFlightMode.SETTING,
  commanderFlightHeight: null as null | number
});

function onShowTakeoffToPointPopover() {
  takeoffToPointPopoverData.visible = !takeoffToPointPopoverData.visible;
  takeoffToPointPopoverData.loading = false;
  takeoffToPointPopoverData.height = null;
  takeoffToPointPopoverData.latitude = null;
  takeoffToPointPopoverData.longitude = null;
  takeoffToPointPopoverData.securityTakeoffHeight = null;
  takeoffToPointPopoverData.rthAltitude = null;
  takeoffToPointPopoverData.rcLostAction = LostControlActionInCommandFLight.RETURN_HOME;
  takeoffToPointPopoverData.exitWaylineWhenRcLost = WaylineLostControlActionInCommandFlight.EXEC_LOST_ACTION;
  takeoffToPointPopoverData.rthMode = ERthMode.SETTING;
  takeoffToPointPopoverData.commanderModeLostAction = ECommanderModeLostAction.EXEC_LOST_ACTION;
  takeoffToPointPopoverData.commanderFlightMode = ECommanderFlightMode.SETTING;
  takeoffToPointPopoverData.commanderFlightHeight = null;
  setMarkerPoint('takeOffPoint', null);
}

async function onTakeoffToPointConfirm(confirm: boolean) {
  if (confirm) {
    if (!takeoffToPointPopoverData.height ||
      !takeoffToPointPopoverData.latitude ||
      !takeoffToPointPopoverData.longitude ||
      !takeoffToPointPopoverData.securityTakeoffHeight ||
      !takeoffToPointPopoverData.rthAltitude ||
      !takeoffToPointPopoverData.commanderFlightHeight) {
      message.error('Input error');
      return;
    }
    try {
      await takeoffToPoint(osdVisible.value.gateway_sn, {
        target_latitude: takeoffToPointPopoverData.latitude,
        target_longitude: takeoffToPointPopoverData.longitude,
        target_height: takeoffToPointPopoverData.height,
        security_takeoff_height: takeoffToPointPopoverData.securityTakeoffHeight,
        rth_altitude: takeoffToPointPopoverData.rthAltitude,
        max_speed: takeoffToPointPopoverData.maxSpeed,
        rc_lost_action: takeoffToPointPopoverData.rcLostAction,
        exit_wayline_when_rc_lost: takeoffToPointPopoverData.exitWaylineWhenRcLost,
        rth_mode: takeoffToPointPopoverData.rthMode,
        commander_mode_lost_action: takeoffToPointPopoverData.commanderModeLostAction,
        commander_flight_mode: takeoffToPointPopoverData.commanderFlightMode,
        commander_flight_height: takeoffToPointPopoverData.commanderFlightHeight
      });
      await EventBus.emit('takeoffToPointPopoverData', true);
    } catch (error) {
    }
  }
  takeoffToPointPopoverData.visible = false;
  setMarkerPoint('takeOffPoint', null);
}

//返回机场
const initCmdList = baseCmdList.map(cmdItem => Object.assign({}, cmdItem));
const cmdList = ref(initCmdList);
const { sendDockControlCmd } = useDockControl();

async function sendControlCmd(cmdItem: DeviceCmdItem, index: number) {
  cmdItem.loading = true;
  const result = await sendDockControlCmd({
    sn: osdVisible.value.gateway_sn,
    cmd: cmdItem.cmdKey,
    action: cmdItem.action
  }, false);
  if (result) {
    message.success('Return home successful');
    if (flightController.value) {
      exitFlightCOntrol();
    }
  } else {
    message.error('Failed to return home');
  }
  cmdItem.loading = false;
}

//暂停和回复航线
function flighttask(type) {
  let gateWaySn = store.state.dock.osdVisible.gateway_sn;
  if (type == '暂停') {
    flighttaskPause(getWorkspaceId(), gateWaySn).then(res => {
      if (res.code === 0) {
        message.success(res.message);
      } else {
        message.error(res.message);
      }
    });
  } else {
    flighttaskRecovery(getWorkspaceId(), gateWaySn).then(res => {
      if (res.code === 0) {
        message.success(res.message);
      } else {
        message.error(res.message);
      }
    });
  }
}

//负载
const payloadSelectInfo = {
  value: null as any,
  controlSource: undefined as undefined | ControlSource,
  options: [] as any,
  payloadIndex: '' as string,
  camera: undefined as undefined | DeviceOsdCamera // 当前负载osd信息
};
//负载均衡选择
const handlePayloadChange = (value: string) => {
  photoCount.value = 0;
  const payload = osdVisible.value.payloads?.find(item => item.payload_sn === value);
  if (payload) {
    payloadSelectInfo.payloadIndex = payload.payload_index || '';
    payloadSelectInfo.controlSource = payload.control_source;
    payloadSelectInfo.camera = undefined;
  }
};
const {
  checkPayloadAuth,
  authPayload,
  resetGimbal,
  switchCameraMode,
  takeCameraPhoto,
  startCameraRecording,
  stopCameraRecording,
  changeCameraFocalLength,
  cameraAim,
  cameraExposure,
  cameraExposureValue,
  cameraFocus,
  cameraFocusValue,
  thermometric,
  photostorage,
  splitscreen,
  videostorage,
  thermometricPoint,
  thermometricArea,
  photoCount
} = usePayloadControl();

//负载均衡控制
async function onAuthPayload() {
  const result = await authPayload(osdVisible.value.gateway_sn, payloadSelectInfo.payloadIndex);
  if (result) {
    payloadControlSource.value = ControlSource.A;
  }
}

//云台重置
const gimbalResetPopoverData = reactive({
  visible: false,
  loading: false,
  resetMode: null as null | GimbalResetMode
});

//云台重置
function onShowGimbalResetPopover() {
  gimbalResetPopoverData.visible = !gimbalResetPopoverData.visible;
  gimbalResetPopoverData.loading = false;
  gimbalResetPopoverData.resetMode = null;
}

//云台重置
async function onGimbalResetConfirm(confirm: boolean) {
  if (confirm) {
    if (gimbalResetPopoverData.resetMode === null) {
      message.error('Please select reset mode');
      return;
    }
    gimbalResetPopoverData.loading = true;
    try {
      await resetGimbal(osdVisible.value.gateway_sn, {
        payload_index: payloadSelectInfo.payloadIndex,
        reset_mode: gimbalResetPopoverData.resetMode
      });
    } catch (err) {
    }
  }
  gimbalResetPopoverData.visible = false;
}

// 切换相机模式
const switchCameraModePopoverData = reactive({
  visible: false,
  loading: false,
  cameraMode: null as null | CameraMode
});

function onShowSwitchCameraModePopover() {
  if (payloadSelectInfo.value) {
    switchCameraModePopoverData.visible = !switchCameraModePopoverData.visible;
    switchCameraModePopoverData.loading = false;
    // switchCameraModePopoverData.cameraMode = null
  } else {
    message.warning('请先选择负载！');
  }
}

async function onSwitchCameraModeConfirm(confirm: boolean) {
  if (confirm) {
    if (switchCameraModePopoverData.cameraMode === null) {
      message.error('Input error');
      return;
    }
    try {
      await switchCameraMode(osdVisible.value.gateway_sn, {
        payload_index: payloadSelectInfo.payloadIndex,
        camera_mode: switchCameraModePopoverData.cameraMode
      });
    } catch (error) {
      console.log(error);
    }
  }
  switchCameraModePopoverData.visible = false;
}

//开始录像
async function onStartCameraRecording() {
  if (!checkPayloadAuth(payloadSelectInfo.controlSource)) {
    return;
  }
  await startCameraRecording(osdVisible.value.gateway_sn, payloadSelectInfo.payloadIndex);
}

//结束录像
async function onStopCameraRecording() {
  if (!checkPayloadAuth(payloadSelectInfo.controlSource)) {
    return;
  }
  await stopCameraRecording(osdVisible.value.gateway_sn, payloadSelectInfo.payloadIndex);
}

//拍照
async function onTakeCameraPhoto() {
  if (!checkPayloadAuth(payloadSelectInfo.controlSource)) {
    return;
  }
  await takeCameraPhoto(osdVisible.value.gateway_sn, payloadSelectInfo.payloadIndex);
}

// 拍照
const zoomFactorPopoverData = reactive({
  visible: false,
  loading: false,
  cameraType: null as null | CameraType,
  zoomFactor: null as null | number,
})
async function onZoomFactorConfirm(confirm: boolean) {
  let { zoomFactor, cameraType } = zoomFactorPopoverData;
  if (confirm) {
    if (!zoomFactor || cameraType === null) {
      message.error('Please input Zoom Factor');
      return;
    }
    zoomFactorPopoverData.loading = true;
    try {
      await changeCameraFocalLength(osdVisible.value.gateway_sn, {
        payload_index: payloadSelectInfo.payloadIndex,
        camera_type: cameraType,
        zoom_factor: zoomFactor
      });
    } catch (err) {
    }
  }
  zoomFactorPopoverData.visible = false;
}

//变焦
function onShowZoomFactorPopover() {
  if (payloadSelectInfo.value) {
    zoomFactorPopoverData.visible = !zoomFactorPopoverData.visible;
    zoomFactorPopoverData.loading = false;
    zoomFactorPopoverData.cameraType = 'ir';
    zoomFactorPopoverData.zoomFactor = null;
  } else {
    message.warning('请先选择负载！');
  }
}

// 变焦
const cameraAimPopoverData = reactive({
  visible: false,
  loading: false,
  cameraType: null as null | CameraType,
  locked: false,
  x: null as null | number,
  y: null as null | number
});

// 变焦
async function onCameraAimConfirm(confirm: boolean) {
  const { cameraType, x, y, locked } = cameraAimPopoverData;
  if (confirm) {
    if (cameraType === null || x === null || y === null) {
      message.error('Input error');
      return;
    }
    try {
      await cameraAim(osdVisible.value.gateway_sn, {
        payload_index: payloadSelectInfo.payloadIndex,
        camera_type: cameraType,
        locked,
        x,
        y
      });
    } catch (error) {
    }
  }
  cameraAimPopoverData.visible = false;
}

//镜头中心
function onShowCameraAimPopover() {
  if (payloadSelectInfo.value) {
    cameraAimPopoverData.visible = !cameraAimPopoverData.visible;
    cameraAimPopoverData.loading = false;
    cameraAimPopoverData.cameraType = 'ir';
    cameraAimPopoverData.locked = false;
    cameraAimPopoverData.x = null;
    cameraAimPopoverData.y = null;
  } else {
    message.warning('请先选择负载！');
  }
}

// 相机曝光模式
const cameraExposurePopoverData = reactive({
  visible: false,
  loading: false,
  cameraType: null as null | CameraType,
  cameraMode: null as null | CameraMode
});

async function onCameraExposureConfirm(confirm: boolean) {
  if (confirm) {
    // if (cameraExposurePopoverData.cameraType === null || cameraExposurePopoverData.cameraMode === null) {
    //   message.error('Input error');
    //   return;
    // }
    try {
      await cameraExposure(osdVisible.value.gateway_sn, {
        payload_index: payloadSelectInfo.payloadIndex,
        camera_type: lens.value,
        exposure_mode: cameraExposurePopoverData.cameraMode
      });
    } catch (error) {
      console.log(error);
    }
  }
  cameraExposurePopoverData.visible = false;
}

//相机曝光模式
function onShowCameraExposurePopover() {
  if (payloadSelectInfo.value) {
    cameraExposurePopoverData.visible = !cameraExposurePopoverData.visible;
    cameraExposurePopoverData.loading = false;
    cameraExposurePopoverData.cameraType = null;
    cameraExposurePopoverData.cameraMode = null;
  } else {
    message.warning('请先选择负载！');
  }
}

// 曝光值调节
const cameraExposureValuePopoverData = reactive({
  visible: false,
  loading: false,
  cameraType: null as null | CameraType,
  cameraValue: null as null
});
/**
 * 曝光值确认选择
 */
async function onCameraExposureValueConfirm(confirm: boolean) {
  if (confirm) {
    // if (cameraExposureValuePopoverData.cameraType === null || cameraExposureValuePopoverData.cameraValue === null) {
    //   message.error('Input error');
    //   return;
    // }
    try {
      await cameraExposureValue(osdVisible.value.gateway_sn, {
        payload_index: payloadSelectInfo.payloadIndex,
        camera_type: lens.value,
        exposure_value: cameraExposureValuePopoverData.cameraValue
      });
    } catch (error) {
      console.log(error);
    }
  }
  cameraExposureValuePopoverData.visible = false;
}

// 曝光值调节
function onShowCameraExposureValuePopover() {
  if (payloadSelectInfo.value) {
    cameraExposureValuePopoverData.visible = !cameraExposurePopoverData.visible;
    cameraExposureValuePopoverData.loading = false;
    cameraExposureValuePopoverData.cameraType = null;
    cameraExposureValuePopoverData.cameraValue = null;
  } else {
    message.warning('请先选择负载！');
  }
}

// 相机对焦模式
const cameraFocusPopoverData = reactive({
  visible: false,
  loading: false,
  cameraType: null as null | CameraType,
  cameraMode: null as null | CameraMode
});

async function onCameraFocusConfirm(confirm: boolean) {
  if (confirm) {
    // if (cameraFocusPopoverData.cameraType === null || cameraFocusPopoverData.cameraMode === null) {
    //   message.error('Input error');
    //   return;
    // }
    try {
      await cameraFocus(osdVisible.value.gateway_sn, {
        payload_index: payloadSelectInfo.payloadIndex,
        camera_type: lens.value,
        focus_mode: cameraFocusPopoverData.cameraMode
      });
    } catch (error) {
      console.log(error);
    }
  }
  cameraFocusPopoverData.visible = false;
}

function onShowCameraFocusPopover() {
  if (payloadSelectInfo.value) {
    cameraFocusPopoverData.visible = !cameraFocusPopoverData.visible;
    cameraFocusPopoverData.loading = false;
    cameraFocusPopoverData.cameraType = null;
    cameraFocusPopoverData.cameraMode = null;
  } else {
    message.warning('请先选择负载！');
  }
}

// 对焦值调节
const cameraFocusValuePopoverData = reactive({
  visible: false,
  loading: false,
  cameraType: null as null | CameraType,
  cameraValue: null as null
});

async function onCameraFocusValueConfirm(confirm: boolean) {
  if (confirm) {
    // if (cameraFocusValuePopoverData.cameraType === null || cameraFocusValuePopoverData.cameraValue === null) {
    //   message.error('Input error');
    //   return;
    // }
    try {
      await cameraFocusValue(osdVisible.value.gateway_sn, {
        payload_index: payloadSelectInfo.payloadIndex,
        camera_type: lens.value,
        focus_value: cameraFocusValuePopoverData.cameraValue
      });
    } catch (error) {
      console.log(error);
    }
  }
  cameraFocusValuePopoverData.visible = false;
}

function onShowCameraFocusValuePopover() {
  if (payloadSelectInfo.value) {
    cameraFocusValuePopoverData.visible = !cameraFocusValuePopoverData.visible;
    cameraFocusValuePopoverData.loading = false;
    cameraFocusValuePopoverData.cameraType = null;
    cameraFocusValuePopoverData.cameraValue = null;
  } else {
    message.warning('请先选择负载！');
  }
}

// 测温模式
const thermometricPopoverData = reactive({
  visible: false,
  loading: false,
  thermometricType: null
});

async function onThermometricConfirm(confirm: boolean) {
  if (confirm) {
    //
    console.log("测温模式" + thermometricPopoverData.thermometricType)
    EventBus.emit('thermometricPopoverData', thermometricPopoverData.thermometricType == '1' ? true : false);
    if (thermometricPopoverData.thermometricType == '1') {
      thermometricPointPopoverData.visible = true;
      // 如果从类型2切换到类型1，需要关闭框选功能
      EventBus.emit('toggleDrawRectMode', false);
    } else {
      thermometricPointPopoverData.visible = false;
      // 当测温模式选择类型2时，触发框选功能
      if (thermometricPopoverData.thermometricType == '2') {
        EventBus.emit('toggleDrawRectMode', true);
      }
    }
    if (thermometricPopoverData.thermometricType === null) {
      message.error('Input error');
      return;
    }
    try {
      await thermometric(osdVisible.value.gateway_sn, {
        payload_index: payloadSelectInfo.payloadIndex,
        mode: thermometricPopoverData.thermometricType
      });
    } catch (error) {
      console.log(error);
    }
  } else {
    // 当用户取消测温功能时，确保框选功能也被关闭
    EventBus.emit('toggleDrawRectMode', false);
  }
  thermometricPopoverData.visible = false;
}

// 分屏
const splitScreenPopoverData = reactive({
  visible: false,
  loading: false,
  splitScreen: null
});

async function onSplitscreenConfirm(confirm: boolean) {
  if (confirm) {
    EventBus.emit('splitScreenPopoverData', true);
    if (splitScreenPopoverData.splitScreen === null) {
      message.error('Input error');
      return;
    }
    try {
      await splitscreen(osdVisible.value.gateway_sn, {
        payload_index: payloadSelectInfo.payloadIndex,
        enable: splitScreenPopoverData.splitScreen
      });
    } catch (error) {
      console.log(error);
    }
  }
  thermometricPopoverData.visible = false;
}

// 联动变焦
const linkageZoomPopoverData = reactive({
  visible: false,
  loading: false,
  linkageZoom: null
});

async function onLinkagezoomConfirm(confirm: boolean) {
  if (confirm) {
    EventBus.emit('linkageZoomPopoverData', true);
    if (linkageZoomPopoverData.linkageZoom === null) {
      message.error('Input error');
      return;
    }
    try {
      drcLinkageZoomSet(payloadSelectInfo.payloadIndex, linkageZoomPopoverData.linkageZoom);
    } catch (error) {
      console.log(error);
    }
  }
  linkageZoomPopoverData.visible = false;
}

// 照片存储
const photoStoragePopoverData = reactive({
  visible: false,
  loading: false,
  photoStorage: 'current',
});

async function onPhotoStorageConfirm(confirm: boolean) {
  if (photoStoragePopoverData.photoStorage.length < 1) {
    // message.warning('必须至少选择一个选项');
    photoStoragePopoverData.photoStorage = ['current']
  }

  if (confirm) {
    EventBus.emit('photoStoragePopoverData', true);
    if (photoStoragePopoverData.photoStorage === null) {
      message.error('Input error');
      return;
    }
    try {
      await photostorage(osdVisible.value.gateway_sn, {
        payload_index: payloadSelectInfo.payloadIndex,
        photo_storage_settings: photoStoragePopoverData.photoStorage
      });
    } catch (error) {
      console.log(error);
    }
  }
  photoStoragePopoverData.visible = false;
}

// 视频存储
const videoStoragePopoverData = reactive({
  visible: false,
  loading: false,
  videoStorage: 'current',
});

async function onVideoStorageConfirm(confirm: boolean) {
  if (videoStoragePopoverData.videoStorage.length < 1) {
    // message.warning('必须至少选择一个选项');
    videoStoragePopoverData.videoStorage = ['current']
  }

  if (confirm) {
    EventBus.emit('videoStoragePopoverData', true);
    if (videoStoragePopoverData.videoStorage === null) {
      message.error('Input error');
      return;
    }
    try {
      await videostorage(osdVisible.value.gateway_sn, {
        payload_index: payloadSelectInfo.payloadIndex,
        video_storage_settings: videoStoragePopoverData.videoStorage
      });
    } catch (error) {
      console.log(error);
    }
  }
  videoStoragePopoverData.visible = false;
}

// 测温点
const thermometricPointPopoverData = reactive({
  visible: false,
  loading: false,
  x: null as null | number,
  y: null as null | number
});



async function onThermometricPointConfirm(confirm: boolean) {
  if (confirm) {
    if (thermometricPointPopoverData.x === null || thermometricPointPopoverData.y === null) {
      message.error('Input error');
      return;
    }
    try {
      await thermometricPoint(osdVisible.value.gateway_sn, {
        payload_index: payloadSelectInfo.payloadIndex,
        x: thermometricPointPopoverData.x,
        y: thermometricPointPopoverData.y
      });
    } catch (error) {
      console.log(error);
    }
  }
  // thermometricPointPopoverData.visible = false;
}

// 测温区域
const thermometricAreaPopoverData = reactive({
  visible: false,
  loading: false,
  x: null as null | number,
  y: null as null | number,
  width: null as null | number,
  height: null as null | number
});

async function onThermometricAreaConfirm(confirm: boolean) {
  if (confirm) {
    if (thermometricAreaPopoverData.x === null || thermometricAreaPopoverData.y === null ||
      thermometricAreaPopoverData.width === null || thermometricAreaPopoverData.height === null) {
      message.error('Input error');
      return;
    }
    try {
      await thermometricArea(osdVisible.value.gateway_sn, {
        payload_index: payloadSelectInfo.payloadIndex,
        x: thermometricAreaPopoverData.x,
        y: thermometricAreaPopoverData.y,
        width: thermometricAreaPopoverData.width,
        height: thermometricAreaPopoverData.height
      });
    } catch (error) {
      console.log(error);
    }
  }
  thermometricAreaPopoverData.visible = false;
}

function onShowThermometricAreaPopover() {
  if (payloadSelectInfo.value) {
    thermometricAreaPopoverData.visible = !thermometricAreaPopoverData.visible;
    thermometricAreaPopoverData.loading = false;
    thermometricAreaPopoverData.x = null;
    thermometricAreaPopoverData.y = null;
    thermometricAreaPopoverData.width = null;
    thermometricAreaPopoverData.height = null;
  } else {
    message.warning('请先选择负载！');
  }
}

// 更新负载信息
watch(() => osdVisible.value.payloads, (payloads) => {
  // console.log('驾驶舱控制器：', payloads);
  if (payloads && payloads.length > 0) {
    payloadSelectInfo.value = payloads[0].payload_sn;
    payloadSelectInfo.payloadIndex = payloads[0].payload_index || '';
    payloadSelectInfo.options = payloads.map(item => ({ label: item.payload_name, value: item.payload_sn }));
    payloadSelectInfo.camera = undefined;
    payloadSelectInfo.controlSource = payloads[0].control_source || ControlSource.B;
  } else {
    payloadSelectInfo.value = null;
    payloadSelectInfo.controlSource = undefined;
    payloadSelectInfo.options = [];
    payloadSelectInfo.payloadIndex = '';
    payloadSelectInfo.camera = undefined;
  }
}, {
  immediate: true,
  deep: true
});
/**测温点 */
let temperature = ref<number>(0);
/**测温区域 */
let areaTemperature = ref<number>(0);
watch(() => props.deviceInfoAttrs, (deviceInfo) => {
  if (!isClickProcessing.value) {
    return;
  }
  // console.log('驾驶舱控制器：', deviceState);
  if (!deviceInfo) {
    return
  }
  const { device: droneOsd, dock: osd, cameras } = deviceInfo;
  // 负载默认值
  if (droneOsd && droneOsd.cameras) {
    // decoder.js:1 Jessibuca: [worker]: [h264 @ 0x61f060] SEI type 240 size 200 truncated at 192 printErr	@	
    // ir_metering_point：测温点 ，ir_metering_area：测温区域
    const { camera_mode, zoom_factor } = droneOsd.cameras[0];
    switchCameraModePopoverData.cameraMode = switchCameraModePopoverData.cameraMode || camera_mode;
    zoomFactorPopoverData.zoomFactor = zoomFactorPopoverData.zoomFactor || zoom_factor;
    //
    payloadSelectInfo.camera = payloadSelectInfo.camera || droneOsd.cameras.find(item => item.payload_index === payloadSelectInfo.payloadIndex);
  } else {
    payloadSelectInfo.camera = undefined;
  }
  // 红外
  if (cameras && cameras.length > 0) {
    let { ir_metering_point, ir_metering_area, photo_storage_settings, video_storage_settings, camera_mode, zoom_factor } = cameras[0];
    if (ir_metering_point) {
      temperature.value = ir_metering_point.temperature.toFixed(1);
      EventBus.emit('temperatureSet', temperature.value)
    }
    if (ir_metering_area) {
      areaTemperature.value = ir_metering_area.aver_temperature.toFixed(1);
      EventBus.emit('temperatureSet', areaTemperature.value)
    }
    if (photo_storage_settings !== undefined) {
      photoStoragePopoverData.photoStorage = photo_storage_settings;
    }
    if (video_storage_settings !== undefined) {
      videoStoragePopoverData.videoStorage = video_storage_settings;
    }
    if (camera_mode !== undefined) {
      switchCameraModePopoverData.cameraMode = camera_mode;
    }
    if (zoom_factor !== undefined) {
      zoomFactorPopoverData.zoomFactor = zoom_factor;
    }
  }
  // 监听负载是否控制
  if (osd) {
    if (osd.dock && osd.dock.link_osd == 1) {
      flightController.value = true;
    } else {
      payloadSelectInfo.camera = undefined;
      flightController.value = false;
    }
  }

}, { immediate: true, deep: true })
// ws 消息通知
const {
  droneControlSource,
  payloadControlSource
} = useDroneControlWsEvent(osdVisible.value.gateway_sn, payloadSelectInfo.value);
watch(() => payloadControlSource, (controlSource) => {
  payloadSelectInfo.controlSource = controlSource.value;
}, {
  immediate: true,
  deep: true
});
watch(() => props.cockpit_dock, (cockpit_dock) => {
  // 初始化算法列表
  initAlgoList(cockpit_dock.sn);
}, {
  immediate: true,
  deep: true
});
// watch(isHeartbeatActive, (value) => {
//   console.log('Heartbeat Active:', value);
// });

//喊话器
const sliderValue = ref<number>(50);
const textareaValue = ref<string>('');

//切换
const wrj = ref(true);
const xj = ref(false);
const hhq = ref(false);
const sf = ref(false);

function toggleActive(type) {
  const tabs = [wrj, xj, hhq, sf];
  tabs.forEach((tab, index) => {
    tab.value = index === type - 1;
  });
  wrjVisible.value = true;
}

//窗口隐藏显示
const shs = ref(false);
const wrjVisible = ref(true);

function toggleShs() {
  shs.value = !shs.value;
  wrjVisible.value = !wrjVisible.value;
}
</script>

<style lang="scss" scoped>
.controlContainer {
  position: absolute;
  width: 100%;
  aspect-ratio: 16 / 10;
  height: 33vh;
  // bottom: 1vw;
  // right: .5vw;
  // display: flex;
  border-radius: .3em;
  overflow: hidden;
  color: #fff;
}

.conetntSection {
  flex: auto;
  height: 100%;
  overflow: hidden;
  // background-color: #3c3c3c;
  transition: all .3s;
  box-shadow: 0 0 5px rgba(0, 0, 0, .4);

  .contentItem {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }
}

.subTabSection {
  flex: 0 0 2.3vw;
  height: 100%;
  width: 100%;
  display: flex;
  align-items: center;
  color: hsla(0, 0%, 100%, .9);
  transition: all .3s;
  user-select: none;
  font-size: clamp(13px, .85vw, 16px);

}

.noTab {
  justify-content: space-between;
  padding: 0 1em;
  background-color: rgba(0, 0, 0, .15);

  .right1 {
    display: flex;
    align-items: center;
    height: 100%;

    .openRouter {
      border: 1px solid #fff;
      border-radius: .25rem;
      cursor: pointer;
      padding: 0 .225rem;
      height: 1.875rem;
      line-height: 1.6rem;
      width: 5.1rem;

      span {
        font-size: .7rem;
        padding-left: 1.4rem;
        background: url('../../assets/jsc_hx.png') no-repeat 0;
        background-size: 1.5rem;
      }
    }

    .moreWrapper {
      position: absolute;
      top: 2.3vw;
      left: 0;
      right: 3.5vw;
      bottom: 0;
      color: #000;
      z-index: 3;
      background-color: #333;
      transition: all .3s;

      .uav {
        display: grid;
        grid-template-columns: 1fr 1fr 1fr;
        padding: 5%;
      }
    }
  }
}

.iconfont {
  transition: none;
  font-size: 1.2em;
  padding: .25em 0 .2em;
  font-family: iconfont !important;
  font-size: 16px;
  font-style: normal;
}

/* .mask{
    position: absolute;
    top: 2.3vw;
    left: 0;
    right: 9vw;
    bottom: 5vw;
    z-index: 1;
    background-color: transparent;
    cursor: not-allowed;
} */
.subContentSection {
  overflow-y: scroll;
  flex: auto;
  width: 100%;
  margin-bottom: 10px;
}

.subContentItem {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 1vw;
}

.uavControlContainer {
  padding: 0 .5vw;
}

.uavControlLeft {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  padding: 1vw 0;

  .uavControlTopActive::after {
    content: "";
    position: absolute;
    width: 100%;
    height: 44%;
    background-color: rgb(0 0 0 / 40%);
    z-index: 1;
    transition: all 0.3s;
  }

  .uavControlTop {
    height: 57%;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-around;

    .uavControlItem {
      position: relative;
      width: 34%;
      padding-top: 34%;
      height: 0;
      border: 1px solid #6f6f6f;
      background-color: #474747;
      border-radius: 50%;
      filter: drop-shadow(0 0 5px #303030);

      .uavControlButton {
        width: 1.8vw;
        height: 3.1vw;
        border-radius: .5em;
        border-bottom-left-radius: 0;
        border-bottom-right-radius: 0;
        background-color: #666;
        display: flex;
        justify-content: center;
        align-items: center;
        padding-bottom: 4%;
        position: absolute;
        top: 50%;
        left: 50%;
        margin-top: -3vw;
        margin-left: -.9vw;
        transform-origin: .9vw 3vw;
        transition: all .3s;
        clip-path: polygon(0 0, 100% 0, 100% 66%, 50% 100%, 0 66%);
        box-shadow: inset 0 0 3px hsla(0, 0%, 80.4%, .49);
        cursor: pointer;
        color: #fff;
        overflow: hidden;

        &::after {
          content: "";
          display: block;
          position: absolute;
          left: 0;
          right: 0;
          bottom: 0;
          top: var(--top);
          background-color: rgba(16, 205, 95, .3);
          z-index: 1;
          transition: all .3s;
        }

        &:first-child {
          .buttonText {
            letter-spacing: 4px;
            transform: scale(.9);
            writing-mode: vertical-lr;
          }
        }

        .buttonText {
          letter-spacing: 2px;
          white-space: nowrap;
          user-select: none;
          font-size: .8vw;
          z-index: 2;
        }

        .en_a {
          writing-mode: horizontal-tb;
          letter-spacing: 0px;
        }

        &:nth-child(2) {
          transform: rotate(180deg);
          padding-bottom: 8%;

          .buttonText {
            letter-spacing: 4px;
            transform: rotate(-180deg) scale(.9);
            writing-mode: vertical-lr;
          }
        }

        &:nth-child(3) {
          transform: rotate(270deg);

          .buttonText {
            transform: rotate(-270deg) scale(.9);
          }
        }

        &:nth-child(4) {
          transform: rotate(90deg);

          .buttonText {
            transform: rotate(-90deg) scale(.9);
          }
        }
      }
    }
  }

  .uavControlBottom {
    height: 30%;
    width: 100%;
    display: flex;
    align-items: flex-end;
    justify-content: space-around;
    padding: 0 .1vw;
  }

}

.buttonInner {
  padding: 0 .6em .1em;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-around;
  color: #fff;
  border-radius: .3em;
  font-size: .8vw;
  cursor: pointer;
  transition: all .3s;
  background-color: rgba(84, 84, 84, .41);
  border: 1px solid hsla(0, 0%, 41.2%, .74);
  box-shadow: inset 0 0 5px rgba(7, 7, 7, .41);
  white-space: nowrap;
  width: 100%;

  &:hover {
    background-color: rgba(54, 54, 54, .54);
  }

  /* .iconfont {
    transition: none;
    font-size: 1.2em;
    padding: .4em 0 .2em;

    font-family: iconfont !important;
    font-size: 16px;
    font-style: normal;
  } */
  .buttonText {
    transition: none;
    margin-bottom: .2em;
    transform: scale(.9);
  }
}

.uavControlRight {
  height: 40%;
  width: 23%;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  justify-content: space-around;
  padding: 0vw 1vw 0vw;
}

.tabSection {
  flex: 0 0 3.5vw;
  height: 100%;
  background-color: #3c3c3c;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-around;
  user-select: none;
  z-index: 1;

  .tabItem {
    width: 2.2vw;
    height: 2.5vw;
    display: flex;
    justify-content: center;
    align-items: center;
    line-height: 1;
    cursor: pointer;
    transition: all .3s;
    color: #999;
    border-radius: .3em;
  }

  .toIcon {
    font-size: 20px;
  }

  .toIconImg {
    width: 20px;
    height: 20px;
  }

  .active {
    background-color: rgba(0, 0, 0, .8);
  }

  .changeItem {
    width: 2.2vw;
    height: 2.2vw;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    transition: all .3s;
    background-color: #07a372;
    color: #fff;
    border-radius: 99em;
  }
}

.row {
  display: flex;
  flex-wrap: wrap;
  padding: 2px;

  +.row {
    margin-bottom: 6px;
  }

  &::v-deep(.ant-btn) {
    font-size: 12px;
    padding: 0 4px;
    margin-right: 5px;
  }
}

.shoutListConItem {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  position: relative;
  padding: 3%;

  .shoutTopbar {
    width: 100%;
    display: flex;
    padding: 0 .5em;
    align-items: center;
    justify-content: space-between;

    .voiceValue {
      width: 62%;
      height: 30px;
      margin-right: 2.6%;
      padding: 0 5%;
      font-size: .8em;
      color: #fff;
      display: flex;
      justify-content: space-between;
      align-items: center;
      user-select: none;
      box-shadow: inset 0 0 5px rgba(0, 0, 0, .6);
      border-radius: 99em;
    }

    .repeatPlay {
      font-size: .8em;
      color: #fff;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      padding: .3em .5em;
      border-radius: .3em;
      transition: all .3s;
    }
  }

  .shoutInputText {
    flex: 0 0 50%;
    width: 70%;
    font-size: 12px;

    .el-textarea__inner {
      display: block;
      resize: vertical;
      padding: 5px 15px;
      line-height: 1.5;
      box-sizing: border-box;
      width: 100%;
      font-size: inherit;
      color: #606266;
      box-shadow: none;
      border-radius: 4px;
      transition: border-color .2s cubic-bezier(.645, .045, .355, 1);
      height: 100%;
      background-color: #525252;
      color: #fff;
    }
  }

  .buttonList {
    flex: 0 0 20%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: .75vw;

    .buttonItem {
      padding: .6em 2.5em;
      line-height: 1;
      color: #fff;
      border-radius: .3em;
      background-color: #525252;
      white-space: nowrap;
      cursor: pointer;
      user-select: none;
      box-shadow: 0 0 5px rgba(19, 19, 19, .4);
      transition: all .3s;
      display: flex;
      justify-content: center;
      align-items: center;
      margin: 0 .8em;
      padding: .6em 3em;
    }
  }
}

::v-deep(.ant-slider:hover .ant-slider-track) {
  background-color: #18ae68;
}

::v-deep(.ant-slider-track) {
  background-color: #18ae68;
}

// 单选框的按钮
::v-deep(.ant-radio-group .ant-radio-wrapper) {
  color: #fff;
}

// 单选框禁用的样式
::v-deep(.ant-radio-disabled + span) {
  color: #999999;
}

// 多选框禁用的样式
::v-deep(.ant-checkbox-disabled + span) {
  color: #999999;
}

.red {
  color: red
}

.algoList {
  width: 100%;
  height: 100%;
  padding: 1em;
  display: flex;
  flex-direction: column;
  gap: 1em;
  overflow-y: auto;
}

.algoSection {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 0.5em;
  padding: 1em;
}

.algoSectionTitle {
  font-size: 1.1em;
  color: #fff;
  margin-bottom: 1em;
  padding-bottom: 0.5em;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.algoItem {
  display: flex;
  align-items: flex-start;
  padding: 0.8em;
  margin-bottom: 0.5em;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 0.3em;
  transition: all 0.3s;

  &:hover {
    background: rgba(255, 255, 255, 0.1);
  }

  &:last-child {
    margin-bottom: 0;
  }
}

.algoItemIcon {
  width: 1.5em;
  height: 1.5em;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #07a372;
  border-radius: 50%;
  margin-right: 1em;
  font-size: 1em;
  color: #fff;
  cursor: pointer;
}

.algoItemContent {
  flex: 1;
}

.algoItemName {
  font-size: 1em;
  color: #fff;
  margin-bottom: 0.3em;
}

.algoItemDesc {
  font-size: 0.9em;
  color: rgba(255, 255, 255, 0.6);
}
</style>