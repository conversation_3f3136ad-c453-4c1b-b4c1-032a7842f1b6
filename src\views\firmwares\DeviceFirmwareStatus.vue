<template>
  <div>
    <span class="status-tag pointer">
      <a-popconfirm :title="getTitle()"
                    ok-text="是"
                    cancel-text="否"
                    placement="left"
                    @confirm="onFirmwareStatusClick(firmware)"
      >
        <a-tag :color="firmware.firmware_status ? commonColor.NORMAL : commonColor.FAIL"
               :class="firmware.firmware_status ? 'border-corner ' : 'status-disable border-corner'">
          {{ getText(firmware.firmware_status) }}
        </a-tag>
      </a-popconfirm>
    </span>
  </div>
</template>

<script lang="ts" setup>
import {defineProps} from 'vue'
import {ELocalStorageKey} from '@/types'
import {commonColor} from '@/utils/color'
import {changeFirmareStatus} from '@/api/manage'
import {Firmware, FirmwareStatusEnum} from '@/types/device-firmware'
import { getWorkspaceId } from '@/utils/storage'

const props = defineProps<{
  firmware: Firmware
}>()


function getTitle() {
  return `您确定要将此固件设置为 ${getText(!props.firmware.firmware_status)} 吗?`
}

function getText(status: boolean) {
  return status ? FirmwareStatusEnum.TRUE : FirmwareStatusEnum.FALSE
}

function onFirmwareStatusClick(record: Firmware) {
  changeFirmareStatus(getWorkspaceId(), record.firmware_id, {status: !record.firmware_status}).then((res) => {
    if (res.code === 0) {
      record.firmware_status = !record.firmware_status
    }
  })
}
</script>

<style lang="scss" scoped>
.status-disable {
  opacity: 0.4;
}

.border-corner {
  border-radius: 3px;
}

.pointer {
  cursor: pointer;
}
</style>