<template>
  <!-- <div class="main-container"> -->
  <div class="video-container">
    <video ref="videoPlayer" width="100%" height="100%" style="object-fit: fill;" controls preload="auto"
      controlslist="noplaybackrate">
      <source :src="videoUrl" type="video/mp4" :key="VideoId">
    </video>
    <a-button v-if="!isVideo" type="primary" @click="togglePlayState" class="play-button">
      {{ isPlaying ? '暂停' : '播放' }}
    </a-button>
  </div>

  <!-- <div class="map-container">
      <div id="cesium-container" style="width: 100%; height: 100%;"></div>
    </div> -->

  <div class="name">{{ name }}</div>

  <div class="params" v-if="VideoId">
    <div class="row">
      <i class="flyParamIcon"></i>
      <div class="flyParamTitle">经度:</div>
      <div class="text">{{ longitudeVd }}</div>
    </div>
    <div class="row">
      <i class="flyParamIcon"></i>
      <div class="flyParamTitle">绝对高度：</div>
      <div class="text">{{ heightVd }}</div>
    </div>
    <div class="row">
      <i class="flyParamIcon"></i>
      <div class="flyParamTitle">相对起飞点高度：</div>
      <div class="text">{{ elevationVd }}</div>
    </div>

    <div class="row">
      <i class="flyParamIcon"></i>
      <div class="flyParamTitle">纬度:</div>
      <div class="text">{{ latitudeVd }}</div>
    </div>
    <div class="row">
      <i class="flyParamIcon"></i>
      <div class="flyParamTitle">水平速度：</div>
      <div class="text">{{ horizontalSpeedVd }}</div>
    </div>
    <div class="row">
      <i class="flyParamIcon"></i>
      <div class="flyParamTitle">垂直速度：</div>
      <div class="text">{{ verticalSpeed }}</div>
    </div>
  </div>

  <div class="speed" v-if="VideoId">
    <a-popover placement="left">
      <template #content>
        <p @click="changePlaybackRate(1)" style="cursor: pointer;">1x</p>
        <p @click="changePlaybackRate(2)" style="cursor: pointer;">2x</p>
        <p @click="changePlaybackRate(3)" style="cursor: pointer;">3x</p>
        <p @click="changePlaybackRate(4)" style="cursor: pointer;">4x</p>
      </template>
      <text>倍速</text>
    </a-popover>
  </div>

  <!-- <div class="follow-btn" v-if="VideoId">
      <a-button type="primary" @click="toggleFollow">
        {{ isFollowing ? '取消跟随' : '视角跟随' }}
      </a-button>
    </div> -->
  <!-- </div> -->
</template>

<script>
import M30Vd from '@/assets/m30.png';
import { getHistoryTrajectory } from '@/api/wayline';
import { getApp } from "@/root";
import { useGMapManage } from "@/hooks/use-c-map";
import { nextTick } from 'vue';

export default {
  props: ['VideoId', 'name', 'isVideo'],
  data() {
    return {
      videoUrl: '',
      videoPlayer: null,
      cesiumViewer: null,
      uavEntity: null,
      flightPath: null,
      passedPath: null,
      lineArrVd: [],
      uavDetailsVd: [],
      durationVd: 500,
      longitudeVd: '',//经度
      latitudeVd: '',//纬度
      heightVd: '',//高度
      elevationVd: '',//相对起飞点高度
      horizontalSpeedVd: '',//水平速度
      verticalSpeed: '',//垂直速度
      FJindex: 0,//拖动进度条记录索引
      rate: '',
      mapInitialized: false,
      isFollowing: true, // 视角跟随状态
      _pausedByCode: false,
      _playedByCode: false,
      _currentRequestId: 0, // 添加请求ID标识符
      isPlaying: false, // 控制播放状态
      playInterval: null, // 控制播放定时器
      playStartTime: 0, // 播放开始时间
      playCurrentTime: 0, // 当前播放时间
      playTotalTime: 0, // 总播放时间
    }
  },
  watch: {
    VideoId: {
      handler(newVal) {
        if (newVal) {
          this.initCesium();
          this.init();
        }
      },
      immediate: true
    }
  },
  mounted() {
    this.initCesium();
    if (this.VideoId) {
      this.init();
    }
  },
  beforeUnmount() {
    this.removeVideoEventListeners();
    this.clearPlayInterval();
    if (this.cesiumViewer) {
      this.cesiumViewer.destroy();
      this.cesiumViewer = null;
    }
  },
  methods: {
    initCesium() {
      const app = getApp();
      // useGMapManage().globalPropertiesConfig(app, null, 'cesium-container');
      this.cesiumViewer = app.config.globalProperties.$viewer;
      // 开启深度测试
      if (this.cesiumViewer) {
        this.cesiumViewer.scene.globe.depthTestAgainstTerrain = true;
      }
      this.mapInitialized = true;
    },
    async init() {
      this.rate = '';
      this.isPlaying = false;
      this.clearPlayInterval();

      // 先移除现有的事件监听器
      this.removeVideoEventListeners();

      if (this.isVideo) {
        // 更新视频URL
        this.videoUrl = 'https://media.hzdssoft.com/api/hztech-mediaServer/api/cloud/record/displayFlightMp4?wayLineTaskId=' + this.VideoId;

        // 使用nextTick确保DOM已更新
        await nextTick();

        if (this.$refs.videoPlayer) {
          // 清除视频缓存并重置视频元素
          const videoPlayer = this.$refs.videoPlayer;
          videoPlayer.pause();
          videoPlayer.currentTime = 0;
          videoPlayer.load(); // 强制重新加载视频

          // 设置视频属性
          videoPlayer.preload = "auto";
          videoPlayer.autoplay = false; // 确保不自动播放，而是由用户控制

          this.videoPlayer = videoPlayer;
        }
      } else {
        this.videoUrl = '';
        if (this.$refs.videoPlayer) {
          // 清除视频缓存并重置视频元素
          const videoPlayer = this.$refs.videoPlayer;
          videoPlayer.pause();
          videoPlayer.currentTime = 0;
          videoPlayer.load(); // 强制重新加载视频

          // 设置视频属性
          videoPlayer.preload = "auto";
          videoPlayer.autoplay = false; // 确保不自动播放，而是由用户控制

          this.videoPlayer = videoPlayer;
        }
      }

      // 先加载轨迹数据
      await this.historyTrajectory();

      if (this.isVideo) {
        // 再添加事件监听器
        this.addVideoEventListeners();
      } else {
        // 设置总播放时间，根据轨迹点数量设置合理的播放时间
        this.playTotalTime = this.lineArrVd.length * 0.1; // 每个点大约0.1秒
        this.playCurrentTime = 0;
      }
    },
    addVideoEventListeners() {
      if (!this.videoPlayer) return;

      // 使用事件选项防止冲突
      const eventOptions = { once: false, passive: true };

      this.videoPlayer.addEventListener('play', this.onPlay, eventOptions);
      this.videoPlayer.addEventListener('pause', this.onPause, eventOptions);
      this.videoPlayer.addEventListener('ended', this.onEnded, eventOptions);
      this.videoPlayer.addEventListener('seeked', this.onSeeked, eventOptions);
    },
    removeVideoEventListeners() {
      if (!this.videoPlayer) return;

      this.videoPlayer.removeEventListener('play', this.onPlay);
      this.videoPlayer.removeEventListener('pause', this.onPause);
      this.videoPlayer.removeEventListener('ended', this.onEnded);
      this.videoPlayer.removeEventListener('seeked', this.onSeeked);

      if (this.onTimeUpdate) {
        this.videoPlayer.removeEventListener('timeupdate', this.onTimeUpdate);
      }
    },
    onPlay() {
      if (!this.videoPlayer) return;

      // 如果是代码触发的播放，不重复处理
      if (this._playedByCode) {
        this._playedByCode = false;
        return;
      }

      console.log('视频开始播放');

      // 同步轨迹动画的位置
      this.syncTrajectoryWithVideo();

      // 视频开始播放时，确保动画同步启动
      if (this.FJindex === 0) {
        // 从头开始播放
        this.startAnimation();
      }

      // 如果开启了视角跟随，确保相机跟随无人机实体
      if (this.isFollowing && this.uavEntity) {
        this.enableEntityFollow();
      }
    },
    onPause() {
      if (!this.videoPlayer) return;

      // 如果是代码触发的暂停，不重复处理
      if (this._pausedByCode) {
        this._pausedByCode = false;
        return;
      }

      console.log('视频暂停');
    },
    onEnded() {
      if (!this.videoPlayer) return;

      console.log('视频播放结束');
      // 重置轨迹动画
      this.resetAnimation();
    },
    //拖动进度条时同步更新轨迹动画的位置
    onSeeked() {
      if (!this.videoPlayer) return;

      // 当视频拖动后，需要同步更新轨迹动画的位置
      const currentTime = this.videoPlayer.currentTime;
      if (currentTime > 0) { // 确认播放状态和有效时间
        const progressRatio = currentTime / this.videoPlayer.duration;
        const targetIndex = Math.floor(progressRatio * this.lineArrVd.length);
        // 检查 targetIndex 是否合理并更新标记位置
        if (targetIndex < this.lineArrVd.length) {
          this.updateMarkerPosition(targetIndex);
        }
        // 同步轨迹动画的位置
        this.syncTrajectoryWithVideo();
      }
    },
    // 切换播放状态（用于按钮模式）
    togglePlayState() {
      if (!this.isVideo) {
        this.isPlaying = !this.isPlaying;

        if (this.isPlaying) {
          this.startPlayWithoutVideo();
        } else {
          this.pausePlayWithoutVideo();
        }
      }
    },

    // 启动无视频播放
    startPlayWithoutVideo() {
      if (this.playInterval) {
        this.clearPlayInterval();
      }

      const startIndex = this.FJindex;
      const totalPoints = this.lineArrVd.length;

      if (startIndex >= totalPoints - 1) {
        // 如果已经到了终点，重新从头开始
        this.FJindex = 0;
        this.playCurrentTime = 0;
      }

      this.playStartTime = Date.now() - this.playCurrentTime * 1000;

      // 如果开启了视角跟随，确保相机跟随无人机实体
      if (this.isFollowing && this.uavEntity) {
        this.enableEntityFollow();
      }

      this.playInterval = setInterval(() => {
        const now = Date.now();
        const elapsedSeconds = (now - this.playStartTime) / 1000;
        this.playCurrentTime = elapsedSeconds;

        // 应用播放速率
        const effectiveElapsedTime = this.rate ? elapsedSeconds * this.rate : elapsedSeconds;

        // 计算当前应该显示的索引
        const progress = effectiveElapsedTime / this.playTotalTime;
        const currentIndex = Math.min(
          Math.floor(progress * totalPoints),
          totalPoints - 1
        );

        if (currentIndex >= totalPoints - 1) {
          // 到达终点
          this.updateMarkerPosition(totalPoints - 1);
          this.isPlaying = false;
          this.clearPlayInterval();
          return;
        }

        this.updateMarkerPosition(currentIndex);

      }, 50); // 每50ms更新一次
    },

    // 暂停无视频播放
    pausePlayWithoutVideo() {
      this.clearPlayInterval();
    },

    // 清除播放定时器
    clearPlayInterval() {
      if (this.playInterval) {
        clearInterval(this.playInterval);
        this.playInterval = null;
      }
    },

    // 调整速率
    changePlaybackRate(rate) {
      if (this.isVideo) {
        if (!this.videoPlayer) return;

        this.rate = rate;
        this.videoPlayer.playbackRate = rate;
        this.durationVd = 500 / rate; // 更新动画持续时间

        this.syncTrajectoryWithVideo(); // 更新倍速后同步轨迹和视频
      } else {
        // 非视频模式下更改速率
        this.rate = rate;
        this.durationVd = 500 / rate;

        if (this.isPlaying) {
          // 重新开始播放以应用新的播放速率
          this.pausePlayWithoutVideo();
          this.startPlayWithoutVideo();
        }
      }
    },
    //轨迹生成
    async historyTrajectory() {
      if (!this.cesiumViewer) {
        console.error('Cesium viewer尚未初始化');
        return;
      }

      // 创建当前请求的唯一标识符
      const requestId = ++this._currentRequestId;

      // 清除现有实体
      if (this.uavEntity) {
        this.cesiumViewer.entities.remove(this.uavEntity);
        this.uavEntity = null;
      }

      if (this.flightPath) {
        this.cesiumViewer.entities.remove(this.flightPath);
        this.flightPath = null;
      }

      if (this.passedPath) {
        this.cesiumViewer.entities.remove(this.passedPath);
        this.passedPath = null;
      }

      this.lineArrVd = []; // 清空之前的路径数组
      this.uavDetailsVd = [];

      if (!this.VideoId) {
        return;
      }

      try {
        const response = await getHistoryTrajectory({ jobId: this.VideoId });

        // 检查当前请求是否仍然是最新的请求
        if (requestId !== this._currentRequestId) {
          console.log('请求已过期，不处理返回结果');
          return;
        }

        if (response.data.data && response.data.data !== '') {
          response.data.data.forEach(location => {
            this.lineArrVd.push([location.longitude, location.latitude, location.height || 0]);
            this.uavDetailsVd.push(location);
          });
        } else {
          console.warn('没有轨迹数据');
          return;
        }
      } catch (error) {
        // 如果请求出错，但不是最新请求，忽略错误
        if (requestId !== this._currentRequestId) {
          return;
        }
        console.error('获取轨迹数据失败:', error);
        return;
      }

      if (this.lineArrVd.length === 0) {
        return;
      }

      // 创建无人机实体
      this.uavEntity = this.cesiumViewer.entities.add({
        position: Cesium.Cartesian3.fromDegrees(
          this.lineArrVd[0][0],
          this.lineArrVd[0][1],
          this.lineArrVd[0][2]
        ),
        billboard: {
          image: M30Vd,
          width: 32,
          height: 32,
          scaleByDistance: new Cesium.NearFarScalar(1000, 1, 20000, 0.5)
        }
      });

      // 创建飞行路径
      const positions = this.lineArrVd.map(point =>
        Cesium.Cartesian3.fromDegrees(point[0], point[1], point[2])
      );

      this.flightPath = this.cesiumViewer.entities.add({
        polyline: {
          positions: positions,
          width: 4,
          material: Cesium.Color.fromCssColorString('#0aed8b')
        }
      });

      // 创建已通过路径
      this.passedPath = this.cesiumViewer.entities.add({
        polyline: {
          positions: [positions[0]],
          width: 3,
          material: Cesium.Color.GREEN
        }
      });

      // 计算整个飞行路径的边界范围
      const boundingSphere = Cesium.BoundingSphere.fromPoints(positions);

      // 使用flyToBoundingSphere方法自动计算最佳视角以查看整个路径
      this.cesiumViewer.camera.flyToBoundingSphere(boundingSphere, {
        offset: new Cesium.HeadingPitchRange(0, Cesium.Math.toRadians(-45), boundingSphere.radius * 2),
        duration: 0 // 立即跳转，不使用动画
      });

      // 更新初始位置信息
      this.updatePositionInfo(0);
    },
    //开始动画
    startAnimation() {
      if (!this.uavEntity || this.lineArrVd.length === 0) return;

      // 设置初始位置
      this.updateMarkerPosition(0);

      // 视频播放时，根据视频时间更新位置
      this.syncTrajectoryWithVideo();

      // 如果启用了视角跟随，设置视角到无人机位置
      if (this.isFollowing && this.uavEntity) {
        this.enableEntityFollow();
      }
    },
    //轨迹动画暂停
    pauseAnimation() {
      // 暂停动画在视频事件处理中
      if (this.videoPlayer && !this.videoPlayer.paused) {
        // 标记暂停来源为代码控制，避免事件循环
        this._pausedByCode = true;
        this.videoPlayer.pause();
      }
    },
    //轨迹动画继续
    resumeAnimation() {
      // 继续动画在视频事件处理中
      if (this.videoPlayer && this.videoPlayer.paused) {
        // 标记继续来源为代码控制，避免事件循环
        this._playedByCode = true;

        // 使用Promise和setTimeout确保异步执行，避免事件堆栈冲突
        Promise.resolve().then(() => {
          setTimeout(() => {
            this.videoPlayer.play()
              .then(() => {
                // 播放成功后同步轨迹
                this.syncTrajectoryWithVideo();

                // 如果视角跟随已开启，重新激活
                if (this.isFollowing && this.uavEntity) {
                  this.enableEntityFollow();
                }
              })
              .catch(error => {
                console.error('视频播放失败:', error);
              });
          }, 50); // 短暂延迟以避免潜在冲突
        });
      }
    },
    //轨迹动画结束
    resetAnimation() {
      this.updateMarkerPosition(0);
    },
    //更新位置信息
    updatePositionInfo(index) {
      if (index < this.uavDetailsVd.length) {
        this.longitudeVd = this.lineArrVd[index][0];
        this.latitudeVd = this.lineArrVd[index][1];
        this.heightVd = this.uavDetailsVd[index].height;
        this.elevationVd = this.uavDetailsVd[index].elevation;
        this.horizontalSpeedVd = this.uavDetailsVd[index].horizontal_speed;
        this.verticalSpeed = this.uavDetailsVd[index].vertical_speed;
      }
    },
    //更新轨迹动画位置
    updateMarkerPosition(index) {
      if (!this.uavEntity || !this.lineArrVd[index]) return;

      this.FJindex = index;

      // 更新无人机位置
      this.uavEntity.position = Cesium.Cartesian3.fromDegrees(
        this.lineArrVd[index][0],
        this.lineArrVd[index][1],
        this.lineArrVd[index][2]
      );

      // 更新已通过路径
      const passedPositions = this.lineArrVd.slice(0, index + 1).map(point =>
        Cesium.Cartesian3.fromDegrees(point[0], point[1], point[2])
      );

      this.passedPath.polyline.positions = passedPositions;

      // 更新位置信息
      this.updatePositionInfo(index);
    },
    //修改视频播放控制
    syncTrajectoryWithVideo() {
      if (!this.videoPlayer || this.lineArrVd.length === 0) {
        return; // 如果videoPlayer未初始化或轨迹数据为空，直接返回
      }

      // 移除之前的监听器以避免重复
      if (this.onTimeUpdate) {
        this.videoPlayer.removeEventListener('timeupdate', this.onTimeUpdate);
        this.onTimeUpdate = null;
      }

      // 添加新的监听器
      this.onTimeUpdate = () => {
        // 检查视频是否正在播放
        if (this.videoPlayer.paused) return;

        const currentTime = this.videoPlayer.currentTime;
        if (currentTime > 0) {
          const videoDuration = this.videoPlayer.duration;
          const progressRatio = currentTime / videoDuration;
          const targetIndex = Math.max(0, Math.min(
            Math.floor(progressRatio * this.lineArrVd.length),
            this.lineArrVd.length - 1
          ));

          // 使用requestAnimationFrame保证平滑更新且不干扰视频播放
          requestAnimationFrame(() => {
            this.updateMarkerPosition(targetIndex);
          });
        }
      };

      this.videoPlayer.addEventListener('timeupdate', this.onTimeUpdate);
    },
    toggleFollow() {
      this.isFollowing = !this.isFollowing;
      if (this.isFollowing) {
        this.enableEntityFollow();
      } else {
        this.disableEntityFollow();
      }
    },
    enableEntityFollow() {
      if (!this.cesiumViewer || !this.uavEntity) return;

      // 设置相机跟随无人机实体
      this.cesiumViewer.trackedEntity = this.uavEntity;
    },
    disableEntityFollow() {
      if (!this.cesiumViewer) return;

      // 取消跟随
      this.cesiumViewer.trackedEntity = undefined;
    }
  }
}
</script>

<style scoped lang="scss">
.main-container {
  position: relative;
  width: 100%;
  height: 100%;
}

.video-container {
  position: fixed;
  top: 59.4vh;
  left: 1.3vw;
  width: 35.8vw;
  height: 40.6vh;
  z-index: 1;
}

.play-button {
  font-size: 16px;
  height: auto;
  position: absolute;
  bottom: 8%;
  left: 3%;
  z-index: 2;
}

.map-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.name {
  position: absolute;
  top: 10px;
  left: 10px;
  font-size: 20px;
  color: #000000;
}

.params {
  position: absolute;
  width: 50%;
  height: 12%;
  bottom: 10px;
  right: 10px;
  background: rgba(0, 0, 0, 0.5);
  padding: 20px 0 0 10px;
  display: flex;
  flex-wrap: wrap;
  z-index: 2;
  font-size: 12px;

  .row {
    width: 33%;
    height: 25%;
    color: #fff;
    position: relative;
    display: flex;
    align-items: center;

    .flyParamIcon {
      width: 2%;
      height: 55%;
      background: #11cc80;
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
    }

    .flyParamTitle {
      margin-left: 6%;
      flex: 1;
      white-space: nowrap;
    }

    .text {
      margin-left: 0;
      flex: 3;
    }
  }
}

.speed {
  width: 37px;
  height: 37px;
  border-radius: 50%;
  position: absolute;
  top: 10%;
  right: 10px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  color: white;
  background: rgba(0, 0, 0, .5);
}

/* 视角跟随按钮样式 */
.follow-btn {
  position: absolute;
  top: 10%;
  right: 60px;
  z-index: 2;
}
</style>