<template>
  <div class="media-panel-wrapper">
    <a-form style="background-color: #fff;padding-top: 10px;">
      <a-row>
        <a-col :span="12">
          <a-form-item label="航线名称" style="padding-left: 10px;">
            <a-input v-model:value="inputName" placeholder="请输入航线名称" allow-clear style="width: 200px;" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item>
            <a-button style="margin-left: 10px;" type="primary" @click="isShowSearch">搜索</a-button>
            <a-button style="margin-left: 10px;" @click="reset">重置</a-button>
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>

    <a-config-provider :locale="zhCN">
      <a-table class="media-table" :scroll="{ y: 300 }" :columns="columns" @change="refreshData"
        :data-source="waylinesData.data" :pagination="paginationProp" :row-selection="{
          type: 'radio',
          onChange: onSelectChange,
          selectedRowKeys: selectedRowKeys
        }">
        <template #update_time="{ text }">
          <span>{{ new Date(text).toLocaleString() }}</span>
        </template>
        <template #templateTypes="{ record }">
          <span>{{ formatTemplateTypes(record) }}</span>
        </template>
      </a-table>
    </a-config-provider>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { getWaylineFiles } from '@/api/wayline';
import zhCN from "ant-design-vue/es/locale/zh_CN";
import { getWorkspaceId } from '@/utils/storage'
import { useFormatTask } from '@/components/task/use-format-task'

const { formatTemplateTypes } = useFormatTask()
const inputName = ref('');
const waylinesData = reactive({
  data: [],
});
const columns = [
  {
    title: '航线名称',
    dataIndex: 'name',
    ellipsis: true,
  },
  // {
  //   title: '航线id',
  //   dataIndex: 'id',
  //   ellipsis: true,
  // },
  {
    title: '航线类型',
    dataIndex: 'template_types',
    ellipsis: true,
    slots: { customRender: 'templateTypes' },
  },
  {
    title: '创建人',
    dataIndex: 'user_name',
    ellipsis: true,
  },
  {
    title: '更新时间',
    dataIndex: 'update_time',
    ellipsis: true,
    slots: { customRender: 'update_time' },
  },
];

const body = reactive({
  page: 1,
  total: 0,
  page_size: 10,
});
const paginationProp = reactive({
  pageSizeOptions: ['10', '20', '50', '100'],
  showQuickJumper: true,
  showSizeChanger: true,
  pageSize: 10,
  current: 1,
  total: 0,
});

// 用于存储选中的行的键值
const selectedRowKeys = ref([]);

onMounted(() => {
  selectedRowKeys.value = [];
  getWaylines();
});

async function getWaylines() {
  let params = {
    page: body.page,
    page_size: body.page_size,
    order_by: 'update_time desc',
    // payload_model_key: selectType.value.split(','),
    key: inputName.value,
  }
  if (inputName.value == '') delete params.key
  await getWaylineFiles(getWorkspaceId(), params).then(res => {
    if (res.data.code !== 0) {
      return;
    }

    waylinesData.data = res.data.data.list;
    paginationProp.total = res.data.data.pagination.total;
    paginationProp.current = res.data.data.pagination.page;
    body.total = res.data.data.pagination.total;
    body.page = res.data.data.pagination.page;
  })
}

function isShowSearch() {
  getWaylines();
}

function reset() {
  body.page = 1
  body.total = 0
  body.page_size = 10
  inputName.value = '';
  getWaylines();
}

function refreshData(page) {
  body.page = page?.current!;
  body.page_size = page?.pageSize!;
  getWaylines();
}

const emit = defineEmits(['onSelectedRowChanged']);
const onSelectChange = (selectedRowKeys: string[], selectRows: []) => {
  selectedRowKeys.value = selectedRowKeys;
  emit('onSelectedRowChanged', selectRows[0].id, selectRows[0].name, selectRows[0].template_types)
};
</script>

<style lang="scss" scoped>
.media-panel-wrapper {
  width: 100%;
  height: 100%;
  padding: 0 16px 16px 16px;

  .media-table {
    background: #fff;
  }

  .action-area {
    color: #333;
    cursor: pointer;
  }
}

.header {
  width: 100%;
  height: 60px;
  background: #fff;
  padding: 16px;
  font-size: 20px;
  font-weight: bold;
  text-align: start;
  color: #000;
}

.bor-b {
  height: 50px;
  line-height: 50px;
  border-bottom: 1px solid #4f4f4f;
  font-weight: 450;
}
</style>