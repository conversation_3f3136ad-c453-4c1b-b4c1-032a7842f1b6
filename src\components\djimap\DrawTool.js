/**
 * 绘制工具类
 * 注意：此类依赖全局Cesium对象
 */
export default class DrawTool {
  /**
   * 构造函数
   * @param viewer
   */
  constructor(viewer) {
    this.viewer = viewer;
    this._drawHandler = null; //事件
    this._dataSource = null; //存储entities
    this._tempPositions = []; //存储点集合
    this._mousePos = null; //移动点
    this._drawType = null; //类型
    this._lastPointEntity = null; // 最后一个点的实体引用
    this._hoverHandler = null; // 悬停事件处理器
    this._clickHandler = null; // 点击事件处理器
    this._drawingCallback = null; // 绘制完成回调函数

    // 设置抗锯齿和高清渲染配置
    this.viewer.scene.postProcessStages.fxaa.enabled = true;
    // 检查是否支持MSAA，如果支持则设置样本数
    if (this.viewer.scene.msaaSupported) {
      this.viewer.scene.msaaSamples = 8; // 提高MSAA采样数
    }

    // 提高分辨率比例
    this.viewer.resolutionScale = 1.5;

    // 提高地形质量
    this.viewer.scene.globe.maximumScreenSpaceError = 1.0;

    // 在相机移动时临时降低质量，停止后恢复
    this.viewer.scene.requestRenderMode = true;
    this.viewer.scene.maximumRenderTimeChange = 0.5;
  }

  /**
   * 激活点线面
   * @param drawType
   */
  activate(drawType, callback) {
    this.clearAll();
    this._drawType = drawType;
    this._dataSource = new Cesium.CustomDataSource("_dataSource");
    this.viewer.dataSources.add(this._dataSource);
    this._drawingCallback = callback; // 保存回调函数
    this._registerEvents(callback); //注册鼠标事件
  }

  /**
   * 激活并初始化已有多边形数据
   * @param {String} drawType 绘制类型
   * @param {Function} callback 回调函数
   * @param {Array} polygonPositions 多边形坐标点数组 [[lng, lat], [lng, lat], ...]
   */
  activateWithPolygon(drawType, callback, polygonPositions) {
    this.clearAll();
    this._drawType = drawType;
    this._dataSource = new Cesium.CustomDataSource("_dataSource");
    this.viewer.dataSources.add(this._dataSource);
    this._drawingCallback = callback; // 保存回调函数

    // 将经纬度坐标转换为Cesium的Cartesian3坐标
    if (polygonPositions && polygonPositions.length > 2) {
      this._tempPositions = polygonPositions.map(point => {
        return Cesium.Cartesian3.fromDegrees(point[0], point[1]);
      });

      // 绘制多边形
      this._removeAllEvent();
      this._dataSource.entities.removeAll();
      this._rightClickEventForPolygon();

      // 添加每个顶点的圆点
      for (let i = 0; i < this._tempPositions.length; i++) {
        this._dataSource.entities.add({
          position: this._tempPositions[i],
          point: {
            color: Cesium.Color.WHITE,
            pixelSize: 10,
            heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
            outlineColor: Cesium.Color.WHITE,
            outlineWidth: 2,
            // disableDepthTestDistance: Number.POSITIVE_INFINITY
          }
        });
      }

      // 创建多边形实体
      this._dataSource.entities.add({
        polyline: {
          positions: this._tempPositions,
          clampToGround: true,
          width: 6,
          material: new Cesium.PolylineOutlineMaterialProperty({
            color: Cesium.Color.fromCssColorString('#2d8cf0'),
            outlineWidth: 2,
            outlineColor: Cesium.Color.WHITE
          }),
          depthFailMaterial: new Cesium.PolylineOutlineMaterialProperty({
            color: Cesium.Color.fromCssColorString('#2d8cf0'),
            outlineWidth: 2,
            outlineColor: Cesium.Color.WHITE
          }),
          classificationType: Cesium.ClassificationType.BOTH,
          shadows: Cesium.ShadowMode.DISABLED,
          distanceDisplayCondition: new Cesium.DistanceDisplayCondition(0, 9999999),
        },
        polygon: {
          hierarchy: this._tempPositions,
          extrudedHeightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
          material: Cesium.Color.fromCssColorString('#2d8cf0').withAlpha(0.3),
          clampToGround: true,
        },
      });
    } else {
      // 如果没有足够的点，则正常注册鼠标事件
      this._registerEvents(callback);
    }
  }

  /**
   * 注册鼠标事件
   */
  _registerEvents(callback) {
    this._drawHandler = new Cesium.ScreenSpaceEventHandler(
      this.viewer.scene.canvas
    );
    this.viewer.scene.globe.depthTestAgainstTerrain = true; //开启深度测试
    switch (this._drawType) {
      case "Polygon": {
        this._leftClickEventForPolygon();
        this._mouseMoveEventForPolygon();
        break;
      }
    }

    // 注册悬停事件处理器
    this._registerHoverEvent();

    // 注册点击事件处理器
    this._registerClickEvent();
  }

  /**
   * 注册悬停事件
   * @private
   */
  _registerHoverEvent() {
    this._hoverHandler = new Cesium.ScreenSpaceEventHandler(this.viewer.scene.canvas);
    this._hoverHandler.setInputAction((movement) => {
      const pickedObject = this.viewer.scene.pick(movement.endPosition);
      if (Cesium.defined(pickedObject) &&
        pickedObject.id === this._lastPointEntity) {
        // 鼠标悬停在最后一个点上，改变样式
        this._lastPointEntity.point.color = Cesium.Color.YELLOW;
        this._lastPointEntity.point.pixelSize = 14;
        this.viewer.canvas.style.cursor = 'pointer';
      } else if (this._lastPointEntity) {
        // 鼠标离开最后一个点，恢复样式
        this._lastPointEntity.point.color = Cesium.Color.WHITE;
        this._lastPointEntity.point.pixelSize = 10;
        this.viewer.canvas.style.cursor = 'default';
      }
    }, Cesium.ScreenSpaceEventType.MOUSE_MOVE);
  }

  /**
   * 注册点击事件
   * @private
   */
  _registerClickEvent() {
    this._clickHandler = new Cesium.ScreenSpaceEventHandler(this.viewer.scene.canvas);
    this._clickHandler.setInputAction((movement) => {
      const pickedObject = this.viewer.scene.pick(movement.position);
      if (Cesium.defined(pickedObject) &&
        pickedObject.id === this._lastPointEntity &&
        this._tempPositions.length >= 3) { // 至少需要3个点才能形成一个有效的多边形

        // 结束绘制 - 与右键点击效果类似
        this._tempPositions.push(this._tempPositions[0]); // 闭合多边形
        this._removeAllEvent();
        this._dataSource.entities.removeAll();
        // 绘制完成后，重新注册右键菜单事件
        this._rightClickEventForPolygon();

        // 添加每个顶点的圆点
        for (let i = 0; i < this._tempPositions.length; i++) {
          this._dataSource.entities.add({
            position: this._tempPositions[i],
            point: {
              color: Cesium.Color.WHITE,
              pixelSize: 10,
              heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
              outlineColor: Cesium.Color.WHITE,
              outlineWidth: 2,
              disableDepthTestDistance: Number.POSITIVE_INFINITY
            }
          });
        }

        // 创建最终的多边形实体
        const polygonEntity = this._dataSource.entities.add({
          polyline: {
            positions: this._tempPositions,
            clampToGround: true, // 贴地
            width: 6,
            material: new Cesium.PolylineOutlineMaterialProperty({
              color: Cesium.Color.fromCssColorString('#2d8cf0'),
              outlineWidth: 2,
              outlineColor: Cesium.Color.WHITE
            }),
            depthFailMaterial: new Cesium.PolylineOutlineMaterialProperty({
              color: Cesium.Color.fromCssColorString('#2d8cf0'),
              outlineWidth: 2,
              outlineColor: Cesium.Color.WHITE
            }),
            classificationType: Cesium.ClassificationType.BOTH,
            shadows: Cesium.ShadowMode.DISABLED,
            distanceDisplayCondition: new Cesium.DistanceDisplayCondition(0, 9999999),
          },
          polygon: {
            hierarchy: this._tempPositions,
            extrudedHeightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
            material: Cesium.Color.fromCssColorString('#2d8cf0').withAlpha(0.3),
            clampToGround: true,
          },
        });

        // 转换Cesium.Cartesian3为普通经纬度坐标
        const latLngPositions = this._tempPositions.map(position => {
          const cartographic = Cesium.Cartographic.fromCartesian(position);
          const longitude = Cesium.Math.toDegrees(cartographic.longitude);
          const latitude = Cesium.Math.toDegrees(cartographic.latitude);
          return [longitude, latitude];
        });

        // 调用回调函数，传递绘制完成的实体和转换后的坐标
        if (this._drawingCallback && typeof this._drawingCallback === 'function') {
          this._drawingCallback(latLngPositions);
        }
      }
    }, Cesium.ScreenSpaceEventType.LEFT_CLICK);
  }

  /**
   * 鼠标事件之绘制面的左击事件
   * @private
   */
  _leftClickEventForPolygon() {
    this._drawHandler.setInputAction((e) => {
      let p = this.viewer.scene.pickPosition(e.position);
      if (!p) return;
      this._tempPositions.push(p);
      this._addPolygon();
    }, Cesium.ScreenSpaceEventType.LEFT_CLICK);
  }

  /**
   * 鼠标事件之绘制面的移动事件
   * @private
   */
  _mouseMoveEventForPolygon() {
    this._drawHandler.setInputAction((e) => {
      let p = this.viewer.scene.pickPosition(e.endPosition);
      if (!p) return;
      this._mousePos = p;
    }, Cesium.ScreenSpaceEventType.MOUSE_MOVE);
  }

  /**
   * 鼠标事件之绘制面的右击事件
   * @private
   */
  _rightClickEventForPolygon() {
    this._drawHandler = new Cesium.ScreenSpaceEventHandler(
      this.viewer.scene.canvas
    );
    this._drawHandler.setInputAction((e) => {
      // 检查点击位置是否在实体上
      const pickedObject = this.viewer.scene.pick(e.position);
      // 放宽条件，只要_tempPositions有足够的点位就显示菜单，不再严格要求点击在多边形上
      if (this._tempPositions.length < 3) {
        // 点位不足，不显示菜单
        return;
      }

      // 获取鼠标事件
      const mouseEvent = e.originalEvent || window.event;
      const clientX = mouseEvent ? mouseEvent.clientX : e.position.x;
      const clientY = mouseEvent ? mouseEvent.clientY : e.position.y;

      // 创建右键菜单
      let menuDiv = document.getElementById('drawToolContextMenu');
      if (!menuDiv) {
        menuDiv = document.createElement('div');
        menuDiv.id = 'drawToolContextMenu';
        menuDiv.className = 'draw-tool-context-menu';
        menuDiv.style.position = 'absolute';
        menuDiv.style.backgroundColor = 'white';
        menuDiv.style.border = '1px solid #ccc';
        menuDiv.style.padding = '5px';
        menuDiv.style.borderRadius = '4px';
        menuDiv.style.boxShadow = '0 2px 10px rgba(0,0,0,0.2)';
        menuDiv.style.zIndex = '9999';
        document.body.appendChild(menuDiv);

        // 添加删除选区选项
        const deleteOption = document.createElement('div');
        deleteOption.textContent = '删除选区';
        deleteOption.style.padding = '5px 10px';
        deleteOption.style.cursor = 'pointer';
        deleteOption.style.color = '#333';
        deleteOption.style.fontSize = '14px';

        // 鼠标悬停效果
        deleteOption.onmouseover = function () {
          this.style.backgroundColor = '#f0f0f0';
        };
        deleteOption.onmouseout = function () {
          this.style.backgroundColor = 'transparent';
        };

        // 点击删除选项
        deleteOption.onclick = () => {
          // 保存当前的绘画类型和回调函数
          const currentDrawType = this._drawType;
          const currentCallback = this._drawingCallback;
          
          // 清除绘制内容
          this.clearAll();
          // 隐藏菜单
          menuDiv.style.display = 'none';

          // 向回调函数传递空数组，通知父组件删除了选区
          if (currentCallback && typeof currentCallback === 'function') {
            currentCallback([]);
          }

          // 使用保存的值重新激活
          this.activate(currentDrawType, currentCallback);
        };

        menuDiv.appendChild(deleteOption);
      }

      // 设置菜单位置
      menuDiv.style.left = clientX + 'px';
      menuDiv.style.top = clientY + 'px';
      menuDiv.style.display = 'block';

      // 点击其他区域隐藏菜单
      const hideMenu = () => {
        menuDiv.style.display = 'none';
        document.removeEventListener('click', hideMenu);
      };

      // 延迟添加事件监听器，避免立即触发
      setTimeout(() => {
        document.addEventListener('click', hideMenu);
      }, 100);

    }, Cesium.ScreenSpaceEventType.RIGHT_CLICK);
  }

  /**
   * 移除所有鼠标事件
   * @private
   */
  _removeAllEvent() {
    this._drawHandler &&
      (this._drawHandler.removeInputAction(
        Cesium.ScreenSpaceEventType.LEFT_CLICK
      ),
        this._drawHandler.removeInputAction(
          Cesium.ScreenSpaceEventType.MOUSE_MOVE
        ),
        this._drawHandler.removeInputAction(
          Cesium.ScreenSpaceEventType.RIGHT_CLICK
        ),
        this._drawHandler.destroy(),
        (this._drawHandler = null));

    // 清除悬停和点击事件处理器
    if (this._hoverHandler) {
      this._hoverHandler.removeInputAction(Cesium.ScreenSpaceEventType.MOUSE_MOVE);
      this._hoverHandler.destroy();
      this._hoverHandler = null;
    }

    if (this._clickHandler) {
      this._clickHandler.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_CLICK);
      this._clickHandler.destroy();
      this._clickHandler = null;
    }
  }

  /**
   * 重置所有参数
   * @private
   */
  _resetParams() {
    if (this._dataSource != null) {
      this._dataSource.entities.removeAll();
      this.viewer.dataSources.remove(this._dataSource);
    }
    this._dataSource = null;
    this._tempPositions = [];
    this._mousePos = null;
    this._drawType = null;
    this._lastPointEntity = null;
  }

  /**
   * 清除
   */
  clearAll() {
    this._removeAllEvent();
    this._resetParams();
    
    // 清除右键菜单
    const menuDiv = document.getElementById('drawToolContextMenu');
    if (menuDiv) {
      menuDiv.remove();
    }
  }

  /**
   * 画面
   * @private
   */
  _addPolygon() {
    // 先清除所有实体
    this._dataSource.entities.removeAll();

    // 添加每个顶点的圆点
    for (let i = 0; i < this._tempPositions.length; i++) {
      const pointEntity = this._dataSource.entities.add({
        position: this._tempPositions[i],
        point: {
          color: Cesium.Color.WHITE,
          pixelSize: 10,
          heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
          outlineColor: Cesium.Color.WHITE,
          outlineWidth: 2,
          disableDepthTestDistance: Number.POSITIVE_INFINITY
        }
      });

      // 如果是最后一个点，保存其引用
      if (this._tempPositions.length > 2 && i === this._tempPositions.length - 1) {
        this._lastPointEntity = pointEntity;
      }
    }

    if (this._tempPositions.length == 1) {
      //一个顶点+移动点
      this._dataSource.entities.add({
        polyline: {
          positions: new Cesium.CallbackProperty(() => {
            let c = Array.from(this._tempPositions);
            if (this._mousePos) {
              c.push(this._mousePos);
            }
            return c;
          }, false),
          clampToGround: true, //贴地
          width: 6,
          material: new Cesium.PolylineOutlineMaterialProperty({
            color: Cesium.Color.fromCssColorString('#2d8cf0'),
            outlineWidth: 2,
            outlineColor: Cesium.Color.WHITE
          }),
          depthFailMaterial: new Cesium.PolylineOutlineMaterialProperty({
            color: Cesium.Color.fromCssColorString('#2d8cf0'),
            outlineWidth: 2,
            outlineColor: Cesium.Color.WHITE
          }),
          classificationType: Cesium.ClassificationType.BOTH,
          shadows: Cesium.ShadowMode.DISABLED,
          distanceDisplayCondition: new Cesium.DistanceDisplayCondition(0, 9999999),
          arcType: Cesium.ArcType.GEODESIC // 使用测地线方式绘制
        },
      });
    } else {
      //两个顶点+移动点
      this._dataSource.entities.add({
        polygon: {
          hierarchy: new Cesium.CallbackProperty(() => {
            let poss = Array.from(this._tempPositions);
            if (this._mousePos) {
              poss.push(this._mousePos);
            }
            return new Cesium.PolygonHierarchy(poss);
          }, false),
          extrudedHeightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
          material: Cesium.Color.fromCssColorString('#2d8cf0').withAlpha(0.3),
          clampToGround: true,
        },
        polyline: {
          positions: new Cesium.CallbackProperty(() => {
            let c = Array.from(this._tempPositions);
            if (this._mousePos) {
              c.push(this._mousePos);
              c.push(c[0]); //与第一个点相连
            }
            return c;
          }, false),
          clampToGround: true, //贴地
          width: 6,
          material: new Cesium.PolylineOutlineMaterialProperty({
            color: Cesium.Color.fromCssColorString('#2d8cf0'),
            outlineWidth: 2,
            outlineColor: Cesium.Color.WHITE
          }),
          depthFailMaterial: new Cesium.PolylineOutlineMaterialProperty({
            color: Cesium.Color.fromCssColorString('#2d8cf0'),
            outlineWidth: 2,
            outlineColor: Cesium.Color.WHITE
          }),
          classificationType: Cesium.ClassificationType.BOTH,
          shadows: Cesium.ShadowMode.DISABLED,
          distanceDisplayCondition: new Cesium.DistanceDisplayCondition(0, 9999999),
          arcType: Cesium.ArcType.GEODESIC // 使用测地线方式绘制
        },
      });
    }
  }
}
