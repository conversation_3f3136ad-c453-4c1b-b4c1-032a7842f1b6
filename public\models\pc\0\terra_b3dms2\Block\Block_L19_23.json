{"asset": {"gltfUpAxis": "Z", "version": "0.0"}, "geometricError": 0.15219508111476898, "root": {"boundingVolume": {"box": [-1.4348831176757812, 20.803653717041016, -35.34796142578125, 8.88968276977539, 0.0, 0.0, 0.0, 14.603336334228516, 0.0, 0.0, 0.0, 6.824167251586914]}, "children": [{"boundingVolume": {"box": [-1.4348831176757812, 13.501985549926758, -33.206504821777344, 8.88968276977539, 0.0, 0.0, 0.0, 7.301668167114258, 0.0, 0.0, 0.0, 4.682712554931641]}, "children": [{"boundingVolume": {"box": [-6.6205315589904785, 13.501985549926758, -33.96168518066406, 3.7040343284606934, 0.0, 0.0, 0.0, 7.301668167114258, 0.0, 0.0, 0.0, 3.9256458282470703]}, "children": [{"boundingVolume": {"box": [-6.6205315589904785, 9.851151466369629, -33.94479751586914, 3.7040343284606934, 0.0, 0.0, 0.0, 3.650834083557129, 0.0, 0.0, 0.0, 3.905841827392578]}, "children": [{"boundingVolume": {"box": [-6.6205315589904785, 9.851151466369629, -33.94423294067383, 3.7040343284606934, 0.0, 0.0, 0.0, 3.650834083557129, 0.0, 0.0, 0.0, 3.9052772521972656]}, "content": {"uri": "Block_L23_115.b3dm"}, "geometricError": 0.009473792277276516, "refine": "REPLACE"}], "content": {"uri": "Block_L22_141.b3dm"}, "geometricError": 0.01894296705722809, "refine": "REPLACE"}, {"boundingVolume": {"box": [-6.6205315589904785, 17.152820587158203, -33.96168518066406, 3.7040343284606934, 0.0, 0.0, 0.0, 3.650834083557129, 0.0, 0.0, 0.0, 3.9256458282470703]}, "children": [{"boundingVolume": {"box": [-6.6205315589904785, 17.152820587158203, -33.96168518066406, 3.7040343284606934, 0.0, 0.0, 0.0, 3.650834083557129, 0.0, 0.0, 0.0, 3.9256458282470703]}, "content": {"uri": "Block_L23_114.b3dm"}, "geometricError": 0.008939508348703384, "refine": "REPLACE"}], "content": {"uri": "Block_L22_140.b3dm"}, "geometricError": 0.017942029982805252, "refine": "REPLACE"}], "content": {"uri": "Block_L21_96.b3dm"}, "geometricError": 0.03679655119776726, "refine": "REPLACE"}, {"boundingVolume": {"box": [2.269151210784912, 13.501985549926758, -32.987056732177734, 5.185648441314697, 0.0, 0.0, 0.0, 7.301668167114258, 0.0, 0.0, 0.0, 4.462306976318359]}, "children": [{"boundingVolume": {"box": [2.269151210784912, 13.501985549926758, -32.987056732177734, 5.185648441314697, 0.0, 0.0, 0.0, 7.301668167114258, 0.0, 0.0, 0.0, 4.462306976318359]}, "children": [{"boundingVolume": {"box": [2.269151210784912, 10.459623336791992, -33.7431640625, 5.185648441314697, 0.0, 0.0, 0.0, 4.25930643081665, 0.0, 0.0, 0.0, 3.705930709838867]}, "content": {"uri": "Block_L23_113.b3dm"}, "geometricError": 0.009108562022447586, "refine": "REPLACE"}, {"boundingVolume": {"box": [2.269151210784912, 17.76129150390625, -32.98692321777344, 5.185648441314697, 0.0, 0.0, 0.0, 3.0423617362976074, 0.0, 0.0, 0.0, 4.46217155456543]}, "content": {"uri": "Block_L23_112.b3dm"}, "geometricError": 0.008424016647040844, "refine": "REPLACE"}], "content": {"uri": "Block_L22_139.b3dm"}, "geometricError": 0.017443502321839333, "refine": "REPLACE"}], "content": {"uri": "Block_L21_95.b3dm"}, "geometricError": 0.03494895249605179, "refine": "REPLACE"}], "content": {"uri": "Block_L20_49.b3dm"}, "geometricError": 0.07219166308641434, "refine": "REPLACE"}, {"boundingVolume": {"box": [-5.879724502563477, 28.105321884155273, -36.1531867980957, 4.444841384887695, 0.0, 0.0, 0.0, 7.301668167114258, 0.0, 0.0, 0.0, 6.018939018249512]}, "children": [{"boundingVolume": {"box": [-5.879724502563477, 28.105321884155273, -36.1531867980957, 4.444841384887695, 0.0, 0.0, 0.0, 7.301668167114258, 0.0, 0.0, 0.0, 6.018939018249512]}, "children": [{"boundingVolume": {"box": [-5.879724502563477, 23.84601593017578, -34.67746353149414, 4.444841384887695, 0.0, 0.0, 0.0, 3.0423622131347656, 0.0, 0.0, 0.0, 4.543213844299316]}, "children": [{"boundingVolume": {"box": [-5.879724502563477, 23.84601593017578, -37.70449447631836, 4.444841384887695, 0.0, 0.0, 0.0, 3.0423622131347656, 0.0, 0.0, 0.0, 1.5140495300292969]}, "content": {"uri": "Block_L23_111.b3dm"}, "geometricError": 0.009689045138657093, "refine": "REPLACE"}, {"boundingVolume": {"box": [-4.959426403045654, 23.40106964111328, -33.16234588623047, 3.524543285369873, 0.0, 0.0, 0.0, 2.597414970397949, 0.0, 0.0, 0.0, 3.0280981063842773]}, "content": {"uri": "Block_L23_110.b3dm"}, "geometricError": 0.008794063702225685, "refine": "REPLACE"}], "content": {"uri": "Block_L22_138.b3dm"}, "geometricError": 0.01877621002495289, "refine": "REPLACE"}, {"boundingVolume": {"box": [-5.879724502563477, 31.14768409729004, -39.540042877197266, 4.444841384887695, 0.0, 0.0, 0.0, 4.259305953979492, 0.0, 0.0, 0.0, 2.6287765502929688]}, "children": [{"boundingVolume": {"box": [-5.879724502563477, 31.14768409729004, -39.540042877197266, 4.444841384887695, 0.0, 0.0, 0.0, 4.259305953979492, 0.0, 0.0, 0.0, 2.6287765502929688]}, "content": {"uri": "Block_L23_109.b3dm"}, "geometricError": 0.010420292615890503, "refine": "REPLACE"}], "content": {"uri": "Block_L22_137.b3dm"}, "geometricError": 0.020809371024370193, "refine": "REPLACE"}], "content": {"uri": "Block_L21_94.b3dm"}, "geometricError": 0.03924750164151192, "refine": "REPLACE"}], "content": {"uri": "Block_L20_48.b3dm"}, "geometricError": 0.07851176708936691, "refine": "REPLACE"}, {"boundingVolume": {"box": [3.009958267211914, 28.105321884155273, -35.37877655029297, 4.444841384887695, 0.0, 0.0, 0.0, 7.301668167114258, 0.0, 0.0, 0.0, 6.779722213745117]}, "children": [{"boundingVolume": {"box": [3.009958267211914, 24.454486846923828, -33.85429763793945, 4.444841384887695, 0.0, 0.0, 0.0, 3.650834083557129, 0.0, 0.0, 0.0, 5.254328727722168]}, "children": [{"boundingVolume": {"box": [3.009958267211914, 24.524085998535156, -37.35731506347656, 4.444841384887695, 0.0, 0.0, 0.0, 3.581235885620117, 0.0, 0.0, 0.0, 1.751312255859375]}, "children": [{"boundingVolume": {"box": [0.7875375747680664, 24.524085998535156, -37.35731506347656, 2.2224206924438477, 0.0, 0.0, 0.0, 3.581235885620117, 0.0, 0.0, 0.0, 1.751312255859375]}, "content": {"uri": "Block_L23_108.b3dm"}, "geometricError": 0.009690945036709309, "refine": "REPLACE"}, {"boundingVolume": {"box": [5.232378959655762, 24.755577087402344, -37.293853759765625, 2.2224206924438477, 0.0, 0.0, 0.0, 3.349745750427246, 0.0, 0.0, 0.0, 1.6878490447998047]}, "content": {"uri": "Block_L23_107.b3dm"}, "geometricError": 0.009686136618256569, "refine": "REPLACE"}], "content": {"uri": "Block_L22_136.b3dm"}, "geometricError": 0.019377846270799637, "refine": "REPLACE"}, {"boundingVolume": {"box": [3.009958267211914, 22.953571319580078, -32.10337829589844, 4.444841384887695, 0.0, 0.0, 0.0, 2.149916648864746, 0.0, 0.0, 0.0, 3.5026235580444336]}, "children": [{"boundingVolume": {"box": [3.009958267211914, 22.953571319580078, -32.10337829589844, 4.444841384887695, 0.0, 0.0, 0.0, 2.149916648864746, 0.0, 0.0, 0.0, 3.5026235580444336]}, "content": {"uri": "Block_L23_106.b3dm"}, "geometricError": 0.00829141866415739, "refine": "REPLACE"}], "content": {"uri": "Block_L22_135.b3dm"}, "geometricError": 0.016620341688394547, "refine": "REPLACE"}], "content": {"uri": "Block_L21_93.b3dm"}, "geometricError": 0.03722216933965683, "refine": "REPLACE"}, {"boundingVolume": {"box": [3.009958267211914, 31.75615692138672, -39.589256286621094, 4.444841384887695, 0.0, 0.0, 0.0, 3.650834083557129, 0.0, 0.0, 0.0, 2.5692405700683594]}, "children": [{"boundingVolume": {"box": [3.009958267211914, 31.75615692138672, -39.589881896972656, 4.444841384887695, 0.0, 0.0, 0.0, 3.650834083557129, 0.0, 0.0, 0.0, 2.568540573120117]}, "children": [{"boundingVolume": {"box": [0.7875375747680664, 31.75615692138672, -39.6978759765625, 2.2224206924438477, 0.0, 0.0, 0.0, 3.650834083557129, 0.0, 0.0, 0.0, 2.4605484008789062]}, "content": {"uri": "Block_L23_105.b3dm"}, "geometricError": 0.010328738018870354, "refine": "REPLACE"}, {"boundingVolume": {"box": [5.232378959655762, 31.75615692138672, -38.97218322753906, 2.2224206924438477, 0.0, 0.0, 0.0, 3.650834083557129, 0.0, 0.0, 0.0, 1.9508399963378906]}, "content": {"uri": "Block_L23_104.b3dm"}, "geometricError": 0.010259052738547325, "refine": "REPLACE"}], "content": {"uri": "Block_L22_134.b3dm"}, "geometricError": 0.02057672291994095, "refine": "REPLACE"}], "content": {"uri": "Block_L21_92.b3dm"}, "geometricError": 0.041162971407175064, "refine": "REPLACE"}], "content": {"uri": "Block_L20_47.b3dm"}, "geometricError": 0.07785218954086304, "refine": "REPLACE"}], "content": {"uri": "Block_L19_23.b3dm"}, "geometricError": 0.15219508111476898, "refine": "REPLACE"}}