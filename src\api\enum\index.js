export const ERouterName = {
  ELEMENT: 'element',
  PROJECT: 'project',
  HOME: 'home',
  TSA: 'tsa/index',
  LAYER: 'layer/index',
  MEDIA: 'media/index',
  WAYLINE: 'wayline/index',
  LIVESTREAM: 'livestream/index',
  LIVING: 'living',
  WORKSPACE: 'workspace',
  workspace: '工作区',
  MEMBERS: 'members/index', // members
  members: '成员',
  DEVICES: 'devices', // devices
  devices: '设备',
  TASK: 'task/index',
  TASK_NAME:'任务计划库',
  CREATE_PLAN: 'create-plan',
  SELECT_PLAN: 'select-plan',
  FIRMWARES: 'firmwares',
  firmwares: '固件',
  FLIGHT_AREA: 'flight-area/index',
  PILOT: 'pilot-login',
  PILOT_HOME: 'pilot-home',
  PILOT_MEDIA: 'pilot-media',
  PILOT_LIVESHARE: 'pilot-liveshare',
  PILOT_BIND: 'pilot-bind',
};
export const EBizCode = {
  GatewayOsd: 'gateway_osd',
  DeviceOsd: 'device_osd',
  DockOsd: 'dock_osd',
  MapElementCreate: 'map_element_create',
  MapElementUpdate: 'map_element_update',
  MapElementDelete: 'map_element_delete',
  DeviceOnline: 'device_online',
  DeviceOffline: 'device_offline',
  DeviceHms: 'device_hms',

  // 机场任务
  FlightTaskProgress: 'flighttask_progress', // 机场任务执行进度
  FlightTaskMediaProgress: 'file_upload_callback', // 机场任务媒体上传进度
  FlightTaskMediaHighestPriority: 'highest_priority_upload_flighttask_media', // 机场任务媒体优先级上报

  // 设备指令
  DeviceReboot: 'device_reboot', // 机场重启
  DroneOpen: 'drone_open', // 飞行器开机
  DroneClose: 'drone_close', // 飞行器关机
  AirConditionerModeSwitch: 'air_conditioner_mode_switch', // 空调模式切换
  DeviceFormat: 'device_format', // 机场数据格式化
  DroneFormat: 'drone_format', // 飞行器数据格式化
  CoverOpen: 'cover_open', // 打开舱盖
  CoverClose: 'cover_close', // 关闭舱盖
  PutterOpen: 'putter_open', // 推杆展开
  PutterClose: 'putter_close', // 推杆闭合
  ChargeOpen: 'charge_open', // 打开充电
  ChargeClose: 'charge_close', // 关闭充电

  // 设备升级
  DeviceUpgrade: 'ota_progress', // 设备升级

  // 设备日志
  DeviceLogUploadProgress: 'fileupload_progress', // 设备日志上传

  // 飞行指令消息
  ControlSourceChange: 'control_source_change', // 控制权更新
  FlyToPointProgress: 'fly_to_point_progress', // 飞向目标点
  TakeoffToPointProgress: 'takeoff_to_point_progress', // 一键起飞
  JoystickInvalidNotify: 'joystick_invalid_notify', // 设备端退出drc模式
  DrcStatusNotify: 'drc_status_notify', // 飞行控制模式状态

  // custom flight area
  FlightAreasSyncProgress: 'flight_areas_sync_progress',
  FlightAreasDroneLocation: 'flight_areas_drone_location',
  FlightAreasUpdate: 'flight_areas_update',
};

export const ELocalStorageKey = {
  Username: 'username',
  WorkspaceId: 'workspace_id',
  Token: 'HzTech-Auth',
  PlatformName: 'platform_name',
  WorkspaceName: 'workspace_name',
  WorkspaceDesc: 'workspace_desc',
  Flag: 'flag',
  UserId: 'user_id',
  Device: 'device',
  GatewayOnline: 'gateway_online',
};
export const EDownloadOwner = {
  Mine: 0,
  Others: 1,
  Unknown: -1,
};
export const EComponentName = {
  Thing: 'thing',
  Liveshare: 'liveshare',
  Api: 'api',
  Ws: 'ws',
  Map: 'map',
  Tsa: 'tsa',
  Media: 'media',
  Mission: 'mission',
};
export const EDeviceTypeName = {
  Aircraft: 0,
  Gateway: 2,
  Dock: 3,
};

export const OnlineDevice = {
  model: '',
  callsign: '',
  sn: '',
  mode: '',
  gateway: {
    model: '',
    callsign: '',
    sn: '',
    domain: '',
  },
  payload: [
    {
      index: '',
      model: '',
      control_source: '',
      payload_sn: '',
      payload_index: '',
      payload_name: '',
    },
  ],
};
export const EHmsLevel = {
  NOTICE: '',
  CAUTION: '',
  WARN: '',
};

export const EStatusValue = {
  CONNECTED: '在线',
  DISCONNECT: '断开',
  LIVING: 'Living',
};

export const EPhotoType = {
  Original: 0,
  Preview: 1,
  Unknown: -1,
};

export const ELiveStatusValue = {
  DISCONNECT: 'DISCONNECT',
  CONNECTED: 'CONNECTED',
  LIVING: 'LIVING',
};
export const EVideoPublishType = {
  VideoOnDemand: 'video-on-demand',
  VideoByManual: 'video-by-manual',
  VideoDemandAuxManual: 'video-demand-aux-manual',
};

export const EDockModeCode = {
  Disconnected: -1,
  IdleL: 0,
  Debugging: 1,
  Remote_Debugging: 2,
  Upgrading: 4,
  Working: 4,
};
export const EModeCode = {
  Standby: 0,
  Preparing: 1,
  Ready: 2,
  Manual: 3,
  Automatic: 4,
  Waypoint: 5,
  Panoramic: 6,
  Active_Track: 7,
  ADS_B: 8,
  Return_To_Home: 9,
  Landing: 10,
  Forced_Landing: 11,
  Three_Blades_Landing: 12,
  Upgrading: 13,
  Disconnected: 14,
  APAS: 15,
  虚拟摇杆状态: 16,
  指令飞行: 17,
  空中RTK收敛模式: 18,
  机场选址中: 19
};
// export const EModeCode = {
//   待机:0,
//   起飞准备:1,
//   起飞准备完毕:2,
//   手动飞行:3,
//   自动起飞:4,
//   航线飞行:5,
//   全景拍照:6,
//   智能跟随:7,
//   ADS_B躲避:8,
//   自动返航:9,
//   自动降落:10,
//   强制降落:11,
//   三桨叶降落:12,
//   升级中: 13,
//   未连接:14,
//   APAS: 15,
//   虚拟摇杆状态: 16,
//   指令飞行: 17,
//   空中RTK收敛模式: 18,
//   机场选址中: 19
// };

export const   NetworkStateTypeEnum= {
  FOUR_G : 1,
  ETHERNET : 2,
}
export const  RainfallEnum =[
  '无',
  '小雨',
  '中雨',
  '大雨',
]
export const DrcStateEnum ={
  DISCONNECT : 0,
  CONNECTING : 1,
  CONNECTED :2
}
export const FourGLinkStateEnum ={
  CLOSE : 0, // 断开
  OPEN : 1, // 连接
}
//  Sdr链路连接状态
export const  SdrLinkStateEnum ={
  CLOSE : 0, // 断开
  OPEN : 1, // 连接
}
// 机场的图传链路模式
export const LinkWorkModeEnum ={
  SDR : 0, // sdr模式
  FourG_FUSION_MODE : 1, // 4G融合模式
}
export const DroneBatteryStateEnum ={
  NoMaintenanceRequired : 0, // 0-无需保养
  MaintenanceRequired   :1, // 1-待保养
  MaintenanceInProgress : 2, // 2-正在保养
}
export const NetworkStateQualityEnum ={
  NO_SIGNAL : 0,
  BAD : 1,
  POOR : 2,
  FAIR : 3,
  GOOD : 4,
  EXCELLENT: 5,
}

// 机场的提示信息
export const dockTitle = {
  acc_time:'累计运行时间',
  activation_time:'激活时间',
  network_state:'网速',
  job_number:'机场执行任务的总次数',
  remain_upload:'媒体文件上传',
  total:'机场可用存储(KB)',
  used:'机场已使用存储(KB)',
  wind_speed:'风速',
  rainfall:'降雨量',
  environment_temperature:'环境温度',
  dock_temperature:'机场温度',
  dock_humidity:'机场湿度',
  working_voltage:'工作电压',
  working_current:'工作电流',
  drone_in_dock:'无人机在机场'
}
//飞行器提示信息
export const deviceTitle = {
  drone_capacity_percent:'无人机电池电量',
  rc_capacity_percent:'遥控器电池电量',
  capacity_percent:'飞行器电池电量',
  rtk_fixed:'高精度定位到厘米级(红色代表开启)',
  rtk:'搜索的卫星数量',
  total:'飞行器可用存储(KB)',
  used:'飞行器已使用存储(KB)',
  gear:'飞行档位,F代表功能挡，P/N代表普通挡,S代表运动挡',
  Altitude:'海拔高度',
  takeoff:'起飞高度',
  distance:'距离原点距离',
  horizontal_speed:'水平速度',
  vertical_speed:'垂直速度',
  wind_speed:'风速',
  upward_quality:'上行带宽',
  downward_quality:'下行带宽',
  hd:'遥控器信号强度',
  camera_mode:'相机模式',
}

//空调状态
export const enumerateAirStatus = {
  0: '空闲模式(无制冷、制热、除湿等)',
  1: '制冷模式',
  2: '制热模式',
  3: '除湿模式',
  4: '制冷退出模式',
  5: "制热退出模式",
  6: "除湿退出模式",
  7: "制冷准备模式",
  8: "制热准备模式",
  9: "除湿准备模式",
};

//机场状态
export const enumerateStatus = {
  0: '空闲中',
  1: '现场调试',
  2: '远程调试',
  3: '固件升级中',
  4: '作业中',
};

//舱盖状态
export const enumerateCoverStatus = {
  0: '关闭',
  1: '打开',
  2: '半开',
  3: '舱盖状态异常',
};

//飞行器状态
export const enumerateaircraftStatus = {
  null: "离线",
  0: "待机",
  1: "起飞准备",
  2: "起飞准备完毕",
  3: "手动飞行",
  4: "自动起飞",
  5: "航线飞行",
  6: "全景拍照",
  7: "智能跟随",
  8: "ADS-B 躲避",
  9: "自动返航",
  10: "自动降落",
  11: "强制降落",
  12: "三桨叶降落",
  13: "升级中",
  14: "未连接",
  15: "APAS",
  16: "虚拟摇杆状态",
  17: "指令飞行",
  18: "空中 RTK 收敛模式",
  19: "机场选址中",
}