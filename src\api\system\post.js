import request from '@/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/hztech-system/post/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    },
  });
};

export const getPostList = tenantId => {
  return request({
    url: '/hztech-system/post/select',
    method: 'get',
    params: {
      tenantId,
    },
  });
};

export const getDetail = id => {
  return request({
    url: '/hztech-system/post/detail',
    method: 'get',
    params: {
      id,
    },
  });
};

export const remove = ids => {
  return request({
    url: '/hztech-system/post/remove',
    method: 'post',
    params: {
      ids,
    },
  });
};

export const add = row => {
  return request({
    url: '/hztech-system/post/submit',
    method: 'post',
    data: row,
  });
};

export const update = row => {
  return request({
    url: '/hztech-system/post/submit',
    method: 'post',
    data: row,
  });
};
