import mitt, {Emitter} from 'mitt'

type Events = {
  deviceUpgrade: any; // 设备升级
  deviceLogUploadProgress: any // 设备日志上传
  flightTaskWs: any // 机场任务消息
  droneControlWs: any // 飞行指令信息
  droneControlMqttInfo: any // drc 链路通知
  flightAreasDroneLocationWs: any
  flightAreasSyncProgressWs: any
  flightAreasUpdateWs: any
  flightList: any // 自定义飞行列表
  updateplan: any
  lineClose: any // 航线库-新增&编辑时关闭事件
  lineClick: any // 标注线点击事件
  updateMap:any
  // 视频相关事件
  cockpit_video_control: any
  takeoffToPointPopoverData: any
  temperatureSet: any
  thermometricPopoverData: any
  setLensSelected: any
  rectangle_selection: any
  toggle_rectangle_drawing: any
  toggleDrawRectMode: boolean // 切换框选模式
  // 驾驶舱控制相关
  cockpit_control: string
  droneControlSource: string
  // 算法状态变更事件
  algoStatusChanged: any
};

const emitter: Emitter<Events> = mitt<Events>()

export default emitter
