<template>
    <div>
        <!-- 组件模板内容 -->
        <a-tooltip :title="props.title">
            <span class="form-label">{{ props.content }}：</span>
            <InfoCircleOutlined v-if="props.title" class="mr5"/>
        </a-tooltip>
    </div>
</template>

<script lang="ts" setup>
import { defineProps } from 'vue'
import { InfoCircleOutlined } from '@ant-design/icons-vue'
const props = defineProps({
    title: String,
    content: String
})
</script>
<style lang="scss" scoped>
.form-label{
    margin-right: 5px;
}
.mr5{
    margin-right: 5px;
}
</style>