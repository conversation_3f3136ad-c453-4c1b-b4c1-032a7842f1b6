import { createApp} from 'vue';
import router from '@/router';

let app = null;
export function createInstance(App) {
  app = createApp(App);

  // 注意：在 JavaScript 中，你不能直接扩展 Vue 组件的实例属性
  // 你需要在组件中通过其他方式（如 mixin、provide/inject 或在组件内部定义）来使用这些属性
  // 或者，你可以直接在全局属性中设置它们，但这种方式不是类型安全的
  app.config.globalProperties.$aMap = null; // Map类，你可以在这里初始化或赋予默认值
  app.config.globalProperties.$map = null; // 地图对象
  app.config.globalProperties.$mouseTool = null;
  app.config.globalProperties.$root = router;

  return app;
}

export function getRoot() {
  return app.config.globalProperties.root;
}

export function getApp() {
  return app;
}