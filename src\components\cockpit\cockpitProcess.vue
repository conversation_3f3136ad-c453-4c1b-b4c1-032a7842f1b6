<template>
    <div class="cockpit-process">
        <a-progress v-if="![0, 100].includes(progressLoading)" :percent="progressLoading || 0" />
        <div>
            <div class="font-bold text-hidden" :style="dockInfo != EDockModeCode.未连接
                ? 'color: #00ee8b'
                : 'color: red;'
                ">
                <a-tooltip title="机场">
                    <RobotOutlined />
                </a-tooltip>
                {{ EDockModeCode[dockInfo] }}

            </div>
            <div class="font-bold text-hidden" :style="deviceInfo  != EModeCode.未连接
                ? 'color: #00ee8b'
                : 'color: red;'
                ">
                <a-tooltip title="无人机">
                    <RocketOutlined />
                </a-tooltip>
                {{ EModeCode[deviceInfo] }}
            </div>
        </div>
    </div>
</template>

<script setup>
import { onMounted, reactive } from 'vue'
import EventBus from '@/event-bus';
import { EBizCode } from '@/types'
import { TaskProgressStatus } from '@/types/task'
import { EModeCode, EDockModeCode } from '@/types/device'
import { useAttrs } from 'vue';
import {
    RocketOutlined,
    RobotOutlined,
} from '@ant-design/icons-vue';
const props = defineProps({
    deviceInfoAttrs: {
        type: Object,
        default: () => {
            return null
        }
    },
    dockInfoAttrs: {
        type: Object,
        default: () => {
            return null
        }
    }
})
const jcState = ref({})
// const attrs = useAttrs();
// const sn = attrs.cockpit_dock?.sn;
let deviceInfo = ref({})
let dockInfo = ref({})
watch(() => props.deviceInfoAttrs, (newVal) => {
    if (newVal) {
        newVal.mode_code = newVal.mode_code?.toString() || null
        deviceInfo.value = newVal.mode_code;
        if (deviceInfo.value == EModeCode.手动飞行 || deviceInfo.value == EModeCode.虚拟摇杆状态) {
            EventBus.emit('cockpit_control', '手动模式控制中');
        }
    }else {
        deviceInfo.value = EModeCode.未连接;
    }
}, { deep: true, immediate: true })
watch(() => props.dockInfoAttrs, (newVal) => {

    if (newVal) {
        dockInfo.value = newVal.basic_osd?.mode_code;
    }else {
        dockInfo.value = EDockModeCode.未连接
    }

}, { deep: true, immediate: true })


onMounted(() => {
    // 监听执行任务进度
    EventBus.on('flightTaskWs', handleTaskWsEvent)
});

const handleTaskWsEvent = (payload) => {
    if (!payload) {
        return
    }
    // console.log('驾驶舱进度条：', payload);
    switch (payload.biz_code) {
        case EBizCode.FlightTaskProgress: {
            onTaskProgressWs(payload.data)
            break
        }
    }
}
let progressLoading = ref(0)
const onTaskProgressWs = (data) => {
    const { bid, output } = data
    if (output) {
        const { status, progress } = output || {}
        if (status) {
            // 执行中，更新进度
            if (status === TaskProgressStatus.Sent || status === TaskProgressStatus.inProgress) {
                progressLoading.value = progress?.percent || 0
            } else if ([TaskProgressStatus.Rejected, TaskProgressStatus.Canceled, TaskProgressStatus.Timeout, TaskProgressStatus.Failed, TaskProgressStatus.OK].includes(status)) {
                progressLoading.value = 100
            }
        }
    }
}
onUnmounted(() => {
    EventBus.off('flightTaskWs')
})
</script>
<style lang="scss" scoped>
.cockpit-process {
    position: absolute;
    background: rgba(60, 60, 60, 0.38);
    top: 50px;
    right: 10px;
    border-radius: 0.25rem;
    color: #fff;
    width: 10vw;

    ::v-deep(.ant-progress) {
        .ant-progress-text {
            color: #ffffff;
        }
    }
}
</style>