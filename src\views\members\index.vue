<template>
  <basic-container>
    <avue-crud :option="option" :data="data" v-model:page="page" ref="crud" v-model="form" @row-update="rowUpdate"
      @current-change="currentChange" @size-change="sizeChange" @on-load="onLoad" :before-open="beforeOpen">
      <template #menu-left>
        <el-button type="primary" @click="synchronization" v-permission>同 步</el-button>
      </template>
    </avue-crud>
  </basic-container>
</template>

<script>
import { membersList, updateUser, synchronization } from '@/api/members/index';
import { encrypt, decrypt } from '@/utils/sm2';

export default {
  data() {
    return {
      form: {},
      page: {
        currentPage: 1,
        pageSize: 10
      },
      option: {
        tip: false,
        addBtn: false,
        delBtn: false,
        editBtn: true,
        simplePage: true,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        viewBtn: false,
        dialogWidth: 700,
        dialogClickModal: false,
        labelWidth: 160,
        height: 'auto',
        calcHeight: 32,
        index: true,
        excelBtn: true,
        column: [
          {
            label: '账户',
            prop: 'username',
            span: 24,
            disabled: true
          },
          {
            label: '用户类型',
            prop: 'user_type',
            type: 'select',
            dicUrl: '/hztech-system/dict/dictionary?code=userType',
            dataType: 'string',
            props: {
              label: 'dictValue',
              value: 'dictKey',
            },
            rules: [
              { required: true, message: '请选择用户类型', trigger: 'blur' },
            ],
            search: false,
            // disabled: true,
            span: 24,
          },
          // {
          //   label: '工作空间名称',
          //   prop: 'workspace_name',
          //   span: 24,
          //   disabled: true
          // },
          {
            label: 'Mqtt 用户名',
            prop: 'mqtt_username',
            span: 24,
            rules: [
              { required: true, message: '请输入用户名', trigger: 'blur' },
              { min: 6, max: 40, message: '长度在 6 到 40 个字符', trigger: 'blur' },
              { pattern: /^[a-zA-Z0-9]+$/, message: '只能包含数字和字母', trigger: 'blur' }
            ]
          },
          {
            label: 'Mqtt 密码',
            prop: 'mqtt_password',
            type: 'password',
            span: 24,
            overHidden: true,
            rules: [
              { required: true, message: '请输入密码', trigger: 'blur' },
              { pattern: /^(?=.*[a-zA-Z])(?=.*\d)(?=.*[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?])[a-zA-Z\d!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]{8,40}$/, message: '密码长度8-40个字符，必须包含字母、数字和特殊字符', trigger: 'blur' }
            ]
          },
          {
            label: '创建时间',
            prop: 'create_time',
            span: 24,
            disabled: true
          }
        ]
      },
      data: [],
    };
  },
  mounted() {
    const userInfo = JSON.parse(localStorage.getItem('hztech-userInfo') || '{}');
    if (userInfo?.content?.role_name === 'ViewUers') {
      this.option.editBtn = false;
    }
  },
  methods: {
    //同步
    synchronization() {
      const userInfo = JSON.parse(localStorage.getItem('hztech-userInfo') || '{}');
      const tenantId = userInfo?.content?.tenant_id;
      synchronization(tenantId).then((res) => {
        if (res.data.code == 0) {
          this.$message({
            type: "success",
            message: '操作成功'
          });
          this.memberList();
        }
      }, error => {
        console.log(error);
      });
    },
    currentChange(currentPage) {
      this.page.page = currentPage;
    },
    sizeChange(pageSize) {
      this.page.page_size = pageSize;
    },
    beforeOpen(done) {
      console.log(this.form.mqtt_password + '----------->')
      // 检查密码是否存在且为加密格式
      if (this.form.mqtt_password && this.form.mqtt_password.length > 0) {
        try {
          const decrypted = decrypt(this.form.mqtt_password.slice(2));
          if (decrypted) {
            this.form.mqtt_password = decrypted;
          } else {
            console.error('解密结果为空，可能密码格式不正确或密钥不匹配');
          }
        } catch (error) {
          console.error('解密过程出错:', error);
        }
      }
      console.log(this.form.mqtt_password + '----------->')
      done();
    },
    //编辑回调方法
    rowUpdate(row, index, done, loading) {
      if (row.user_type === 'Web') {
        row.user_type = '1';
      } else if (row.user_type === 'Pilot') {
        row.user_type = '2';
      }
      let parpams = {
        create_time: row.create_time,
        mqtt_username: row.mqtt_username,
        mqtt_password: `04${encrypt(row.mqtt_password)}`,
        user_id: row.user_id,
        user_type: row.user_type,
        username: row.username,
        workspace_name: row.workspace_name,
      }
      
      updateUser(row.user_id, parpams).then(() => {
        this.memberList();
        this.$message({
          type: "success",
          message: "操作成功!"
        });
        done();
      }, error => {
        loading();
        console.log(error);
      });
    },
    memberList() {
      membersList(this.page.currentPage, this.page.pageSize).then(res => {
        if (res.data.code == 0) {
          this.data = res.data.data.list
          this.page.total = res.data.data.pagination.total
        }
      });
    },
    onLoad(page, params = {}) {
      let data = JSON.stringify(this.$store.state.user.detail)
      console.log(data + '----------->')
      this.memberList();
    }
  }
}
</script>