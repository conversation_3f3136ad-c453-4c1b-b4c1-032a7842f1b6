{"asset": {"gltfUpAxis": "Z", "version": "0.0"}, "geometricError": 0.15455754101276398, "root": {"boundingVolume": {"box": [27.774072647094727, -4.490922927856445, -35.72770690917969, 7.619726181030273, 0.0, 0.0, 0.0, 13.618413925170898, 0.0, 0.0, 0.0, 4.638055801391602]}, "children": [{"boundingVolume": {"box": [23.32923126220703, -12.43499755859375, -35.39025115966797, 3.1748857498168945, 0.0, 0.0, 0.0, 5.6743388175964355, 0.0, 0.0, 0.0, 4.300600051879883]}, "children": [{"boundingVolume": {"box": [23.32923126220703, -15.272167205810547, -35.37112808227539, 3.1748857498168945, 0.0, 0.0, 0.0, 2.837169647216797, 0.0, 0.0, 0.0, 4.281475067138672]}, "children": [{"boundingVolume": {"box": [23.32923126220703, -15.272167205810547, -37.511863708496094, 3.1748857498168945, 0.0, 0.0, 0.0, 2.837169647216797, 0.0, 0.0, 0.0, 2.140737533569336]}, "children": [{"boundingVolume": {"box": [21.741788864135742, -15.272167205810547, -37.01127624511719, 1.587442398071289, 0.0, 0.0, 0.0, 2.837169647216797, 0.0, 0.0, 0.0, 1.6401500701904297]}, "content": {"uri": "Block_L23_65.b3dm"}, "geometricError": 0.009358066134154797, "refine": "REPLACE"}, {"boundingVolume": {"box": [24.916675567626953, -15.272167205810547, -37.511863708496094, 1.5874433517456055, 0.0, 0.0, 0.0, 2.837169647216797, 0.0, 0.0, 0.0, 2.140737533569336]}, "content": {"uri": "Block_L23_64.b3dm"}, "geometricError": 0.009756787680089474, "refine": "REPLACE"}], "content": {"uri": "Block_L22_107.b3dm"}, "geometricError": 0.0190875343978405, "refine": "REPLACE"}, {"boundingVolume": {"box": [23.32923126220703, -15.761514663696289, -33.23039245605469, 3.1748857498168945, 0.0, 0.0, 0.0, 2.3478221893310547, 0.0, 0.0, 0.0, 2.140737533569336]}, "children": [{"boundingVolume": {"box": [23.32923126220703, -15.761514663696289, -33.23039245605469, 3.1748857498168945, 0.0, 0.0, 0.0, 2.3478221893310547, 0.0, 0.0, 0.0, 2.140737533569336]}, "content": {"uri": "Block_L23_63.b3dm"}, "geometricError": 0.008742306381464005, "refine": "REPLACE"}], "content": {"uri": "Block_L22_106.b3dm"}, "geometricError": 0.01748676598072052, "refine": "REPLACE"}], "content": {"uri": "Block_L21_76.b3dm"}, "geometricError": 0.03734157606959343, "refine": "REPLACE"}, {"boundingVolume": {"box": [23.32923126220703, -9.597827911376953, -36.56175994873047, 3.1748857498168945, 0.0, 0.0, 0.0, 2.8371691703796387, 0.0, 0.0, 0.0, 3.1290931701660156]}, "children": [{"boundingVolume": {"box": [23.32923126220703, -9.597827911376953, -36.56175994873047, 3.1748857498168945, 0.0, 0.0, 0.0, 2.8371691703796387, 0.0, 0.0, 0.0, 3.1290931701660156]}, "children": [{"boundingVolume": {"box": [21.477214813232422, -9.597827911376953, -36.02616882324219, 1.3228693008422852, 0.0, 0.0, 0.0, 2.8371691703796387, 0.0, 0.0, 0.0, 2.5935020446777344]}, "content": {"uri": "Block_L23_62.b3dm"}, "geometricError": 0.009336717426776886, "refine": "REPLACE"}, {"boundingVolume": {"box": [24.652101516723633, -9.597827911376953, -37.35017013549805, 1.8520164489746094, 0.0, 0.0, 0.0, 2.8371691703796387, 0.0, 0.0, 0.0, 2.3406829833984375]}, "content": {"uri": "Block_L23_61.b3dm"}, "geometricError": 0.00994826015084982, "refine": "REPLACE"}], "content": {"uri": "Block_L22_105.b3dm"}, "geometricError": 0.01925683580338955, "refine": "REPLACE"}], "content": {"uri": "Block_L21_75.b3dm"}, "geometricError": 0.03851762041449547, "refine": "REPLACE"}], "content": {"uri": "Block_L20_40.b3dm"}, "geometricError": 0.07565280050039291, "refine": "REPLACE"}, {"boundingVolume": {"box": [30.948959350585938, -12.43499755859375, -35.69184112548828, 4.444840431213379, 0.0, 0.0, 0.0, 5.6743388175964355, 0.0, 0.0, 0.0, 4.5851945877075195]}, "children": [{"boundingVolume": {"box": [30.948959350585938, -15.272167205810547, -35.71611404418945, 4.444840431213379, 0.0, 0.0, 0.0, 2.837169647216797, 0.0, 0.0, 0.0, 4.548365592956543]}, "children": [{"boundingVolume": {"box": [28.726539611816406, -15.272167205810547, -38.48606491088867, 2.2224206924438477, 0.0, 0.0, 0.0, 2.837169647216797, 0.0, 0.0, 0.0, 1.2191009521484375]}, "children": [{"boundingVolume": {"box": [28.726539611816406, -15.272167205810547, -38.48606491088867, 2.2224206924438477, 0.0, 0.0, 0.0, 2.837169647216797, 0.0, 0.0, 0.0, 1.2191009521484375]}, "content": {"uri": "Block_L23_60.b3dm"}, "geometricError": 0.009978007525205612, "refine": "REPLACE"}], "content": {"uri": "Block_L22_104.b3dm"}, "geometricError": 0.01994839310646057, "refine": "REPLACE"}, {"boundingVolume": {"box": [33.17137908935547, -15.272167205810547, -38.778629302978516, 2.2224197387695312, 0.0, 0.0, 0.0, 2.837169647216797, 0.0, 0.0, 0.0, 1.4684638977050781]}, "children": [{"boundingVolume": {"box": [33.17137908935547, -15.272167205810547, -38.778629302978516, 2.2224197387695312, 0.0, 0.0, 0.0, 2.837169647216797, 0.0, 0.0, 0.0, 1.4684638977050781]}, "content": {"uri": "Block_L23_59.b3dm"}, "geometricError": 0.010157318785786629, "refine": "REPLACE"}], "content": {"uri": "Block_L22_103.b3dm"}, "geometricError": 0.02031826041638851, "refine": "REPLACE"}, {"boundingVolume": {"box": [27.39718246459961, -15.826860427856445, -34.2539176940918, 0.8930644989013672, 0.0, 0.0, 0.0, 2.2824764251708984, 0.0, 0.0, 0.0, 3.013045310974121]}, "children": [{"boundingVolume": {"box": [27.377029418945312, -15.826860427856445, -34.286834716796875, 0.8729124069213867, 0.0, 0.0, 0.0, 2.2824764251708984, 0.0, 0.0, 0.0, 2.980128288269043]}, "content": {"uri": "Block_L23_58.b3dm"}, "geometricError": 0.00869200099259615, "refine": "REPLACE"}], "content": {"uri": "Block_L22_102.b3dm"}, "geometricError": 0.017378460615873337, "refine": "REPLACE"}], "content": {"uri": "Block_L21_74.b3dm"}, "geometricError": 0.03997310250997543, "refine": "REPLACE"}, {"boundingVolume": {"box": [30.948959350585938, -9.597827911376953, -38.19584655761719, 4.444840431213379, 0.0, 0.0, 0.0, 2.8371691703796387, 0.0, 0.0, 0.0, 1.6845741271972656]}, "children": [{"boundingVolume": {"box": [30.948959350585938, -9.597827911376953, -38.19794464111328, 4.444840431213379, 0.0, 0.0, 0.0, 2.8371691703796387, 0.0, 0.0, 0.0, 1.682474136352539]}, "children": [{"boundingVolume": {"box": [28.726539611816406, -9.597827911376953, -38.526336669921875, 2.2224206924438477, 0.0, 0.0, 0.0, 2.8371691703796387, 0.0, 0.0, 0.0, 1.1290264129638672]}, "content": {"uri": "Block_L23_57.b3dm"}, "geometricError": 0.010150409303605556, "refine": "REPLACE"}, {"boundingVolume": {"box": [33.17137908935547, -9.597827911376953, -38.198394775390625, 2.2224197387695312, 0.0, 0.0, 0.0, 2.8371691703796387, 0.0, 0.0, 0.0, 1.6820259094238281]}, "content": {"uri": "Block_L23_56.b3dm"}, "geometricError": 0.010159251280128956, "refine": "REPLACE"}], "content": {"uri": "Block_L22_101.b3dm"}, "geometricError": 0.02030678279697895, "refine": "REPLACE"}], "content": {"uri": "Block_L21_73.b3dm"}, "geometricError": 0.04061922803521156, "refine": "REPLACE"}], "content": {"uri": "Block_L20_39.b3dm"}, "geometricError": 0.08046204596757889, "refine": "REPLACE"}, {"boundingVolume": {"box": [27.774072647094727, 1.1834158897399902, -35.91603088378906, 7.619726181030273, 0.0, 0.0, 0.0, 7.944075107574463, 0.0, 0.0, 0.0, 4.430144309997559]}, "children": [{"boundingVolume": {"box": [27.774072647094727, -3.4506280422210693, -37.16569137573242, 7.619726181030273, 0.0, 0.0, 0.0, 3.3100311756134033, 0.0, 0.0, 0.0, 3.1787452697753906]}, "children": [{"boundingVolume": {"box": [23.964210510253906, -3.4506280422210693, -37.233009338378906, 3.8098630905151367, 0.0, 0.0, 0.0, 3.3100311756134033, 0.0, 0.0, 0.0, 2.399515151977539]}, "children": [{"boundingVolume": {"box": [23.964210510253906, -3.4506280422210693, -37.233009338378906, 3.8098630905151367, 0.0, 0.0, 0.0, 3.3100311756134033, 0.0, 0.0, 0.0, 2.399515151977539]}, "content": {"uri": "Block_L23_55.b3dm"}, "geometricError": 0.009541411884129047, "refine": "REPLACE"}], "content": {"uri": "Block_L22_100.b3dm"}, "geometricError": 0.019075874239206314, "refine": "REPLACE"}, {"boundingVolume": {"box": [31.583934783935547, -3.4506280422210693, -37.16529846191406, 3.8098630905151367, 0.0, 0.0, 0.0, 3.3100311756134033, 0.0, 0.0, 0.0, 3.1783523559570312]}, "children": [{"boundingVolume": {"box": [31.583934783935547, -3.4506280422210693, -37.1640510559082, 3.8098630905151367, 0.0, 0.0, 0.0, 3.3100311756134033, 0.0, 0.0, 0.0, 3.177104949951172]}, "content": {"uri": "Block_L23_54.b3dm"}, "geometricError": 0.009923577308654785, "refine": "REPLACE"}], "content": {"uri": "Block_L22_99.b3dm"}, "geometricError": 0.0198438111692667, "refine": "REPLACE"}], "content": {"uri": "Block_L21_72.b3dm"}, "geometricError": 0.03901602327823639, "refine": "REPLACE"}, {"boundingVolume": {"box": [27.774072647094727, 4.493447303771973, -35.52366256713867, 7.619726181030273, 0.0, 0.0, 0.0, 4.6340436935424805, 0.0, 0.0, 0.0, 4.037774085998535]}, "children": [{"boundingVolume": {"box": [22.694255828857422, 4.493447303771973, -37.36479949951172, 2.5399093627929688, 0.0, 0.0, 0.0, 4.6340436935424805, 0.0, 0.0, 0.0, 2.0618057250976562]}, "children": [{"boundingVolume": {"box": [22.694255828857422, 4.493447303771973, -37.36582946777344, 2.5399093627929688, 0.0, 0.0, 0.0, 4.6340436935424805, 0.0, 0.0, 0.0, 2.0607776641845703]}, "content": {"uri": "Block_L23_53.b3dm"}, "geometricError": 0.009447215124964714, "refine": "REPLACE"}], "content": {"uri": "Block_L22_98.b3dm"}, "geometricError": 0.018889131024479866, "refine": "REPLACE"}, {"boundingVolume": {"box": [30.313982009887695, 4.493447303771973, -34.59685134887695, 5.079816818237305, 0.0, 0.0, 0.0, 4.6340436935424805, 0.0, 0.0, 0.0, 3.110964775085449]}, "children": [{"boundingVolume": {"box": [30.313982009887695, 4.493447303771973, -34.59685134887695, 5.079816818237305, 0.0, 0.0, 0.0, 4.6340436935424805, 0.0, 0.0, 0.0, 3.110964775085449]}, "content": {"uri": "Block_L23_52.b3dm"}, "geometricError": 0.009012402966618538, "refine": "REPLACE"}], "content": {"uri": "Block_L22_97.b3dm"}, "geometricError": 0.018063928931951523, "refine": "REPLACE"}], "content": {"uri": "Block_L21_71.b3dm"}, "geometricError": 0.037058498710393906, "refine": "REPLACE"}], "content": {"uri": "Block_L20_38.b3dm"}, "geometricError": 0.07607365399599075, "refine": "REPLACE"}], "content": {"uri": "Block_L19_19.b3dm"}, "geometricError": 0.15455754101276398, "refine": "REPLACE"}}