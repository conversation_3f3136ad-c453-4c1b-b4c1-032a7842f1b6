<template>
  <div id="channelList" style="width: 100%">
    <div class="page-header">
      <div class="page-title">
        <el-button
            icon="el-icon-back"
            size="mini"
            style="font-size: 20px; color: #000"
            type="text"
            @click="showDevice"
        ></el-button>
        <el-divider direction="vertical"></el-divider>
        通道列表
      </div>
      <div class="page-header-btn">
        <div v-if="!showTree" style="display: inline">
          搜索:
          <el-input
              @input="search"
              style="margin-right: 1rem; width: auto"
              size="mini"
              placeholder="关键字"
              prefix-icon="el-icon-search"
              v-model="searchSrt"
              clearable
          ></el-input>

          通道类型:
          <el-select
              size="mini"
              @change="search"
              style="width: 8rem; margin-right: 1rem"
              v-model="channelType"
              placeholder="请选择"
              default-first-option
          >
            <el-option label="全部" value=""></el-option>
            <el-option label="设备" value="false"></el-option>
            <el-option label="子目录" value="true"></el-option>
          </el-select>
          在线状态:
          <el-select
              size="mini"
              style="width: 8rem; margin-right: 1rem"
              @change="search"
              v-model="online"
              placeholder="请选择"
              default-first-option
          >
            <el-option label="全部" value=""></el-option>
            <el-option label="在线" value="true"></el-option>
            <el-option label="离线" value="false"></el-option>
          </el-select>
          码流类型重置:
          <el-select
              size="mini"
              style="width: 16rem; margin-right: 1rem"
              @change="subStreamChange"
              v-model="subStream"
              placeholder="请选择码流类型"
              default-first-option
          >
            <el-option label="stream:0(主码流)" value="stream:0"></el-option>
            <el-option label="stream:1(子码流)" value="stream:1"></el-option>
            <el-option label="streamnumber:0(主码流-2022)" value="streamnumber:0"></el-option>
            <el-option label="streamnumber:1(子码流-2022)" value="streamnumber:1"></el-option>
            <el-option label="streamprofile:0(主码流-大华)" value="streamprofile:0"></el-option>
            <el-option label="streamprofile:1(子码流-大华)" value="streamprofile:1"></el-option>
            <el-option
                label="streamMode:main(主码流-水星+TP-LINK)"
                value="streamMode:main"
            ></el-option>
            <el-option
                label="streamMode:sub(子码流-水星+TP-LINK)"
                value="streamMode:sub"
            ></el-option>
          </el-select>
        </div>
        <el-button icon="el-icon-refresh-right" circle size="mini" @click="refresh()"></el-button>
        <el-button
            v-if="showTree"
            icon="el-icon-operation"
            circle
            size="mini"
            @click="switchList()"
        ></el-button>
        <el-button
            v-if="!showTree"
            icon="el-icon-share"
            circle
            size="mini"
            @click="switchTree()"
        ></el-button>
      </div>
    </div>

    <el-container v-loading="isLoging" style="height: 82vh">
      <el-aside
          width="auto"
          style="height: 82vh; background-color: #ffffff; overflow: auto"
          v-if="showTree"
      >
        <DeviceTree
            ref="deviceTree"
            :device="device"
            :onlyCatalog="true"
            :clickEvent="treeNodeClickEvent"
        ></DeviceTree>
      </el-aside>
      <el-main style="padding: 5px">
        <el-table
            ref="channelListTable"
            :data="deviceChannelList"
            :height="winHeight"
            style="width: 100%"
            header-row-class-name="table-header"
        >
          <el-table-column prop="channelId" label="通道编号" min-width="180"></el-table-column>
          <el-table-column prop="deviceId" label="设备编号" min-width="180"></el-table-column>
          <el-table-column prop="name" label="通道名称" min-width="180">
            <template v-slot="scope">
              <el-input
                  v-show="scope.row.edit"
                  v-model="scope.row.name"
                  placeholder="通道名称"
                  :maxlength="255"
                  show-word-limit
                  clearable
              />
              <span v-show="!scope.row.edit">{{ scope.row.name }}</span>
            </template>
          </el-table-column>
          <el-table-column label="快照" min-width="100">
            <template v-slot="scope">
              <div class="demo-image__preview">
                <el-image :src="getSnap(scope.row)" @click="showImage(scope.row)"/>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="subCount" label="子节点数" min-width="100"></el-table-column>
          <el-table-column prop="manufacture" label="厂家" min-width="100"></el-table-column>
          <el-table-column label="位置信息" min-width="120">
            <template v-slot="scope">
              <el-input
                  v-show="scope.row.edit"
                  v-model="scope.row.location"
                  placeholder="例：117.234,36.378"
                  :maxlength="30"
                  show-word-limit
                  clearable
              />
              <span v-show="!scope.row.edit">{{ scope.row.location }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="ptzType" label="云台类型" min-width="100">
            <template v-slot="scope">
              <el-select
                  v-show="scope.row.edit"
                  v-model="scope.row.ptzType"
                  placeholder="云台类型"
                  filterable
              >
                <el-option
                    v-for="(value, key) in ptzTypes"
                    :key="key"
                    :label="value"
                    :value="key"
                />
              </el-select>
              <div v-show="!scope.row.edit">{{ scope.row.ptzTypeText }}</div>
            </template>
          </el-table-column>
          <el-table-column label="开启音频" min-width="100">
            <template v-slot="scope">
              <el-switch
                  @change="updateChannel(scope.row)"
                  v-model="scope.row.hasAudio"
                  active-color="#409EFF"
              >
              </el-switch>
            </template>
          </el-table-column>
          <el-table-column label="码流类型" min-width="180">
            <template v-slot="scope">
              <el-select
                  size="mini"
                  style="margin-right: 1rem"
                  @change="channelSubStreamChange(scope.row)"
                  v-model="scope.row.streamIdentification"
                  placeholder="请选择码流类型"
                  default-first-option
              >
                <el-option label="stream:0(主码流)" value="stream:0"></el-option>
                <el-option label="stream:1(子码流)" value="stream:1"></el-option>
                <el-option label="streamnumber:0(主码流-2022)" value="streamnumber:0"></el-option>
                <el-option label="streamnumber:1(子码流-2022)" value="streamnumber:1"></el-option>
                <el-option label="streamprofile:0(主码流-大华)" value="streamprofile:0"></el-option>
                <el-option label="streamprofile:1(子码流-大华)" value="streamprofile:1"></el-option>
                <el-option
                    label="streamMode:main(主码流-水星+TP-LINK)"
                    value="streamMode:main"
                ></el-option>
                <el-option
                    label="streamMode:sub(子码流-水星+TP-LINK)"
                    value="streamMode:sub"
                ></el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column label="状态" min-width="100">
            <template v-slot="scope">
              <div class="name-wrapper">
                <el-tag size="medium" v-if="scope.row.status === true">在线</el-tag>
                <el-tag size="medium" type="info" v-if="scope.row.status === false">离线</el-tag>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="操作" min-width="340" fixed="right">
            <template v-slot="scope">
              <el-button
                  size="medium"
                  v-bind:disabled="device == null || device.online === 0"
                  icon="el-icon-video-play"
                  type="text"
                  @click="sendDevicePush(scope.row)"
              >播放
              </el-button>
              <el-button
                  size="medium"
                  v-bind:disabled="device == null || device.online === 0"
                  icon="el-icon-switch-button"
                  type="text"
                  style="color: #f56c6c"
                  v-if="!!scope.row.streamId"
                  @click="stopDevicePush(scope.row)"
              >停止
              </el-button>
              <el-divider direction="vertical"></el-divider>
              <el-button
                  v-if="scope.row.edit"
                  size="medium"
                  type="text"
                  icon="el-icon-edit-outline"
                  @click="handleSave(scope.row)"
              >
                保存
              </el-button>
              <el-button
                  v-else
                  size="medium"
                  type="text"
                  icon="el-icon-edit"
                  @click="handleEdit(scope.row)"
                  v-permission
              >
                编辑
              </el-button>
              <el-divider direction="vertical"></el-divider>
              <el-button
                  size="medium"
                  icon="el-icon-s-open"
                  type="text"
                  v-if="scope.row.subCount > 0 || scope.row.parental === 1"
                  @click="changeSubchannel(scope.row)"
              >查看
              </el-button>
              <el-divider
                  v-if="scope.row.subCount > 0 || scope.row.parental === 1"
                  direction="vertical"
              ></el-divider>
              <!--              <el-button size="medium" v-bind:disabled="device == null || device.online === 0"-->
              <!--                         icon="el-icon-video-camera"-->
              <!--                         type="text" @click="queryRecords(scope.row)">设备录像-->
              <!--              </el-button>-->
              <!--              <el-button size="medium" v-bind:disabled="device == null || device.online === 0" icon="el-icon-cloudy"-->
              <!--                         type="text" @click="queryCloudRecords(scope.row)">云端录像-->
              <!--              </el-button>-->
              <el-dropdown @command="command => {moreClick(command, scope.row);}">
                <el-button size="medium" type="text">
                  更多功能<i class="el-icon-arrow-down el-icon--right"></i>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item
                        command="records"
                        v-bind:disabled="device == null || device.online === 0"
                    >
                      设备录像
                    </el-dropdown-item>
                    <el-dropdown-item
                        command="cloudRecords"
                        v-bind:disabled="device == null || device.online === 0"
                    >
                      云端录像
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination
            style="float: right"
            @size-change="handleSizeChange"
            @current-change="currentChange"
            :current-page="currentPage"
            :page-size="count"
            :page-sizes="[15, 25, 35, 50]"
            layout="total, sizes, prev, pager, next"
            :total="total"
        >
        </el-pagination>
      </el-main>
    </el-container>

    <!--设备列表-->
    <devicePlayer ref="devicePlayer"></devicePlayer>

    <el-dialog v-model="showImageVisible" width="60%">
      <img :src="imgUrl" alt="图片预览" style="width:100%;height: 600px;" />
    </el-dialog>
  </div>
</template>

<script>
import devicePlayer from './dialog/devicePlayer.vue';
import uiHeader from './layout/UiHeader.vue';
import DeviceService from './service/DeviceService.js';
import DeviceTree from './common/DeviceTree.vue';
import {getToken} from 'utils/auth';

export default {
  name: 'channelList',
  components: {
    devicePlayer,
    uiHeader,
    DeviceTree,
  },
  data() {
    return {
      show: true,
      deviceService: new DeviceService(),
      device: null,
      showImageVisible: false,
      imgUrl: '',
      deviceId: this.$route.params.deviceId,
      parentChannelId: this.$route.params.parentChannelId,
      deviceChannelList: [],
      videoComponentList: [],
      currentPlayerInfo: {}, //当前播放对象
      updateLooper: 0, //数据刷新轮训标志
      searchSrt: '',
      channelType: '',
      online: '',
      subStream: '',
      winHeight: window.innerHeight - 250,
      currentPage: 1,
      count: 15,
      total: 0,
      beforeUrl: '/video/deviceList',
      isLoging: false,
      showTree: false,
      loadSnap: {},
      ptzTypes: {
        0: '未知',
        1: '球机',
        2: '半球',
        3: '固定枪机',
        4: '遥控枪机',
      }
    }
  },
  mounted() {
    if (this.deviceId) {
      this.deviceService.getDevice(
          this.deviceId,
          result => {
            this.device = result;
          },
          error => {
            console.log('获取设备信息失败');
            console.error(error);
          }
      );
    }
    this.initData();
  },
  destroyed() {
    this.$destroy('videojs');
    clearTimeout(this.updateLooper);
  },
  methods: {
    initData: function () {
      if (typeof this.parentChannelId == 'undefined' || this.parentChannelId == 0) {
        this.getDeviceChannelList();
      } else {
        this.showSubchannels();
      }
    },
    initParam: function () {
      this.deviceId = this.$route.params.deviceId;
      this.parentChannelId = this.$route.params.parentChannelId;
      this.currentPage = 1;
      this.count = 15;
      if (this.parentChannelId == '' || this.parentChannelId == 0) {
        this.beforeUrl = '/deviceList';
      }
    },
    currentChange: function (val) {
      this.currentPage = val;
      this.initData();
    },
    handleSizeChange: function (val) {
      this.count = val;
      this.getDeviceChannelList();
    },
    getDeviceChannelList: function () {
      let that = this;
      if (typeof this.$route.params.deviceId == 'undefined') return;
      this.$axios({
        method: 'get',
        url: `/api/hztech-mediaServer/api/device/query/devices/${this.$route.params.deviceId}/channels`,
        params: {
          page: that.currentPage,
          count: that.count,
          query: that.searchSrt,
          online: that.online,
          channelType: that.channelType,
        },
      }).then(function (res) {
        if (res.data.code === 0) {
          const responseDataList = res.data.data.list;
          responseDataList.forEach(e => {
            e.ptzType = e.ptzType + '';
            e.edit = false
            e.location = ''
            if (e.customLongitude && e.customLatitude) {
              e.location = e.customLatitude + e.customLatitude
            } else if (e.longitude && e.latitude) {
              e.location = e.longitude + e.latitude
            }
          });
          that.total = res.data.data.total;
          that.deviceChannelList = responseDataList;
          // 防止出现表格错位
          that.$nextTick(() => {
            that.$refs.channelListTable.doLayout();
            that.$forceUpdate()
          });
        }
      }).catch(function (error) {
        console.log(error);
      });
    },
    //通知设备上传媒体流
    sendDevicePush: function (itemData) {
      let deviceId = this.deviceId;
      this.isLoging = true;
      let channelId = itemData.channelId;
      console.log('通知设备推流1：' + deviceId + ' : ' + channelId);
      let that = this;
      this.$axios({
        method: 'get',
        url: '/api/hztech-mediaServer/api/play/start/' + deviceId + '/' + channelId,
        params: {
          isSubStream: this.isSubStream,
        },
      }).then(function (res) {
        that.isLoging = false;
        if (res.data.code === 0) {
          setTimeout(() => {
            let snapId = deviceId + '_' + channelId;
            that.loadSnap[deviceId + channelId] = 0;
            that.getSnapErrorEvent(snapId);
          }, 10000);
          itemData.streamId = res.data.data.stream;
          that.$refs.devicePlayer.openDialog('media', deviceId, channelId, {
            streamInfo: res.data.data,
            hasAudio: itemData.hasAudio,
          });
          setTimeout(() => {
            that.initData();
          }, 1000);
        } else {
          that.$message.error(res.data.msg);
          that.isLoging = false;
        }
      }).catch(() => {
        that.isLoging = false;
      });
    },
    moreClick: function (command, itemData) {
      if (command === 'records') {
        this.queryRecords(itemData);
      } else if (command === 'cloudRecords') {
        this.queryCloudRecords(itemData);
      }
    },
    queryRecords: function (itemData) {
      let deviceId = this.deviceId;
      let channelId = itemData.channelId;
      this.$router.push(`/gbRecordDetail/${deviceId}/${channelId}`);
    },
    queryCloudRecords: function (itemData) {
      let deviceId = this.deviceId;
      let channelId = itemData.channelId;
      let app = 'rtp'
      let stream = deviceId + '_' + channelId
      this.$router.push(`/video/cloudRecordDetail/${app}/${stream}`);
    },
    stopDevicePush: function (itemData) {
      var that = this;
      this.$axios({
        method: 'get',
        url: '/api/hztech-mediaServer/api/play/stop/' + this.deviceId + '/' + itemData.channelId,
        params: {
          isSubStream: this.isSubStream,
        },
      }).then(() => {
        that.initData();
      }).catch(function (error) {
        if (error.response.status === 402) {
          // 已经停止过
          that.initData();
        } else {
          console.log(error);
        }
      });
    },
    getSnap: function (row) {
      let token = getToken();
      let baseUrl = window.baseUrl ? window.baseUrl : '';
      return (
          (process.env.NODE_ENV === 'development' ? "" : baseUrl) +
          '/api/hztech-mediaServer/api/device/query/snap/' +
          row.deviceId + '/' + row.channelId + '?HzTech-Auth=' + 'bearer ' + token
      );
    },
    showImage(row){
      this.showImageVisible = true
      this.imgUrl = this.getSnap(row)
    },
    // getBigSnap: function (row) {
    //   return [this.getSnap(row)];
    // },
    getSnapErrorEvent: function (deviceId, channelId) {
      if (typeof this.loadSnap[deviceId + channelId] != 'undefined') {
        console.log('下载截图' + this.loadSnap[deviceId + channelId]);
        if (this.loadSnap[deviceId + channelId] > 5) {
          delete this.loadSnap[deviceId + channelId];
          return;
        }
        setTimeout(() => {
          let url =
              (process.env.NODE_ENV === 'development' ? 'debug' : '') +
              '/api/hztech-mediaServer/api/device/query/snap/' +
              deviceId + '/' + channelId;
          this.loadSnap[deviceId + channelId]++;
          document.getElementById(deviceId + channelId).setAttribute('src', url + '?' + new Date().getTime());
        }, 1000);
      }
    },
    showDevice: function () {
      this.$router.push(this.beforeUrl).then(() => {
        this.initParam();
        this.initData();
      });
    },
    changeSubchannel(itemData) {
      this.beforeUrl = this.$router.currentRoute.path;
      let url = `/${this.$router.currentRoute.name}/${this.$router.currentRoute.params.deviceId}/${itemData.channelId}`;
      this.$router.push(url).then(() => {
        this.searchSrt = '';
        this.channelType = '';
        this.online = '';
        this.initParam();
        this.initData();
      });
    },
    showSubchannels: function (channelId) {
      if (!this.showTree) {
        this.$axios({
          method: 'get',
          url: `/api/hztech-mediaServer/api/device/query/sub_channels/${this.deviceId}/${this.parentChannelId}/channels`,
          params: {
            page: this.currentPage,
            count: this.count,
            query: this.searchSrt,
            online: this.online,
            channelType: this.channelType,
          },
        }).then(res => {
          if (res.data.code === 0) {
            this.total = res.data.data.total;
            this.deviceChannelList = res.data.data.list;
            this.deviceChannelList.forEach(e => {
              e.ptzType = e.ptzType + '';
              this.$set(e, 'edit', false);
              this.$set(e, 'location', '');
              if (e.customLongitude && e.customLatitude) {
                this.$set(e, "location", e.customLongitude + "," + e.customLatitude);
              } else if (e.longitude && e.latitude) {
                this.$set(e, "location", e.longitude + "," + e.latitude);
              }
            });
            // 防止出现表格错位
            this.$nextTick(() => {
              this.$refs.channelListTable.doLayout();
            });
          }
        }).catch(function (error) {
          console.log(error);
        });
      } else {
        this.$axios({
          method: 'get',
          url: `/api/hztech-mediaServer/api/device/query/tree/channel/${this.deviceId}`,
          params: {
            parentId: this.parentChannelId,
            page: this.currentPage,
            count: this.count,
          },
        }).then(res => {
          if (res.data.code === 0) {
            this.total = res.data.total;
            this.deviceChannelList = res.data.list;
            // 防止出现表格错位
            this.$nextTick(() => {
              this.$refs.channelListTable.doLayout();
            });
          }
        }).catch(function (error) {
          console.log(error);
        });
      }
    },
    search: function () {
      this.currentPage = 1;
      this.total = 0;
      this.initData();
    },
    updateChannel: function (row) {
      this.$axios({
        method: 'post',
        url: `/api/hztech-mediaServer/api/device/query/channel/update/${this.deviceId}`,
        params: row,
      }).then(function (res) {
        console.log(JSON.stringify(res));
      });
    },
    subStreamChange: function () {
      this.$confirm('确定重置所有通道的码流类型?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        this.$axios({
          method: 'post',
          url: `/api/hztech-mediaServer/api/device/query/channel/stream/identification/update/`,
          params: {
            deviceId: this.deviceId,
            streamIdentification: this.subStream,
          },
        }).then(res => {
          console.log(JSON.stringify(res));
          this.initData();
        }).finally(() => {
          this.subStream = '';
        });
      }).catch(() => {
        this.subStream = '';
      });
    },
    channelSubStreamChange: function (row) {
      this.$axios({
        method: 'post',
        url: `/api/hztech-mediaServer/api/device/query/channel/stream/identification/update/`,
        params: {
          deviceId: this.deviceId,
          channelId: row.channelId,
          streamIdentification: row.streamIdentification,
        },
      }).then(function (res) {
        console.log(JSON.stringify(res));
      });
    },
    refresh: function () {
      this.initData();
    },
    switchTree: function () {
      this.showTree = true;
      this.deviceChannelList = [];
      this.parentChannelId = 0;
      this.currentPage = 1;
    },
    switchList: function () {
      this.showTree = false;
      this.deviceChannelList = [];
      this.parentChannelId = 0;
      this.currentPage = 1;
      this.initData();
    },
    treeNodeClickEvent: function (device, data, isCatalog) {
      console.log(device);
      if (!!!data.channelId) {
        this.parentChannelId = device.deviceId;
      } else {
        this.parentChannelId = data.channelId;
      }
      this.initData();
    },
    // 保存
    handleSave(row) {
      if (row.location) {
        const segements = row.location.split(",");
        if (segements.length !== 2) {
          console.log(1)
          this.$message.warning("位置信息格式有误，例：117.234,36.378");
          return;
        } else {
          row.customLongitude = parseFloat(segements[0]);
          row.customLatitude = parseFloat(segements[1]);
          if (!(row.customLongitude && row.customLatitude)) {
            this.$message.warning("位置信息格式有误，例：117.234,36.378");
            return;
          }
        }
      } else {
        delete row.longitude;
        delete row.latitude;
      }
      Object.keys(row).forEach(key => {
        const value = row[key];
        if (value === null || value === undefined || (typeof value === "string" && value.trim() === "")) {
          delete row[key];
        }
      });
      this.$axios({
        method: 'post',
        url: `/api/hztech-mediaServer/api/device/query/channel/update/${this.deviceId}`,
        params: row,
      }).then(response => {
        if (response.data.code === 0) {
          this.$message.success('修改成功！');
          this.initData();
        } else {
          this.$message.error('修改失败！');
        }
      }).catch(_ => {
        this.$message.error('修改失败！');
      });
    },
    // 是否正在编辑
    isEdit() {
      let editing = false;
      this.deviceChannelList.forEach(e => {
        if (e.edit) {
          editing = true;
        }
      });
      return editing;
    },
    // 编辑
    handleEdit(row) {
      if (this.isEdit()) {
        this.$message.warning('请保存当前编辑项！');
      } else {
        row.edit = true;
      }
    }
  }
}
</script>

<style scoped>
.el-image {
  z-index: 1001 !important; /* 使用!important确保覆盖其他样式 */
}
</style>