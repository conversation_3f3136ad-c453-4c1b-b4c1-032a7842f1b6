import EventBus from '@/event-bus/'
import {onMounted, onBeforeUnmount} from 'vue'
import {DeviceLogUploadInfo} from '@/types/device-log'

export function useDeviceLogUploadProgressEvent(onDeviceLogUploadWs: (data: DeviceLogUploadInfo) => void): void {
  function handleDeviceLogUploadProgress(payload: any) {
    onDeviceLogUploadWs(payload.data)
  }

  onMounted(() => {
    EventBus.on('deviceLogUploadProgress', handleDeviceLogUploadProgress)
  })

  onBeforeUnmount(() => {
    EventBus.off('deviceLogUploadProgress', handleDeviceLogUploadProgress)
  })
}