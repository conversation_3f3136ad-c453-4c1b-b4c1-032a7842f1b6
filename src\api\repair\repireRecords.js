import request from '@/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/api/hztech-gong-di/fireworkproblemreporthandle/page',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: '/api/hztech-gong-di/fireworkproblemreporthandle/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/api/hztech-gong-di/fireworkproblemreporthandle/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/api/hztech-gong-di/fireworkproblemreporthandle/submit',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/api/hztech-gong-di/fireworkproblemreporthandle/submit',
    method: 'post',
    data: row
  })
}
