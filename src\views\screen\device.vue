<template>
  <div class="main">
    <div class="left">
      <div class="titleBg">
        <span>设备统计</span>
      </div>
      <div class="left1">
        <div class="left1_title1">
          <img src="@/assets/screen/airportEquipment.png" alt="">
          机场设备
        </div>
        <div class="left1_item">
          <div class="airport1">
            <div>部署数量</div>
            <div>{{ dock.count }}</div>
          </div>
          <div class="airport2">
            <div>在线数量</div>
            <div>{{ dock.online }}</div>
          </div>
          <div class="airport3">
            <div>在线率</div>
            <div>{{ dock.onlineStat }}%</div>
          </div>
        </div>

        <div class="left1_title2">
          <img src="@/assets/screen/aircraft.png" alt="" style="width: 100px;height: 70px;">
          飞行器
        </div>
        <div class="left1_item">
          <div class="airport1">
            <div>部署数量</div>
            <div>{{ drone.count }}</div>
          </div>
          <div class="airport2">
            <div>在线数量</div>
            <div>{{ drone.online }}</div>
          </div>
          <div class="airport3">
            <div>在线率</div>
            <div>{{ drone.onlineStat }}%</div>
          </div>
        </div>
      </div>

      <div class="titleBg">
        <span>机场状态</span>
      </div>
      <div class="left2">
        <div class="select">
          <el-select v-model="airportSn" placeholder="请选择机场" style="width: 98%;">
            <el-option v-for="(item, index) in options1" :key="index" :label="item.nickname" :value="item.device_sn" />
          </el-select>
        </div>
        <div class="detail">
          <div class="img">
            <img src="../../assets/screen/airportBackground.png" alt="" style="width: 100%;height: 100%;">
          </div>
          <div class="list">
            <div style="margin-top: 10px;font-size: 16px;">{{ airportName }}</div>
            <div>
              SN：{{ airportSn }}
              <span style="float: right;color: #63FF57;">{{ enumerateStatus[airportStatus] }}</span>
            </div>
            <div style="color: #63FF57;">空调状态：{{ enumerateAirStatus[airConditioningStatus] }}</div>
          </div>
        </div>
        <div class="data">
          <div class="data_item" v-for="(item, index) in options3" :key="index">
            <div :class="`data_item_imgL_${index}`" />
            <div class="data_item_text">
              <div style="white-space: nowrap;">{{ item.name }}</div>
              <div>{{ item.num }}{{ item.num !== null ? item.unit : '-' }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="right">
      <div class="titleBg">
        <span>气象信息</span>
      </div>
      <div class="right1">
        <div style="margin-top: 2%">
          <div class="img"></div>
          <div class="num">
            <div>
              <span style="font-size: 16px;">风速</span>
              <span style="color: #0466F8;position: absolute;right: 30px;">{{ deviceInfo.dock.basic_osd?.wind_speed
                }}M/s</span>
            </div>
            <div></div>
          </div>
        </div>
        <div style="margin-top: 3%">
          <div class="img"></div>
          <div class="num">
            <div>
              <span style="font-size: 16px;">温度</span>
              <span style="color: #ED5D25;position: absolute;right: 30px;">{{ deviceInfo.dock.basic_osd?.temperature
                }}℃</span>
            </div>
            <div></div>
          </div>
        </div>
        <div style="margin-top: 3%">
          <div class="img"></div>
          <div class="num">
            <div>
              <span style="font-size: 16px;">湿度</span>
              <span style="color: #20b1ff;position: absolute;right: 30px;">{{ deviceInfo.dock.basic_osd?.humidity
                }}</span>
            </div>
            <div></div>
          </div>
        </div>
        <div style="margin-top: 3%">
          <div class="img"></div>
          <div class="num">
            <div>
              <span style="font-size: 16px;">降雨量</span>
              <span style="color: #56FF5E;position: absolute;right: 30px;">{{
                RainfallEnum[deviceInfo?.dock?.basic_osd?.rainfall] }}</span>
            </div>
            <div></div>
          </div>
        </div>
      </div>

      <div class="titleBg">
        <span>飞行器状态</span>
      </div>
      <div class="right2">
        <div class="select">
          <el-select v-model="aircraftSn" placeholder="请选择飞行器" style="width: 98%;">
            <el-option v-for="(item, index) in options2" :key="index" :label="item.device_name"
              :value="item.device_sn" />
          </el-select>
        </div>
        <div class="detail">
          <div class="img">
            <img src="../../assets/screen/aircraftBackground.png" alt="" style="width: 100%;height: 100%;">
          </div>
          <div class="list">
            <div style="margin-top: 10px;font-size: 16px;">{{ aircraftName ? aircraftName : '暂无飞行器开机上线' }}</div>
            <div>
              SN：{{ aircraftSn ? aircraftSn : '-' }}
              <span style="float: right;color: #63FF57;">{{ enumerateaircraftStatus[aircraftStatus] }}</span>
            </div>
            <!--<div style="color: #63FF57;cursor: pointer;">进入驾驶舱</div>-->
          </div>
        </div>
        <div class="data">
          <div class="data_item" v-for="(item, index) in options4" :key="index">
            <div :class="`data_item_imgR_${index}`" />
            <div class="data_item_text">
              <div>{{ item.name }}</div>
              <div>{{ item.num }}{{ item.num !== null ? item.unit : '-' }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onBeforeUnmount, watch, computed } from 'vue';
import store from "@/store";
import EventBus from "@/event-bus";
import { RainfallEnum } from '@/api/enum';
import { onBeforeRouteUpdate } from 'vue-router';
import { EBizCode, EDeviceTypeName, } from '@/types';
import { deviceTsaUpdate } from "@/hooks/use-g-map-tsa";
import { useConnectWebSocket } from "@/hooks/use-connect-websocket";
import { DevicesStat, DockOnlineDevices, DroneOnlineDevices } from '@/api/screen';
import { enumerateAirStatus, enumerateStatus, enumerateaircraftStatus, enumerateCoverStatus } from '@/api/enum/index'

const deviceTsaUpdateHook = deviceTsaUpdate();

let dock = ref({});
let drone = ref({});
let airportSn = ref('');
let airportName = ref('');
let airportStatus = ref(null);
let airConditioningStatus = ref(null);
let aircraftSn = ref('');
let aircraftName = ref('');
let aircraftStatus = ref(null);
let options1 = ref([]);
let options2 = ref([]);
let options3 = ref([
  { name: '累计运行时间', num: null, unit: 'min' },
  { name: '作业架次', num: null, unit: '次' },
  { name: '剩余容量', num: null, unit: 'GB' },
  { name: '舱盖状态', num: null, unit: null },
  { name: '网络状态', num: null, unit: 'kb/s' },
  { name: '机场搜星', num: null, unit: null },
  { name: '是否设置备降点', num: null, unit: null },
  { name: '工作电压', num: null, unit: 'mV' },
  { name: '工作电流', num: null, unit: 'mA' },
]);
let options4 = ref([
  { name: '水平速度', num: null, unit: 'm/s' },
  { name: '垂直速度', num: null, unit: 'm/s' },
  { name: '电池电量', num: null, unit: '%' },
  { name: '海拔高度', num: null, unit: 'm' },
  { name: '绝对高度', num: null, unit: 'm' },
  { name: '相对高度', num: null, unit: 'm' },
  { name: '电池温度', num: null, unit: '℃' },
  { name: '工作电压', num: null, unit: 'mV' },
  { name: 'GPS搜星', num: null, unit: null },
]);

let deviceInfo = reactive({
  dock: {
    basic_osd: {}
  }
})

const init = () => {
  DevicesStat().then(res => {
    const data = res.data.data
    dock.value = data.dock // 机场
    drone.value = data.drone // 飞行器
  })
  DockOnlineDevices().then(res => {
    const data = res.data.data
    if (data.length > 0) {
      options1.value = data // 机场在线设备列表
      airportSn.value = data[0].device_sn
      airportName.value = data[0].nickname
    }
  })
  DroneOnlineDevices().then(res => {
    const data = res.data.data
    if (data.length > 0) {
      options2.value = data // 飞行器在线设备列表
      aircraftSn.value = data[0].device_sn
      aircraftName.value = data[0].nickname
    }
  })
};

const osdVisible = computed(() => {
  return store.state.dock.osdVisible
});

onMounted(() => {
  init();
});

onBeforeUnmount(() => {
  deviceTsaUpdateHook.removeAllMarker();
});

onBeforeRouteUpdate(() => {
  store.state.dock.osdVisible.visible = false;
});

// 更新机场数据的函数
const updateDockData = (dockData) => {
  if (!dockData) return;

  airportStatus.value = dockData.basic_osd?.mode_code; // 机场状态
  airConditioningStatus.value = dockData.basic_osd?.air_conditioner?.air_conditioner_state; // 机场空调状态
  options3.value[0].num = (dockData.work_osd?.acc_time / 60).toFixed(0); // 累计运行时间
  options3.value[1].num = dockData.work_osd?.job_number; // 作业架次
  options3.value[2].num = getStorage(dockData.basic_osd?.storage); // 剩余容量
  options3.value[3].num = enumerateCoverStatus[dockData.basic_osd?.cover_state]; // 舱盖状态
  options3.value[4].num = dockData.basic_osd?.network_state?.rate; // 网络状态
  options3.value[5].num = dockData.basic_osd?.position_state?.rtk_number; // RTK搜星数量
  options3.value[6].num = dockData.basic_osd?.alternate_land_point?.is_configured ? '已设置' : '未设置'; // 是否设置备降点
  options3.value[7].num = dockData.work_osd?.working_voltage; // 工作电压
  options3.value[8].num = dockData.work_osd?.working_current; // 工作电流

  // 气象信息
  deviceInfo.dock.basic_osd = dockData.basic_osd;
}

// 更新飞行器数据的函数
const updateAircraftData = (deviceData) => {
  if (!deviceData) return;

  aircraftStatus.value = deviceData.mode_code; // 飞行器状态
  options4.value[0].num = deviceData.horizontal_speed; // 水平速度
  options4.value[1].num = deviceData.vertical_speed; // 垂直速度
  options4.value[2].num = deviceData?.battery?.capacity_percent; // 电池电量
  options4.value[3].num = deviceData.height ? deviceData.height.toFixed(2) : null; // 海拔高度
  options4.value[4].num = deviceData.height ? deviceData.height.toFixed(2) : null; // 绝对高度
  options4.value[5].num = deviceData.elevation; // 相对高度
  options4.value[6].num = deviceData?.battery?.batteries[0]?.temperature; // 电池温度
  options4.value[7].num = deviceData?.battery?.batteries[0]?.voltage; // 工作电压
  options4.value[8].num = deviceData?.position_state?.gps_number; // GPS搜星
}

// 重置飞行器数据
const resetAircraftData = () => {
  aircraftStatus.value = null;
  options4.value.forEach((item) => {
    item.num = null;
  });
}

watch(() => store.state.dock.deviceState, data => {
  if (data.currentType === EDeviceTypeName.Dock && data.currentSn === airportSn.value) {
    // 机场实时参数信息
    const dock = data.dockInfo[airportSn.value];
    updateDockData(dock);

    if (osdVisible.value.visible) {
      deviceInfo.dock = data.dockInfo[osdVisible.value.gateway_sn]
      deviceInfo.device = data.deviceInfo[deviceInfo.dock.basic_osd.sub_device?.device_sn ?? osdVisible.value.sn]
    }
  } else if (data.currentType === EDeviceTypeName.Aircraft && data.currentSn === aircraftSn.value) {
    // 飞行器实时参数信息
    const aircraft = data.deviceInfo[aircraftSn.value];
    updateAircraftData(aircraft);

    if (osdVisible.value.visible) {
      deviceInfo.dock = data.dockInfo[osdVisible.value.gateway_sn]
      deviceInfo.device = data.deviceInfo[deviceInfo.dock.basic_osd.sub_device?.device_sn ?? osdVisible.value.sn]
    }
  } else if (!aircraftSn.value || !data.deviceInfo[aircraftSn.value]) {
    resetAircraftData();
  }
}, { deep: true })

function getStorage(data) {
  return ((data?.total - data?.used) / 1024 / 1024).toFixed(2) //GB
}

const messageHandler = async (payload) => {
  if (!payload) {
    return
  }
  switch (payload.biz_code) {
    case EBizCode.GatewayOsd: {
      store.commit('SET_GATEWAY_INFO', payload.data)
      break
    }
    case EBizCode.DeviceOsd: {
      store.commit('SET_DEVICE_INFO', payload.data)
      break
    }
    case EBizCode.DockOsd: {
      store.commit('SET_DOCK_INFO', payload.data)
      break
    }
    case EBizCode.MapElementCreate: {
      store.commit('SET_MAP_ELEMENT_CREATE', payload.data)
      break
    }
    case EBizCode.MapElementUpdate: {
      store.commit('SET_MAP_ELEMENT_UPDATE', payload.data)
      break
    }
    case EBizCode.MapElementDelete: {
      store.commit('SET_MAP_ELEMENT_DELETE', payload.data)
      break
    }
    case EBizCode.DeviceOnline: {
      store.commit('SET_DEVICE_ONLINE', payload.data)
      break
    }
    case EBizCode.DeviceOffline: {
      store.commit('SET_DEVICE_OFFLINE', payload.data)
      break
    }
    case EBizCode.FlightTaskProgress:
    case EBizCode.FlightTaskMediaProgress:
    case EBizCode.FlightTaskMediaHighestPriority: {
      EventBus.emit('flightTaskWs', payload)
      break
    }
    case EBizCode.DeviceHms: {
      store.commit('SET_DEVICE_HMS_INFO', payload.data)
      break
    }
    case EBizCode.DeviceReboot:
    case EBizCode.DroneOpen:
    case EBizCode.DroneClose:
    case EBizCode.CoverOpen:
    case EBizCode.CoverClose:
    case EBizCode.PutterOpen:
    case EBizCode.PutterClose:
    case EBizCode.ChargeOpen:
    case EBizCode.ChargeClose:
    case EBizCode.DeviceFormat:
    case EBizCode.DroneFormat: {
      store.commit('SET_DEVICES_CMD_EXECUTE_INFO', {
        biz_code: payload.biz_code,
        timestamp: payload.timestamp,
        ...payload.data,
      })
      break
    }
    case EBizCode.ControlSourceChange:
    case EBizCode.FlyToPointProgress:
    case EBizCode.TakeoffToPointProgress:
    case EBizCode.JoystickInvalidNotify:
    case EBizCode.DrcStatusNotify: {
      EventBus.emit('droneControlWs', payload)
      break
    }
    case EBizCode.FlightAreasSyncProgress: {
      EventBus.emit('flightAreasSyncProgressWs', payload.data)
      break
    }
    case EBizCode.FlightAreasDroneLocation: {
      EventBus.emit('flightAreasDroneLocationWs', payload)
      break
    }
    case EBizCode.FlightAreasUpdate: {
      EventBus.emit('flightAreasUpdateWs', payload.data)
      break
    }
    case EBizCode.IrMeteringPoint:
    case EBizCode.IrMeteringArea: {
      EventBus.emit('irMeteringWs', payload.data)
      break
    }
    default:
      break
  }
}

// 监听ws 消息
useConnectWebSocket(messageHandler);

// 监听飞行器选择变化
watch(() => aircraftSn.value, (newVal, oldVal) => {
  // 选择新飞行器时，立即从store获取最新数据
  aircraftName.value = options2.value.find(item => item.device_sn === newVal)?.nickname;
  if (newVal && store.state.dock.deviceState.deviceInfo[newVal]) {
    const deviceData = store.state.dock.deviceState.deviceInfo[newVal];
    updateAircraftData(deviceData);
  } else {
    resetAircraftData();
  }
});

watch(() => airportSn.value, (newVal, oldVal) => {
  airportName.value = options1.value.find(item => item.device_sn === newVal)?.nickname;

  // 判断store中是否有新机场的数据，如果有则更新界面显示
  if (newVal && store.state.dock.deviceState.dockInfo[newVal]) {
    const dockData = store.state.dock.deviceState.dockInfo[newVal];
    updateDockData(dockData);
  } else {
    airportStatus.value = null;
    airConditioningStatus.value = null;
    options3.value.forEach(item => {
      item.num = null;
    });
  }
})
</script>

<style lang="scss" scoped>
.main {
  width: 100%;
  height: 100%;
  position: relative;
}

.left {
  position: absolute;
  top: 0;
  left: 1.3vw;
  width: 29.4vw;
  height: 100%;

  .left1 {
    width: 100%;
    height: 37%;

    .left1_title1 {
      width: 98%;
      height: 38px;
      margin: 1.6vh 0 1.6vh 2%;
      font-size: 16px;
    }

    .left1_title2 {
      width: 98%;
      height: 58px;
      margin: 1.6vh 0 1.6vh 2%;
      font-size: 16px;
    }

    .left1_item {
      width: 100%;
      height: 8.2vh;
      display: flex;
      justify-content: space-between;
    }
  }

  .left2 {
    width: 100%;
    height: 63%;

    .select {
      width: 100%;
      margin-top: .6vh;
    }

    .detail {
      width: 100%;
      height: 13vh;
      margin-top: 2vh;
      display: flex;

      .img {
        width: 38%;
        height: 100%;
      }

      .list {
        width: 60%;
        height: 100%;
        margin-left: 2%;

        div {
          width: 100%;
          height: 30%;
        }
      }
    }

    .data {
      width: 100%;
      height: 24vh;
      margin-top: 3vh;
      display: flex;
      flex-wrap: wrap;
      align-content: space-between;
      justify-content: space-between;

      .data_item {
        width: 30%;
        height: 26%;
        display: flex;

        .data_item_text {
          width: 62%;
          margin-left: 5%;
        }
      }
    }
  }
}

.right {
  position: absolute;
  top: 0;
  right: 1.3vw;
  width: 29.4vw;
  height: 100%;

  .right1 {
    width: 100%;
    height: 37%;

    div:nth-child(1) {
      width: 100%;
      height: 20%;
      display: flex;

      .img {
        width: 16%;
        height: 100%;
        background-size: 100% 100%;
        background-position: center center;
        background-image: url('@/assets/screen/weather1.png');
      }

      .num {
        width: 80%;
        height: 100%;
        display: flex;
        flex-direction: column;

        div:nth-child(1) {
          width: 100%;
          height: 50%;
        }

        div:nth-child(2) {
          width: 100%;
          height: 5%;
          background: linear-gradient(270deg, #0466F8 0%, #87B7FF 100%);
        }
      }
    }

    div:nth-child(2) {
      width: 100%;
      height: 20%;
      display: flex;

      .img {
        width: 16%;
        height: 100%;
        background-size: 100% 100%;
        background-position: center center;
        background-image: url('@/assets/screen/weather2.png');
      }

      .num {
        width: 80%;
        height: 100%;
        display: flex;
        flex-direction: column;

        div:nth-child(1) {
          width: 100%;
          height: 50%;
        }

        div:nth-child(2) {
          width: 100%;
          height: 5%;
          background: linear-gradient(90deg, #FF976E 0%, #FF4800 100%);
        }
      }
    }

    div:nth-child(3) {
      width: 100%;
      height: 20%;
      display: flex;

      .img {
        width: 16%;
        height: 100%;
        background-size: 100% 100%;
        background-position: center center;
        background-image: url('@/assets/screen/weather3.png');
      }

      .num {
        width: 80%;
        height: 100%;
        display: flex;
        flex-direction: column;

        div:nth-child(1) {
          width: 100%;
          height: 50%;
        }

        div:nth-child(2) {
          width: 100%;
          height: 5%;
          background: linear-gradient(270deg, #00A6FF 0%, #9FDDFF 100%);
        }
      }
    }

    div:nth-child(4) {
      width: 100%;
      height: 20%;
      display: flex;

      .img {
        width: 16%;
        height: 100%;
        background-size: 100% 100%;
        background-position: center center;
        background-image: url('@/assets/screen/weather4.png');
      }

      .num {
        width: 80%;
        height: 100%;
        display: flex;
        flex-direction: column;

        div:nth-child(1) {
          width: 100%;
          height: 50%;
        }

        div:nth-child(2) {
          width: 100%;
          height: 5%;
          background: linear-gradient(270deg, #0CFF18 0%, #BCFFC0 99%);
        }
      }
    }
  }

  .right2 {
    width: 100%;
    height: 63%;

    .select {
      width: 100%;
      margin-top: .6vh;
    }

    .detail {
      width: 100%;
      height: 13vh;
      margin-top: 2vh;
      display: flex;

      .img {
        width: 38%;
        height: 100%;
      }

      .list {
        width: 60%;
        height: 100%;
        margin-left: 2%;

        div {
          width: 100%;
          height: 30%;
        }
      }
    }

    .data {
      width: 100%;
      height: 24vh;
      margin-top: 3vh;
      display: flex;
      flex-wrap: wrap;
      align-content: space-between;
      justify-content: space-between;

      .data_item {
        width: 30%;
        height: 26%;
        display: flex;

        .data_item_text {
          width: 62%;
          margin-left: 5%;
        }
      }
    }
  }
}

.airport1 {
  width: 30%;
  height: 100%;
  background-size: 100% 100%;
  background-position: center center;
  background-image: url('@/assets/screen/aircraft1.png');
  text-align: center;

  div:nth-child(1) {
    width: 100%;
    height: 50%;
    line-height: 5vh;
  }

  div:nth-child(2) {
    width: 100%;
    height: 50%;
    font-size: 20px;
  }
}

.airport2 {
  width: 30%;
  height: 100%;
  background-size: 100% 100%;
  background-position: center center;
  background-image: url('@/assets/screen/aircraft2.png');
  text-align: center;

  div:nth-child(1) {
    width: 100%;
    height: 50%;
    line-height: 5vh;
  }

  div:nth-child(2) {
    width: 100%;
    height: 50%;
    font-size: 20px;
  }
}

.airport3 {
  width: 30%;
  height: 100%;
  background-size: 100% 100%;
  background-position: center center;
  background-image: url('@/assets/screen/aircraft3.png');
  text-align: center;

  div:nth-child(1) {
    width: 100%;
    height: 50%;
    line-height: 5vh;
  }

  div:nth-child(2) {
    width: 100%;
    height: 50%;
    font-size: 20px;
  }
}

.titleBg {
  width: 29.4vw;
  height: 40px;
  line-height: 40px;
  background-position: center center;
  background-image: url('@/assets/screen/titleBg.png');

  span {
    margin-left: 42px;
    font-size: 16px;
    letter-spacing: .1em;
  }
}

@for $n from 0 through 8 {
  .data_item_imgL_#{$n} {
    background-image: url('@/assets/screen/airportItem#{$n + 1}.png');
    background-size: 100% 100%;
    width: 52px;
    height: 46px;
    display: inline-block;
  }
}

@for $n from 0 through 8 {
  .data_item_imgR_#{$n} {
    background-image: url('@/assets/screen/aircraftItem#{$n + 1}.png');
    background-size: 100% 100%;
    width: 52px;
    height: 46px;
    display: inline-block;
  }
}

:deep(.el-select__wrapper) {
  background: rgba(11, 35, 57, 0.4);
  box-shadow: 0 0 0 1px #09559a;
}

:deep(.el-select__placeholder) {
  color: #ffffff;
}
</style>