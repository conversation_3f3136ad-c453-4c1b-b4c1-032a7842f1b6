import EventBus from '@/event-bus/'
import {onMounted, onBeforeUnmount} from 'vue'
import {DeviceCmdExecuteInfo} from '@/types/device-cmd'

export function useDeviceUpgradeEvent(onDeviceUpgradeWs: (payload: DeviceCmdExecuteInfo) => void): void {
  function handleDeviceUpgrade(payload: any) {
    onDeviceUpgradeWs(payload.data)
  }

  onMounted(() => {
    EventBus.on('deviceUpgrade', handleDeviceUpgrade)
  })

  onBeforeUnmount(() => {
    EventBus.off('deviceUpgrade', handleDeviceUpgrade)
  })
}