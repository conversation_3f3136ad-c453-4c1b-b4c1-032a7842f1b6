<template>
  <!-- ant-desigin-vue -->
  <a-modal
    :visible.async="visible"
    :title="title"
    :style="style"
    :closable="false"
    centered
    :footer="showFooter ? undefined : null"
  >
    <!-- 自定义 -->
    <slot></slot>
    <!-- 自定义页脚 -->
    <template #footer>
      <a-button key="back" @click="onCancel">{{ cancelText }}</a-button>
      <a-button key="submit" type="primary" :disabled="btnDisabled" @click="handleOk">{{
        confirmText
      }}</a-button>
    </template>
  </a-modal>
</template>

<script setup>
import { defineProps, defineEmits } from 'vue';
const props = defineProps({
  /**标题 */
  title: {
    type: String,
    default: '',
  },
  /**是否显示，默认否 */
  visible: {
    type: Boolean,
    default: false,
  },
  /**宽度 */
  width: {
    type: Number,
    default: 1000,
  },
  /**确认按钮是否隐藏 */
  btnDisabled: {
    type: Boolean,
    default: false,
  },
  /**是否显示页脚，默认是*/
  showFooter: {
    type: Boolean,
    default: true,
  },
  confirmText: {
    type: String,
    default: '确定',
  },
  cancelText: {
    type: String,
    default: '取消',
  },
});
const style = computed(() => {
  return {
    width: props.width + 'px',
  };
});
const emit = defineEmits(['cancel', 'submit']);

const onCancel = () => {
  emit('cancel', false);
};
const handleOk = () => {
  emit('submit', true);
};

// 组件逻辑
</script>
