<template>
  <div class="project-app-wrapper">
    <div class="left">
      <!-- <Sidebar /> -->
      <div class="main-content uranus-scrollbar dark">
        <div class="project-flight-area-wrapper height-100">
			<span class="header">自定义飞行区域</span>
          <!-- <Title title="自定义飞行区域" /> -->
          <a-spin :spinning="loading" :delay="300" tip="loading" size="large" class="height-100">
            <FlightAreaPanel :data="flightAreaList" @location-area="clickArea" @delete-area="deleteAreaById"/>

            <DividerLine />
            <FlightAreaSyncPanel />
          </a-spin>
        </div>
      </div>
    </div>
    <div class="right">
      <div class="map-wrapper">
        <div class="g-map-wrapper">
         
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, ref, onBeforeUnmount } from 'vue';
import Title from '@/components/workspace/Title.vue';
import DividerLine from '@/components/workspace/DividerLine.vue';
import FlightAreaPanel from '@/components/flight-area/FlightAreaPanel.vue';
import FlightAreaSyncPanel from '@/components/flight-area/FlightAreaSyncPanel.vue';
import { GetFlightArea, deleteFlightArea, getFlightAreaList } from '@/api/flight-area';
import { useGMapCover } from '@/hooks/use-c-map-cover';
import { useMapTool } from '@/hooks/use-map-tool';
import { EFlightAreaType, EGeometryType, FlightAreaUpdate } from '@/types/flight-area';
import { useFlightArea } from '@/components/flight-area/use-flight-area';
import { useFlightAreaUpdateEvent } from '@/components/flight-area/use-flight-area-update';
import Sidebar from '@/components/common/sidebar.vue';
import djimap from '@/components/djimap/djimap.vue';
import EventBus from '@/event-bus';
import store from '@/store'
import {getRoot} from "@/root"

const loading = ref(false);
const flightAreaList = ref<GetFlightArea[]>([]);
// const coverMap = ref<{ [key: string]: any[] }>({});
let useGMapCoverHook = useGMapCover();
let useMapToolHook = useMapTool();
// let coverMap = store.state.dock.coverMap;
onMounted(() => {
	getDataList();
 EventBus.on('flightList', data => {
    getDataList();
  });
});
onBeforeUnmount(() => {
  //组件内实列被卸载前生命周期
	const root = getRoot();
	for (const id in store.state.dock.coverMap) {
	  useGMapCoverHook.removeCoverFromMap(id)
	}
  // root.$map.clearMap();
  // store.state.dock.coverMap={}
});
const { getGcj02 } = useFlightArea();

const initMapFlightArea = () => {
  useMapToolHook = useMapTool();
  useGMapCoverHook = useGMapCover();
  flightAreaList.value.forEach(area => {
    updateMapFlightArea(area,"show");
  });
};

const updateMapFlightArea = (area: GetFlightArea,show:string) => {
  // 先尝试移除已存在的同ID实体，避免重复添加
  useGMapCoverHook.removeCoverFromMap(area.area_id);
  
  switch (area.content.geometry.type) {
    case EGeometryType.CIRCLE:
      useGMapCoverHook.updateFlightAreaCircle(
        area.area_id,
        area.name,
        area.content.geometry.radius,
        getGcj02(area.content.geometry.coordinates),
        area.status,
        area.type,
		show
      );
      break;
    case EGeometryType.POLYGON:
      useGMapCoverHook.updateFlightAreaPolygon(
        area.area_id,
        area.name,
        getGcj02(area.content.geometry.coordinates[0]),
        area.status,
        area.type,
		show
      );
      break;
  }
};

//获取自定义飞行区域列表
const getDataList = () => {
  loading.value = true
  getFlightAreaList().then(res => {
    flightAreaList.value = res.data
    setTimeout(initMapFlightArea, 2000)
  }).finally(() => {
    loading.value = false
  })
}

// //删除单个ID区域
const deleteAreaById = (areaId: string) => {
  flightAreaList.value = flightAreaList.value.filter(data => data.area_id !== areaId);
  useGMapCoverHook.removeCoverFromMap(areaId);
  deleteFlightArea(areaId);
};

//删除区域
const deleteArea = (area: FlightAreaUpdate) => {
  flightAreaList.value = flightAreaList.value.filter(data => data.area_id !== area.area_id);
  useGMapCoverHook.removeCoverFromMap(area.area_id);
};

//更新区域
const updateArea = (area: FlightAreaUpdate) => {
  flightAreaList.value = flightAreaList.value.map(data => data.area_id === area.area_id ? area : data)
  updateMapFlightArea(area as GetFlightArea,"show")
}

//添加区域
const addArea = (area: FlightAreaUpdate) => {
  flightAreaList.value.push(area as GetFlightArea);
  updateMapFlightArea(area as GetFlightArea,"show");
};

useFlightAreaUpdateEvent(addArea, deleteArea, updateArea);

const clickArea = (area: GetFlightArea) => {
  let coordinate;
  switch (area.content.geometry.type) {
    case EGeometryType.CIRCLE:
      coordinate = getGcj02(area.content.geometry.coordinates);
      break;
    case 'Polygon':
      coordinate = useGMapCoverHook.calcPolygonPosition(
        getGcj02(area.content.geometry.coordinates[0])
      );
      break;
  }
  useMapToolHook.panTo(coordinate);
};
</script>
<style>
.bor-b {
  height: 50px;
  line-height: 50px;
  border-bottom: 1px solid #4f4f4f;
  font-weight: 450;
}
.header {
    height: 52px;
    border-bottom: 1px solid #4f4f4f;
    font-weight: 700;
    font-size: 16px;
    padding-left: 10px;
    display: flex;
    align-items: center;
  }
</style>
