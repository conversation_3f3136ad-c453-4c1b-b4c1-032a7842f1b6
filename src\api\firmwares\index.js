import request from '@/axios';
import {ELocalStorageKey} from '@/api/enum/index';

const HTTP_PREFIX = '/hztech-flight-core/manage/api/v1'

// export const firmwaresList = (params) => {
//   return request({
//     url: '/hztech-flight-core/manage/api/v1/workspaces/' + workspaceId + '/firmwares',
//     method: 'get',
//     params: {
//       page: params.currentPage,
//       page_size: params.pageSize,
//       device_name: params.device_name,
//       product_version: params.product_version,
//       status: 'All'
//     },
//   })
// }

export const getFirmwares = async function (workspace_id, page, body) {
  const url = `${HTTP_PREFIX}/workspaces/${workspace_id}/firmwares?page=${page.page}&page_size=${page.page_size}` +
    `&device_name=${body.device_name}&product_version=${body.product_version}&status=${body.firmware_status ?? ''}`
  const result = await request.get(url)
  return result.data
}

export const importFirmareFile = async function (workspaceId, param) {
  const url = `${HTTP_PREFIX}/workspaces/${workspaceId}/firmwares/file/upload`
  const result = await request.post(url, param)
  return result.data
}
/**
 * 删除固件
 * @param {*} workspaceId 
 * @param {*} param 
 * @returns 
 */
export const removeFirmware = async function (workspaceId, firmware_id) {
  const url = `${HTTP_PREFIX}/workspaces/${workspaceId}/removeFirmware/${firmware_id}`
  const result = await request.get(url)
  return result.data
}