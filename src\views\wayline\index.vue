<template>
  <div class="project-app-wrapper" v-show="terrainLoaded">
    <!-- 左侧列表 -->
    <div class="left">
      <!-- 航线库列表 -->
      <div v-if="isChange" style="width: 100%;position: relative;">
        <div class="hearder">
          <div class="bor-b">
            <a-row>
              <a-col :span="4">
                <span style="margin-top: 4px;">航线库</span>
              </a-col>
              <a-col :span="10">
                <a-select v-model:value="selectType" :dropdown-match-select-width="false" style="width: 140px;">
                  <a-select-option value="">所有负载</a-select-option>
                  <a-select-option value="1-52-0,1-52-1,1-52-2,1-52-3">经纬 M30</a-select-option>
                  <a-select-option value="1-53-0,1-53-1,1-53-2,1-53-3">经纬 M30 T</a-select-option>
                  <a-select-option value="1-66-0,1-66-1,1-66-2,1-66-3"><PERSON><PERSON> ЗE</a-select-option>
                  <a-select-option value="1-67-0,1-67-1,1-67-2,1-67-3">Mavic ЗT</a-select-option>
                  <a-select-option value="1-80-0,1-80-1,1-80-2,1-80-3">Matrice 3D</a-select-option>
                  <a-select-option value="1-81-0,1-81-1,1-81-2,1-81-3">Matrice 3TD</a-select-option>
                  <a-select-option value="1-88-0,1-88-1,1-88-2,1-88-3">Matrice 4E</a-select-option>
                  <a-select-option value="1-89-0,1-89-1,1-89-2,1-89-3">Matrice 4T</a-select-option>
                  <a-select-option value="1-98-0,1-98-1,1-98-2,1-98-3">Matrice 4D</a-select-option>
                  <a-select-option value="1-99-0,1-99-1,1-99-2,1-99-3">Matrice 4TD</a-select-option>
                </a-select>
              </a-col>
              <a-col :span="2">
                <a-tooltip title="搜索航线">
                  <el-icon>
                    <Search @click="isShowSearch" style="margin-top: 16px;" />
                  </el-icon>
                </a-tooltip>
              </a-col>
              <a-col :span="2" style="font-size: 20px;cursor: pointer;" @click="Add" v-permission>
                <a-tooltip title="新增航线">
                  ＋
                </a-tooltip>
              </a-col>
              <a-col :span="2" style="cursor: pointer;" @click="() => { isImport = true }" v-permission>
                <a-tooltip title="导入航线">
                  <el-icon>
                    <DocumentAdd style="margin-top: 18px;margin-left: -24px;font-size: 16px;" />
                  </el-icon>
                </a-tooltip>
              </a-col>
            </a-row>
          </div>
          <a-row>
            <a-col :span="18" v-if="showSearch">
              <a-input v-model:value="inputName" placeholder="按航线名称搜索" class="searchInput" />
            </a-col>
          </a-row>
        </div>

        <div class="content" :style="showSearch ? 'margin-top: 76px;' : 'margin-top: 50px;'">
          <a-spin :spinning="loading" :delay="300" tip="downloading" size="large">
            <div class="scrollbar">
              <div id="data" class="height-100 uranus-scrollbar" v-if="waylinesData.data.length !== 0">
                <div v-for="(item, index) in waylinesData.data" :key="index">
                  <!-- 弹窗航点动作 -->
                  <a-tooltip placement="right" trigger="click">
                    <template v-if="item.template_types[0] === 0" #title>
                      <div>航点动作</div>
                      <ul v-if="activeIndex === index" class="tooltips-content">
                        <template v-for="(subItem, indexJ) in dataList">
                          <li :key="indexJ" v-if="subItem.action_list?.length">
                            <div class="tooltips-container">
                              <div class="index">{{ indexJ + 1 }}.</div>
                              <div class="item_list">
                                <div class="item" v-for="(item1, index1) in subItem.action_list" :key="index1">
                                  <div :class="`img_${item1.index}`" />
                                </div>
                              </div>
                            </div>
                          </li>
                        </template>
                      </ul>
                    </template>
                    <div class="wayline-panel" :class="{ 'blue-border': activeIndex === index }"
                      @click.stop="selectRoute(item, index)">
                      <div class="title">
                        <a-tooltip :title="item.name">
                          <div class="pr10"
                            style="width: 120px;white-space: nowrap;text-overflow: ellipsis;overflow: hidden;">
                            {{ item.name }}
                          </div>
                        </a-tooltip>
                        <div class="ml10">
                          <UserOutlined />
                        </div>
                        <a-tooltip :title="item.user_name">
                          <div class="ml5 pr5"
                            style="width: 90px;white-space: nowrap;text-overflow: ellipsis;overflow: hidden;">
                            {{ item.user_name }}
                          </div>
                        </a-tooltip>
                        <a-tooltip title="编辑航线">
                          <div style="width: 16px;height: 20px;margin-right: 10px;"
                            @click.stop="editWayline(item, index)" v-permission>
                            <EditPen />
                          </div>
                        </a-tooltip>
                        <div class="fz20" v-permission>
                          <a-dropdown>
                            <a style="color: white;">
                              <EllipsisOutlined />
                            </a>
                            <template #overlay>
                              <a-menu theme="dark" class="more" style="background: #3c3c3c;">
                                <a-menu-item @click.stop="downloadWayline(item.id, item.name)">
                                  <span>下载</span>
                                </a-menu-item>
                                <a-menu-item @click.stop="showWaylineTip(item.id)">
                                  <span>删除</span>
                                </a-menu-item>
                              </a-menu>
                            </template>
                          </a-dropdown>
                        </div>
                      </div>
                      <div class="ml10 mt5" style="color: hsla(0, 0%, 100%, 0.65);">
                        <span>
                          <RocketOutlined />
                        </span>
                        <span class="ml5">
                          {{ DEVICE_NAME[item.drone_model_key] }}
                        </span>
                        <span class="ml10">
                          <CameraFilled style="border-top: 1px solid;padding-top: -3px;" />
                        </span>
                        <span class="ml5" v-for="(payload, index) in item.payload_model_keys" :key="index">
                          {{ DEVICE_NAME[payload] }}
                        </span>
                      </div>
                      <div class="mt5 ml10 mr10 flex-row flex-align-center flex-justify-between"
                        style="color: hsla(0, 0%, 100%, 0.35);">
                        <span>
                          更新时间 {{ new Date(item.update_time).toLocaleString() }}
                        </span>
                        <a-tooltip v-if="item.template_types[0] === 0" title="航点航线">
                          <RiseOutlined />
                        </a-tooltip>
                        <a-tooltip v-if="item.template_types[0] === 1 || item.template_types[0] === 2" title="面状航线">
                          <RadarChartOutlined />
                        </a-tooltip>
                      </div>
                    </div>
                  </a-tooltip>
                </div>
              </div>

              <div v-else>
                <a-empty :image-style="{ height: '60px', marginTop: '60px' }" />
              </div>

              <ConfigProvider :locale="zh_CN">
                <a-modal v-model:visible="deleteTip" width="450px" :closable="false" :maskClosable="false" centered
                  :okButtonProps="{ danger: true }" @ok="deleteWayline">
                  <p class="pt10 pl20" style="height: 50px;">
                    航线文件一旦删除就无法恢复，是否确认删除？
                  </p>
                  <template #title>
                    <div class="flex-row flex-justify-center">
                      <span>删除</span>
                    </div>
                  </template>
                </a-modal>
              </ConfigProvider>
            </div>
          </a-spin>

          <div class="pagination">
            <el-pagination background size="small" pager-count="3" layout="prev, pager, next" :total="pagination.total"
              :page-size="pagination.page_size" @current-change="handleCurrentChange" />
          </div>
        </div>
      </div>

      <!-- 航线库新增或修改页面 -->
      <div v-else class="main-content uranus-scrollbar dark" style="position: relative;">
        <el-tooltip effect="dark" content="保存" placement="bottom-start">
          <el-icon size="20" style="position: absolute;top: 6px;" @click="save">
            <SetUp />
          </el-icon>
        </el-tooltip>

        <el-button type="primary" @click="showSet" class="updown">{{ textContent }}</el-button>

        <div class="close" @click="close">
          <el-icon>
            <CloseBold />
          </el-icon>
        </div>

        <!-- 航点航线 -->
        <p v-show="showSetting && ruleForm.routeType === 'waypoint'">
          <el-row style="margin-top: 10px;">
            <el-col :span="1"></el-col>
            <el-col :span="23">航线名称</el-col>
          </el-row>
          <el-row>
            <el-col :span="1"></el-col>
            <el-col :span="23">
              <el-form :model="Form" :rules="Rules" ref="formRef">
                <el-form-item prop="lineName">
                  <el-input v-model="Form.lineName" size="small" clearable style="width: 260px;"
                    placeholder="请输入航线名称" />
                </el-form-item>
              </el-form>
            </el-col>
          </el-row>
          <el-row v-if="showPhotoSetting">
            <el-col :span="1"></el-col>
            <el-col :span="23">拍照设置</el-col>
          </el-row>
          <el-row v-if="showPhotoSetting">
            <el-col :span="1"></el-col>
            <el-col :span="23">
              <el-checkbox-group size="small" v-model="photoSetting" @change="photoSettingChange">
                <el-checkbox-button v-for="(item, index) in photoSettingOption" :key="index" :label="item"
                  :value="item" />
              </el-checkbox-group>
            </el-col>
          </el-row>
          <!--<el-row style="margin-top: 10px;">
            <el-col :span="1"></el-col>
            <el-col :span="23">
              <el-radio-group v-model="verticalTilt" is-button size="small">
                <el-radio-button label="垂直爬升" value="safely"/>
                <el-radio-button label="倾斜爬升" value="pointToPoint"/>
              </el-radio-group>

              <el-tooltip effect="dark" placement="top">
                <template #content>
                  垂直爬升：飞行器爬升到航线起始点高度后，再飞向航线起始点。<br/><br/>
                  倾斜爬升：飞行器爬升到"安全起飞高度"后，再直线飞到航线起始点。<br/><br/>
                  安全起飞高度：是相对起飞点的高度值。<br/><br/>
                  飞行器起飞后，会先上升至"安全起飞高度"，再飞向航线起始点。
                </template>
                <el-icon>
                  <Warning/>
                </el-icon>
              </el-tooltip>
            </el-col>
          </el-row>-->
          <el-row>
            <el-col :span="1"></el-col>
            <el-col :span="11">垂直爬升高度</el-col>
          </el-row>
          <el-row>
            <el-col :span="1"></el-col>
            <el-col :span="23">
              <el-input-number v-model="takeOffSecurityHeight" size="small" :precision="1" :step="10" :min="2"
                :max="1500" />
              <span style="font-size: 16px;">m</span>
            </el-col>
          </el-row>
          <el-row style="margin-top: 10px;">
            <el-col :span="1"></el-col>
            <el-col :span="23">
              <el-radio-group v-model="heightSetting" is-button size="small">
                <el-radio-button label="绝对高度" value="EGM96" />
                <el-radio-button label="相对起飞点高度" value="relativeToStartPoint" />
                <el-radio-button label="相对地形高度" value="aboveGroundLevel" />
              </el-radio-group>

              <el-tooltip effect="dark" placement="top">
                <template #content>
                  绝对高度：航点高度值相对于海平面高度保持不变。<br /><br />
                  相对起飞点高度（ALT）：航点高度值相对起飞点的高度保持不变。<br /><br />
                  相对地形的高度（AGL）：航点高度值相对地形/模型高度保持不变。
                </template>
                <el-icon>
                  <Warning />
                </el-icon>
              </el-tooltip>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="1"></el-col>
            <el-col :span="23">
              <el-input-number v-model="globalHeight" size="small" :precision="1" :step="100" :min="0" :max="10000" />
              <span style="font-size: 16px;">m</span>
            </el-col>
          </el-row>
          <el-row style="margin-top: 10px;">
            <el-col :span="1"></el-col>
            <el-col :span="11">全局航线速度</el-col>
          </el-row>
          <el-row>
            <el-col :span="1"></el-col>
            <el-col :span="22">
              <el-input-number v-model="autoFlightSpeed" @change="watchSpeed" size="small" :precision="1" :step="1"
                :min="1" :max="15" />
              <span style="font-size: 16px;">m/s</span>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="1"></el-col>
            <el-col :span="11">起飞速度</el-col>
          </el-row>
          <el-row>
            <el-col :span="1"></el-col>
            <el-col :span="22">
              <el-input-number v-model="globalTransitionalSpeed" size="small" :precision="1" :step="1" :min="1"
                :max="15" />
              <span style="font-size: 16px;">m/s</span>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="1"></el-col>
            <el-col :span="11">航点类型</el-col>
          </el-row>
          <el-row>
            <el-col :span="1"></el-col>
            <el-col :span="22">
              <el-select v-model="destinationType" style="width: 250px;">
                <el-option v-for="(item, index) in destinationTypeOptions" :key="index" :label="item.label"
                  :value="item.value" />
              </el-select>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="1"></el-col>
            <el-col :span="8.5">飞行器偏航角模式</el-col>
            <el-tooltip effect="dark" placement="top">
              <template #content>
                沿航线方向：飞行器机头沿着航线方向飞至下一航点。<br /><br />
                手动控制：飞行器在飞至下一航点的过程中，用户可以手动控制飞行器机头朝向。<br /><br />
                锁定当前偏航角：飞行器机头保持执行完航点动作后的飞行器偏航角飞至下一航。
              </template>
              <el-icon>
                <Warning />
              </el-icon>
            </el-tooltip>
          </el-row>
          <el-row>
            <el-col :span="1"></el-col>
            <el-col :span="22">
              <el-select v-model="yawAngleMode" style="width: 250px;">
                <el-option v-for="(item, index) in yawAngleModeOptions" :key="index" :label="item.label"
                  :value="item.value" />
              </el-select>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="1"></el-col>
            <el-col :span="12.5">航点间云台俯仰角控制模式</el-col>
            <el-tooltip effect="dark" placement="top">
              <template #content>
                手动控制：飞行器从一个航点飞向下一个航点的过程中，支持用户手动控<br />制云台的俯仰角度；若无用户控制，则保持飞离航点时的云台俯仰角度。<br /><br />
                依照每个航点设置：飞行器从一个航点飞向下一个航点的过程中，云台俯仰角均匀过渡至下一个航点的俯仰角。
              </template>
              <el-icon>
                <Warning />
              </el-icon>
            </el-tooltip>
          </el-row>
          <el-row>
            <el-col :span="1"></el-col>
            <el-col :span="22">
              <el-select v-model="pitchAngleMode" style="width: 250px;">
                <el-option v-for="(item, index) in pitchAngleModeOptions" :key="index" :label="item.label"
                  :value="item.value" />
              </el-select>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="1"></el-col>
            <el-col :span="4.5">完成动作</el-col>
            <el-tooltip effect="dark" placement="top">
              <template #content>
                自动返航：飞行器航线任务完成后，立即飞向起飞点。若飞行器此时处于失联状态或<br />飞向起飞点的过程中飞行器失联，则立即执行失联行为。<br /><br />
                返回航线起始点悬停：飞行器航线任务完成后，立即飞向起始点（S点），若飞行器<br />此时处于失联状态或飞向起始点（S点）的过程中飞行器失联，则立即执行失联行为。<br /><br />
                退出航线模式：飞行器航线任务完成后，立即退出航线模式，并悬停在原点。若飞行<br />器此时处于失联状态，则立即执行失联行为。
                <!--原地降落：飞行器航线任务完成后，立即开始降落。若飞行器此时处于失联状态或在<br/>降落过程中失联，则立即执行失联行为。-->
              </template>
              <el-icon>
                <Warning />
              </el-icon>
            </el-tooltip>
          </el-row>
          <el-row>
            <el-col :span="1"></el-col>
            <el-col :span="22">
              <el-select v-model="completeTheAction" style="width: 250px;">
                <el-option v-for="(item, index) in completeTheActionOptions" :key="index" :label="item.label"
                  :value="item.value" />
              </el-select>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="1"></el-col>
            <el-col :span="11">失控是否继续执行航线</el-col>
          </el-row>
          <el-row>
            <el-col :span="1"></el-col>
            <el-col :span="22">
              <el-select v-model="isImplementRoute" style="width: 250px;">
                <el-option v-for="(item, index) in isImplementRouteOptions" :key="index" :label="item.label"
                  :value="item.value" />
              </el-select>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="1"></el-col>
            <el-col :span="11">失控动作类型</el-col>
          </el-row>
          <el-row>
            <el-col :span="1"></el-col>
            <el-col :span="22">
              <el-select v-model="haywireType" style="width: 250px;">
                <el-option v-for="(item, index) in haywireTypeOptions" :key="index" :label="item.label"
                  :value="item.value" />
              </el-select>
            </el-col>
          </el-row>
        </p>
        <!-- 面状航线 -->
        <p v-if="showSetting && ruleForm.routeType !== 'waypoint'">
          <facialForm :formData="formData" :cameraOptions="photoSettingOption" />
        </p>

        <div v-if="session.routeType === 'waypoint'">
          <el-row v-if="dataList.length > 1">
            <el-col :span="1"></el-col>
            <el-col :span="7">
              <div style="text-align: center;color: #CCC;">航线长度</div>
            </el-col>
            <el-col :span="8">
              <div style="text-align: center;color: #CCC;">预计执行时间</div>
            </el-col>
            <el-col :span="7">
              <div style="text-align: center;color: #CCC;">航点</div>
            </el-col>
          </el-row>
          <el-row v-if="dataList.length > 1">
            <el-col :span="1"></el-col>
            <el-col :span="7">
              <div style="text-align: center;">
                {{ timeData && timeData.distance ? timeData.distance + 'm' : '' }}
              </div>
            </el-col>
            <el-col :span="8">
              <div style="text-align: center;">
                {{ timeData && timeData.workTime ? timeData.workTime : '' }}
              </div>
            </el-col>
            <el-col :span="7">
              <div style="text-align: center;">
                {{ timeData && timeData.pointCount ? timeData.pointCount : '' }}
              </div>
            </el-col>
          </el-row>

          <!-- 航点列表 -->
          <ul v-if="session.routeType === 'waypoint'">
            <li class="list-item" :class="{ active: selectedItem == index }" v-for="(item, index) in dataList"
              :key="index" @click="pointClick(item, index)">
              <div class="container">
                <div class="index">{{ index + 1 }}</div>
                <div class="item_list">
                  <div class="item" v-for="(item1, index1) in dataList[index].action_list" :key="index1">
                    <div :class="`img_${item1.index}`" :style="getBorderStyle(index1, index)"
                      @click="singleClick(item1, index1, index)" />
                  </div>
                </div>
                <div class="button" @click.stop="delLinePoint(item)">X</div>
              </div>
            </li>
          </ul>
        </div>
      </div>
    </div>

    <!-- 右侧高德地图组件 -->
    <div class="right">
      <div class="map-wrapper">
        <div class="g-map-wrapper">
          <Amap :wgs84Center="[120.241176, 30.299719]" :placemark="placemark" :take0ffPoint="take0ffPoint"
            :Polygon="Polygon" :saData="saData" :isSet="changeSet" :zoom="14.5" :aerobatModel="session"
            @allPoints="handlePoints" @takeoffPoint="handleTakeoff" @pretreatment="pretreatment" />

          <div v-if="timeData && timeData.distance" class="leftManage">
            <div class="info-container" v-if="session.routeType !== 'waypoint'">
              <div class="info-item">
                <div class="info-title">面积</div>
                <div class="info-value">{{ timeData?.PolygonArea?.toFixed(2) + '㎡' }}</div>
              </div>
              <div class="info-item">
                <div class="info-title">航线长度</div>
                <div class="info-value">{{ timeData?.distance?.toFixed(1) + 'm' }}</div>
              </div>
              <div class="info-item">
                <div class="info-title">预计总时长</div>
                <div class="info-value">{{ formatSeconds(timeData?.time?.toFixed(0)) || 0 }}</div>
              </div>
              <div class="info-item">
                <div class="info-title">预计总照片数</div>
                <div class="info-value">{{ timeData?.pallcont || 0 }}</div>
              </div>
            </div>
            <div class="info-container" v-if="session.routeType === 'waypoint'">
              <div class="info-item">
                <div class="info-title">航线长度</div>
                <div class="info-value">{{ timeData?.distance + 'm' }}</div>
              </div>
              <div class="info-item">
                <div class="info-title">预计总时长</div>
                <div class="info-value">{{ timeData?.workTime || 0 }}</div>
              </div>
              <div class="info-item">
                <div class="info-title">航点</div>
                <div class="info-value">{{ timeData?.pointCount || 0 }}</div>
              </div>
            </div>
          </div>

          <div v-if="pointSetting" class="openOperate" :style="pointSetting1 ? 'right: 260px;' : 'right: 50px;'">
            <el-button type="primary" icon="More" circle @click="showMore" />
          </div>

          <div v-if="moreOperate" class="moreDiv" :style="pointSetting1 ? 'right: 300px;' : 'right: 82px;'">
            <div class="moreDiv_item" v-for="(item, index) in operations" :key="index"
              @click="optionClick(item, index)">
              <span>{{ item.label }}</span>
              <div :class="`img_${index + 1}`" />
            </div>
          </div>

          <div class="rightSet" v-if="pointSetting1">
            <div class="title" :style="{ borderBottom: operationtitle.label ? '1px solid #8d8a8a' : '' }">
              <span>{{ operationtitle.label }}</span>
              <el-icon v-if="operationtitle.label" style="cursor: pointer;" @click="deleteItem">
                <Delete />
              </el-icon>
            </div>
            <div class="main" v-if="operationtitle.label == '开始录像'">
              <a-input v-model:value="actionData.videoName1" placeholder="DJI_YYYYMMDDhhmm_XXX_" allow-clear
                style="width: 100%;background-color: #282626;" />
              <el-button :type="actionData.isActive1 ? 'primary' : 'info'" size="small"
                @click="() => actionData.isActive1 = !actionData.isActive1" style="margin: 10px 0;">
                跟随航线
              </el-button>
              <el-checkbox-group size="small" v-model="actionData.videoSetting1" @change="photoSettingChange"
                :disabled="actionData.isActive1">
                <el-checkbox-button v-for="(item, index) in photoSettingOption" :key="index" :label="item"
                  :value="item" />
              </el-checkbox-group>
            </div>

            <div class="main" v-if="operationtitle.label == '开始等时间隔拍照'">
              <div style="height: 30px;line-height: 30px;">间隔时间</div>
              <el-input-number v-model="actionData.intervalTime" size="small" :precision="0" :step="1" :min="1"
                :max="30" />
              s
              <br><br>
              <a-input v-model:value="actionData.videoName2" placeholder="DJI_YYYYMMDDhhmm_XXX_" allow-clear
                style="width: 100%;background-color: #282626;" />
              <el-button :type="actionData.isActive2 ? 'primary' : 'info'" size="small"
                @click="() => actionData.isActive2 = !actionData.isActive2" style="margin: 10px 0;">
                跟随航线
              </el-button>
              <el-checkbox-group size="small" v-model="actionData.videoSetting2" @change="photoSettingChange"
                :disabled="actionData.isActive2">
                <el-checkbox-button v-for="(item, index) in photoSettingOption" :key="index" :label="item"
                  :value="item" />
              </el-checkbox-group>
            </div>
            <div class="main" v-if="operationtitle.label == '开始等距间隔拍照'">
              <div style="height: 30px;line-height: 30px;">间隔距离</div>
              <el-input-number v-model="actionData.intervalDistance" size="small" :precision="0" :step="1" :min="1"
                :max="100" />
              m
              <br><br>
              <a-input v-model:value="actionData.videoName3" placeholder="DJI_YYYYMMDDhhmm_XXX_" allow-clear
                style="width: 100%;background-color: #282626;" />
              <el-button :type="actionData.isActive3 ? 'primary' : 'info'" size="small"
                @click="() => actionData.isActive3 = !actionData.isActive3" style="margin: 10px 0;">
                跟随航线
              </el-button>
              <el-checkbox-group size="small" v-model="actionData.videoSetting3" @change="photoSettingChange"
                :disabled="actionData.isActive3">
                <el-checkbox-button v-for="(item, index) in photoSettingOption" :key="index" :label="item"
                  :value="item" />
              </el-checkbox-group>
            </div>
            <div class="main" v-if="operationtitle.label == '悬停'">
              <div style="height: 30px;line-height: 30px;">悬停时间</div>
              <el-input-number v-model="actionData.hover_time" size="small" :precision="0" :step="1" :min="1"
                :max="900" />
              s
            </div>
            <div class="main" v-if="operationtitle.label == '飞行器偏航角'">
              <div style="height: 30px;line-height: 30px;">飞行器偏航角</div>
              <el-input-number v-model="actionData.aerocraftYawangle" size="small" :precision="0" :step="1" :min="-180"
                :max="180" />
              °
            </div>
            <div class="main" v-if="operationtitle.label == '云台偏航角'">
              <div style="height: 30px;line-height: 30px;">云台偏航角</div>
              <el-tooltip effect="dark" placement="top">
                <template #content>
                  顺时针旋转为+，逆时针旋转为-
                </template>
                <el-icon>
                  <Warning />
                </el-icon>
              </el-tooltip>
              <el-input-number v-model="actionData.yawAngle" size="small" :precision="0" :step="1" :min="-180"
                :max="180" />
              °
            </div>
            <div class="main" v-if="operationtitle.label == '云台俯仰角'">
              <div style="height: 30px;line-height: 30px;">云台俯仰角</div>
              <el-tooltip effect="dark" placement="top">
                <template #content>
                  平视为0，垂直向下为-90
                </template>
                <el-icon>
                  <Warning />
                </el-icon>
              </el-tooltip>
              <el-input-number v-model="actionData.pitchAngle" size="small" :precision="0" :step="1" :min="-120"
                :max="45" />
              °
            </div>
            <div class="main" v-if="operationtitle.label == '拍照'">
              <a-input v-model:value="actionData.photoName" placeholder="DJI_YYYYMMDDhhmm_XXX_" allow-clear
                style="width: 100%;background-color: #282626;" />
              <el-button :type="actionData.isActive4 ? 'primary' : 'info'" size="small"
                @click="() => actionData.isActive4 = !actionData.isActive4" style="margin: 10px 0;">
                跟随航线
              </el-button>
              <el-checkbox-group size="small" v-model="actionData.photoSetting" @change="photoSettingChange"
                :disabled="actionData.isActive4">
                <el-checkbox-button v-for="(item, index) in photoSettingOption" :key="index" :label="item"
                  :value="item" />
              </el-checkbox-group>
            </div>
            <div class="main" v-if="operationtitle.label == '相机变焦'">
              <div style="height: 30px;line-height: 30px;">相机变焦</div>
              <el-input-number v-model="actionData.cameraZoom" size="small" :precision="0" :step="1" />
              <el-icon>
                <CloseBold />
              </el-icon>
            </div>
            <div class="main" v-if="operationtitle.label == '创建文件夹'">
              <div style="height: 30px;line-height: 30px;">新文件夹名称</div>
              <a-input v-model:value="actionData.folderName" placeholder="DJI_YYYYMMDDhhmm_XXX_" allow-clear
                style="width: 100%;background-color: #282626;" />
            </div>
            <div class="main" v-if="operationtitle.label == '全景拍照'">
              <el-button :type="actionData.isActive5 ? 'primary' : 'info'" size="small"
                @click="() => actionData.isActive5 = !actionData.isActive5" style="margin: 10px 0;">
                跟随航线
              </el-button>
              <el-checkbox-group size="small" v-model="actionData.panoramaSetting" @change="photoSettingChange"
                :disabled="actionData.isActive5">
                <el-checkbox-button v-for="(item, index) in photoSettingOption" :key="index" :label="item"
                  :value="item" />
              </el-checkbox-group>
            </div>
          </div>

          <div class="pointParams" v-if="pointSetting2">
            <div class="closeIcon" @click="closePointParams">
              <el-icon>
                <CloseBold />
              </el-icon>
            </div>
            <div class="title">航点 {{ selectedItem + 1 }}</div>
            <el-row style="margin-top: 10px;">
              <el-col :span="1"></el-col>
              <el-col :span="12">
                <span style="font-size: 14px;">速度(m/s)</span>
              </el-col>
              <el-col :span="10">
                <el-button :type="operationData.use_global_speed ? 'primary' : 'info'" size="small" style="width: 80px;"
                  @click="() => operationData.use_global_speed = !operationData.use_global_speed">
                  跟随航线
                </el-button>
              </el-col>
            </el-row>
            <el-row style="margin-bottom: 10px;padding-bottom: 10px;border-bottom: 1px solid #ccc;">
              <el-col :span="5"></el-col>
              <el-input-number v-model="operationData.waypoint_speed" :disabled="operationData.use_global_speed"
                size="small" :step="1" :min="1" :max="15" />
            </el-row>

            <el-row>
              <el-col :span="1"></el-col>
              <el-col :span="12">
                <span style="font-size: 14px;">航点高度(m)</span>
              </el-col>
              <el-col :span="10">
                <el-button :type="operationData.use_global_height ? 'primary' : 'info'" size="small"
                  style="width: 80px;"
                  @click="() => operationData.use_global_height = !operationData.use_global_height">
                  跟随航线
                </el-button>
              </el-col>
            </el-row>
            <el-row style="margin-bottom: 10px;padding-bottom: 10px;border-bottom: 1px solid #ccc;">
              <el-col :span="5"></el-col>
              <el-input-number v-model="operationData.height" :disabled="operationData.use_global_height" size="small"
                :step="10" :min="-1500" :max="1500" />
            </el-row>

            <el-row>
              <el-col :span="1"></el-col>
              <el-col :span="12">
                <span style="font-size: 14px;">偏航角模式(°)</span>
              </el-col>
              <el-col :span="10">
                <el-button :type="operationData.use_global_heading_param ? 'primary' : 'info'" size="small"
                  style="width: 80px;"
                  @click="() => operationData.use_global_heading_param = !operationData.use_global_heading_param">
                  跟随航线
                </el-button>
              </el-col>
            </el-row>
            <el-row style="margin-bottom: 10px;padding-bottom: 10px;border-bottom: 1px solid #ccc;">
              <el-col :span="1"></el-col>
              <el-select v-model="operationData.waypoint_heading_param.waypoint_heading_mode" style="width: 200px;"
                :disabled="operationData.use_global_heading_param">
                <el-option v-for="(item, index) in yawAngleModeOptions" :key="index" :label="item.label"
                  :value="item.value" />
              </el-select>
              <div style="margin-top: 6px;">
                <el-input-number
                  v-if="operationData.waypoint_heading_param.waypoint_heading_mode == 'smoothTransition' && !operationData.use_global_heading_param"
                  v-model="operationData.waypoint_heading_param.waypoint_heading_angle" size="small" :step="10"
                  :min="-180" :max="180" style="margin-left: 52px;" />
              </div>
            </el-row>

            <el-row>
              <el-col :span="1"></el-col>
              <el-col :span="12">
                <span style="font-size: 14px;">航点类型</span>
              </el-col>
              <el-col :span="10">
                <el-button :type="operationData.use_global_turn_param ? 'primary' : 'info'" size="small"
                  style="width: 80px;"
                  @click="() => operationData.use_global_turn_param = !operationData.use_global_turn_param">
                  跟随航线
                </el-button>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="1"></el-col>
              <el-select v-model="operationData.waypoint_turn_param.waypoint_turn_mode" style="width: 200px;"
                :disabled="operationData.use_global_turn_param">
                <el-option v-for="(item, index) in destinationTypeOptions" :key="index" :label="item.label"
                  :value="item.value" />
              </el-select>
            </el-row>
            <el-row
              v-if="operationData.waypoint_turn_param.waypoint_turn_mode == 'toPointAndPassWithContinuityCurvature' && !operationData.use_global_turn_param ||
                operationData.waypoint_turn_param.waypoint_turn_mode == 'coordinateTurn' && !operationData.use_global_turn_param"
              style="margin-top: 10px;">
              <el-col :span="1"></el-col>
              <el-col :span="9">
                <span style="font-size: 14px;">转弯截距(m)</span>
              </el-col>
              <el-col :span="10">
                <el-input-number v-model="operationData.waypoint_turn_param.waypoint_turn_damping_dist" size="small"
                  :step="1" :min="1" :max="100" />
              </el-col>
            </el-row>
          </div>
        </div>
      </div>
    </div>

    <!-- 新增航线弹窗 -->
    <el-dialog v-model="isAdd" title="创建新航线" width="800" :close-on-click-modal="false">
      <el-form ref="ruleFormRef" style="max-width: 600px" :model="ruleForm" :rules="rules" label-width="auto"
        class="demo-ruleForm" status-icon>
        <el-form-item label="航线类型" prop="routeType">
          <el-segmented v-model="ruleForm.routeType" :options="routeTypeOptions" @change="updateAerocraft" />
        </el-form-item>
        <el-form-item label="选择飞行器" prop="aerocraft">
          <el-segmented v-model="ruleForm.aerocraft" :options="aerocraftOptions" @change="updateModel" />
        </el-form-item>
        <el-form-item label="选择型号" prop="model">
          <el-segmented v-model="ruleForm.model" :options="modelOptions" />
        </el-form-item>
        <el-form-item label="航线名称" prop="name" style="width: 470px;">
          <el-input v-model="ruleForm.name" />
        </el-form-item>
        <el-form-item style="margin-left: 400px;">
          <el-button style="width: 70px;" @click="() => { isAdd = false; }">取消</el-button>
          <el-button style="width: 70px;" type="primary" @click="submitForm(ruleFormRef)">确定</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>

    <!-- 导入航线弹窗 -->
    <a-modal :visible="isImport" title="导入航线数据" :closable="false" @cancel="onCancel" @ok="uploadFile"
      :style="{ top: '200px' }">
      <a-upload :multiple="false" :before-upload="beforeUpload" :show-upload-list="true" :file-list="fileList"
        :remove="removeFile" accept=".kmz">
        <a-button type="primary">
          <UploadOutlined />
          点击上传
        </a-button>
      </a-upload>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
import store from '@/store';
import EventBus from '@/event-bus';
import { message } from 'ant-design-vue';
import { ELocalStorageKey } from '@/types';
import { DEVICE_NAME } from '@/types/device';
import { WaylineFile } from '@/types/wayline';
import { ConfigProvider } from 'ant-design-vue';
import type { FormInstance } from 'element-plus';
import Amap from '@/components/djimap/index.vue';
import { useGMapCover } from '@/hooks/use-c-map-cover';
import zh_CN from 'ant-design-vue/lib/locale-provider/zh_CN';
import { gcj02towgs84, wgs84togcj02 } from '@/vendors/coordtransform';
import { onMounted, ref, reactive, watch, onBeforeUnmount, computed } from 'vue';
import { EllipsisOutlined, RocketOutlined, CameraFilled, UserOutlined, UploadOutlined, RiseOutlined, RadarChartOutlined } from '@ant-design/icons-vue';
import {
  deleteWaylineFile, downloadWaylineFile, getWaylineFiles, addWayline,
  getWayLineDetail, wayLineCount, rebuildWayLine, importFirmareFile, preCreateMapping2dWayLine, createWayLineKmzMapping2, reBuildWayLineMapping2d, getRouteTypeOptions
} from '@/api/wayline';
import {
  matching, convert, converts, destinationTypeOptions, yawAngleModeOptions, haywireTypeOptions,
  pitchAngleModeOptions, completeTheActionOptions, isImplementRouteOptions, photoTypeSelect, cameraTypeSelect
} from '@/utils/common';
import facialForm from './facialForm.vue';
import { debounce } from 'lodash-es'
import { deepClone } from '@/utils/util';
import { getWorkspaceId } from '@/utils/storage'
let placemark = ref([]);
const fileList = ref([]);
const isAdd = ref(false);
const isImport = ref(false);
/**左侧航线库列表 */
const isChange = ref(true);
const loading = ref(false);
/**显示上右侧更多按钮 */
const pointSetting = ref(false);
const pointSetting1 = ref(false);
const pointSetting2 = ref(false);
let showPhotoSetting = ref(true);
const inputName = ref('');
const selectType = ref('');
const pagination = ref({
  page: 1,
  total: null,
  page_size: 8,
});

let useGMapCoverHook = useGMapCover();
store.commit('MAP_TYPE', false);
store.commit('LAYER_MAP_TYPE', false);

onMounted(() => {
  getWaylines();
})
const terrainLoaded = computed(() => store.state.common.terrainLoaded)

onBeforeUnmount(() => {
  EventBus.emit('lineClose');
  for (const id in store.state.dock.coverMap) {
    useGMapCoverHook.removeCoverFromMap(id)
  }
});

function beforeUpload(file: any) {

  if (!file.name || !file.name?.endsWith('.kmz')) {
    message.error('请上传 kmz 格式文件！')
    return false
  }
  fileList.value = [file]
  return false
}

function removeFile() {
  fileList.value = []
}

function onCancel() {
  fileList.value = []
  isImport.value = false
}

const uploadFile = async () => {
  if (fileList.value.length === 0) {
    message.error('请上传航线文件！')
  }
  const file = fileList.value[0]
  const fileData = new FormData()
  fileData.append('file', file, file.name)
  const res = await importFirmareFile(fileData)
  if (res.code === 200) {
    message.success('导入航线成功!');
    isImport.value = false;
    getWaylines();
    setTimeout(() => {
      selectRoute(waylinesData.data[0], 0)
    }, 500);
  }
  fileList.value = []
}

let moreOperate = ref(false)
const showMore = () => {
  moreOperate.value = !moreOperate.value
}

const showSearch = ref(false)
const isShowSearch = () => {
  showSearch.value = !showSearch.value
}

const operations = ref([
  { label: '开始录像', value: 1 },
  { label: '停止录像', value: 2 },
  { label: '开始等时间隔拍照', value: 3 },
  { label: '开始等距间隔拍照', value: 4 },
  { label: '结束间隔拍照', value: 5 },
  { label: '悬停', value: 6 },
  { label: '飞行器偏航角', value: 7 },
  { label: '云台偏航角', value: 8 },
  { label: '云台俯仰角', value: 9 },
  { label: '拍照', value: 10 },
  { label: '相机变焦', value: 11 },
  { label: '创建文件夹', value: 12 },
])
const rightIndex = ref(null)
const operationtitle = ref({})
const actionData = ref({
  videoName1: '',
  videoName2: '',
  videoName3: '',
  videoSetting1: [],
  videoSetting2: [],
  videoSetting3: [],
  isActive1: true,
  isActive2: true,
  isActive3: true,
  isActive4: true,
  isActive5: true,
  intervalTime: 3,
  intervalDistance: 10,
  hover_time: 10,
  aerocraftYawangle: 0,
  yawAngle: 0,
  pitchAngle: 0,
  photoName: '',
  photoSetting: [],
  cameraZoom: 5,
  folderName: '',
  panoramaSetting: [],

})
const operationData = ref({

  use_global_height: true,
  use_global_speed: true,
  use_global_heading_param: true,
  use_global_turn_param: true,
  height: 100,
  waypoint_speed: 5,
  waypoint_turn_param: {
    waypoint_turn_mode: 'coordinateTurn',
    waypoint_turn_damping_dist: 2
  },
  waypoint_heading_param: {
    waypoint_heading_mode: 'followWayline',
    waypoint_heading_angle: 100
  }
})
// 整合所有表单数据到一个对象中
const formData = ref({});

let selectedItem = ref(null) //左侧li当前下标
let selectedItemIndex = ref<null | number>() //左侧li里面的下标
let changeSet = ref(false)
let dataList = ref([])

watch(inputName, () => {
  getWaylines();
})

watch(selectType, () => {
  getWaylines();
})

watch(operationData, () => {
  console.log("参数赋值----")
  pointConfig()
}, { deep: true }
);
watch(actionData, () => {
  console.log("参数赋值---111-动作")
  dataList.value[selectedItem.value].action_list[selectedItemIndex.value] = indexDict(rightIndex.value + 1)
}, { deep: true }
)


// 只监听影响航线预览的关键属性
watch(
  () => [
    formData.value.device_mode,
    formData.value.global_height,
    formData.value.auto_flight_speed,
    formData.value.height_mode,
    formData.value.elevation_optimize_enable,
    formData.value.shoot_type,
    formData.value.direction,
    formData.value.margin,
    formData.value.overlap,
    formData.value.GSD,
    formData.value.template_type,
    formData.value.oblique_gsd,
    formData.value.inclined_flight_speed,
    formData.value.inclined_gimbal_pitch,
  ],
  debounce((newVal, oldVal) => {
    if (changeSet.value && !isUpdating.value) {
      // 设置标志位，防止循环调用
      isUpdating.value = true
      pretreatment(Polygon.value, newVal, oldVal)
      // 操作完成后重置标志位
      setTimeout(() => {
        isUpdating.value = false
      }, 500)
    }
  }, 300),
  { deep: true }
)

const pointConfig = () => {
  dataList.value[selectedItem.value].use_global_height = operationData.value.use_global_height ? 1 : 0
  dataList.value[selectedItem.value].use_global_speed = operationData.value.use_global_speed ? 1 : 0
  dataList.value[selectedItem.value].use_global_heading_param = operationData.value.use_global_heading_param ? 1 : 0
  dataList.value[selectedItem.value].use_global_turn_param = operationData.value.use_global_turn_param ? 1 : 0
  dataList.value[selectedItem.value].height = operationData.value.height
  dataList.value[selectedItem.value].waypoint_speed = operationData.value.waypoint_speed
  //更新航点高度
  placemark.value[0][selectedItem.value][2] = operationData.value.height

  let a1 = operationData.value.waypoint_turn_param.waypoint_turn_mode
  let b1 = operationData.value.use_global_turn_param
  if (b1) {
    delete dataList.value[selectedItem.value].waypoint_turn_param
  } else {
    dataList.value[selectedItem.value].waypoint_turn_param = {
      waypoint_turn_mode: a1,
      waypoint_turn_damping_dist: (a1 == 'coordinateTurn' || a1 == 'toPointAndPassWithContinuityCurvature') ? operationData.value.waypoint_turn_param.waypoint_turn_damping_dist : 0
    }
  }

  let a2 = operationData.value.waypoint_heading_param.waypoint_heading_mode
  let b2 = operationData.value.use_global_heading_param
  if (b2) {
    delete dataList.value[selectedItem.value].waypoint_heading_param
  } else {
    dataList.value[selectedItem.value].waypoint_heading_param = {
      waypoint_heading_mode: a2,
      waypoint_heading_angle: (a2 == 'smoothTransition') ? operationData.value.waypoint_heading_param.waypoint_heading_angle : 0
    }
  }
}

const echoConfig = (item: any, index: number) => {
  operationData.value.use_global_height = item.use_global_height == undefined ? true : item.use_global_height == 1
  operationData.value.use_global_speed = item.use_global_speed == undefined ? true : item.use_global_speed == 1
  operationData.value.use_global_heading_param = item.use_global_heading_param == undefined ? true : item.use_global_heading_param == 1
  operationData.value.use_global_turn_param = item.use_global_turn_param == undefined ? true : item.use_global_turn_param == 1
  operationData.value.height = item.height == undefined ? 100 : item.height
  operationData.value.waypoint_speed = item.waypoint_speed == undefined ? 5 : item.waypoint_speed

  const turnParam = item?.waypoint_turn_param
  operationData.value.waypoint_turn_param = {
    waypoint_turn_mode: turnParam?.waypoint_turn_mode || 'coordinateTurn',
    waypoint_turn_damping_dist: turnParam.waypoint_turn_damping_dist ? turnParam.waypoint_turn_damping_dist : 2,
  }

  const headingParam = item?.waypoint_heading_param
  operationData.value.waypoint_heading_param = {
    waypoint_heading_mode: headingParam?.waypoint_heading_mode || 'followWayline',
    waypoint_heading_angle: headingParam.waypoint_heading_angle ? headingParam.waypoint_heading_angle : 100,
  }
}

const optionClick = (item: any, index: any) => {
  // 左侧航点索引不为空
  if (selectedItem.value != null) {
    pointSetting1.value = true // 显示右侧动作面板
    rightIndex.value = index
    operationtitle.value = item
    if (dataList.value[selectedItem.value].action_list.length < 10) {
      console.log("添加动作:索引:" + selectedItem.value)
      dataList.value[selectedItem.value].action_list.push(indexDict(index + 1))
      // selectedItemIndex.value = dataList.value[selectedItem.value].action_list.length - 1
    } else {
      message.error('单个航点关联动作不能超过10个！');
    }
  } else {
    message.error('请在左侧选择其中一个航点！');
  }
}

function getBorderStyle(index1: any, index2: any) {
  return {
    border: index1 == selectedItemIndex.value && selectedItem.value == index2 ? '1px solid #FFF' : 'none'
  }
}

const singleClick = (item: any, actionIndex: number, pointIndex: number) => {
  selectedItem.value = pointIndex
  selectedItemIndex.value = actionIndex
  let labels = ''
  operations.value.forEach(i => {
    if (i.value == item.index) {
      labels = i.label
    }
  })
  operationtitle.value = {
    label: labels,
    value: item.action_actuator_func
  }
  echo(item)
  rightIndex.value = Number(item.index) - 1
  pointSetting1.value = true // 显示右侧动作面板
}

function echo(obj: any) {
  switch (Number(obj.index)) {
    case 1: {
      actionData.value.videoName1 = obj.action_actuator_func_params.file_suffix
      actionData.value.videoSetting1 = converts(obj.action_actuator_func_params.payload_lens_index)
      actionData.value.isActive1 = Boolean(obj.action_actuator_func_params.use_global_payload_lens_index)
      break;
    }
    case 3: {
      actionData.value.intervalTime = obj.action_actuator_func_params.action_trigger_param
      actionData.value.videoName2 = obj.action_actuator_func_params.file_suffix
      actionData.value.videoSetting2 = converts(obj.action_actuator_func_params.payload_lens_index)
      actionData.value.isActive2 = Boolean(obj.action_actuator_func_params.use_global_payload_lens_index)
      break;
    }
    case 4: {
      actionData.value.intervalDistance = obj.action_actuator_func_params.action_trigger_param
      actionData.value.videoName3 = obj.action_actuator_func_params.file_suffix
      actionData.value.videoSetting3 = converts(obj.action_actuator_func_params.payload_lens_index)
      actionData.value.isActive3 = Boolean(obj.action_actuator_func_params.use_global_payload_lens_index)
      break;
    }
    case 6: {
      actionData.value.hover_time = obj.action_actuator_func_params.hover_time
      break;
    }
    case 7: {
      actionData.value.aerocraftYawangle = obj.action_actuator_func_params.aircraft_heading
      break;
    }
    case 8: {
      actionData.value.yawAngle = obj.action_actuator_func_params.gimbal_yaw_rotate_angle
      break;
    }
    case 9: {
      actionData.value.pitchAngle = obj.action_actuator_func_params.gimbal_pitch_rotate_angle
      break;
    }
    case 10: {
      actionData.value.photoName = obj.action_actuator_func_params.file_suffix
      actionData.value.photoSetting = converts(obj.action_actuator_func_params.payload_lens_index)
      actionData.value.isActive4 = Boolean(obj.action_actuator_func_params.use_global_payload_lens_index)
      break;
    }
    case 11: {
      actionData.value.cameraZoom = obj.action_actuator_func_params.focal_length
      break;
    }
    case 12: {
      actionData.value.folderName = obj.action_actuator_func_params.directory_name
      break;
    }
    case 13: {
      actionData.value.panoramaSetting = converts(obj.action_actuator_func_params.payload_lens_index)
      actionData.value.isActive5 = Boolean(obj.action_actuator_func_params.use_global_payload_lens_index)
      break;
    }
    default:
      break;
  }
}

function indexDict(i: any) {
  let obj = {
    action_actuator_func: '',
    action_actuator_func_params: {},
    index: i
  }
  switch (i) {
    case 1: {
      obj.action_actuator_func = '1'
      obj.action_actuator_func_params = {
        file_suffix: actionData.value.videoName1,
        payload_lens_index: actionData.value.isActive1 ? convert(photoSetting.value).join(',') : convert(actionData.value.videoSetting1).join(','),
        use_global_payload_lens_index: Number(actionData.value.isActive1),
      }
      break;
    }
    case 2: {
      obj.action_actuator_func = '2'
      delete obj.action_actuator_func_params
      break;
    }
    case 3: {
      obj.action_actuator_func = '3'
      obj.action_actuator_func_params = {
        action_trigger_param: actionData.value.intervalTime,
        file_suffix: actionData.value.videoName2,
        payload_lens_index: actionData.value.isActive2 ? convert(photoSetting.value).join(',') : convert(actionData.value.videoSetting2).join(','),
        use_global_payload_lens_index: Number(actionData.value.isActive2),
      }
      break;
    }
    case 4: {
      obj.action_actuator_func = '4'
      obj.action_actuator_func_params = {
        action_trigger_param: actionData.value.intervalDistance,
        file_suffix: actionData.value.videoName3,
        payload_lens_index: actionData.value.isActive3 ? convert(photoSetting.value).join(',') : convert(actionData.value.videoSetting3).join(','),
        use_global_payload_lens_index: Number(actionData.value.isActive3),
      }
      break;
    }
    case 5: {
      obj.action_actuator_func = '5'
      delete obj.action_actuator_func_params
      break;
    }
    case 6: {
      obj.action_actuator_func = '6'
      obj.action_actuator_func_params = {
        hover_time: actionData.value.hover_time,
      }
      break;
    }
    case 7: {
      obj.action_actuator_func = '7'
      obj.action_actuator_func_params = {
        aircraft_heading: actionData.value.aerocraftYawangle,
      }
      break;
    }
    case 8: {
      obj.action_actuator_func = '8'
      obj.action_actuator_func_params = {
        gimbal_yaw_rotate_enable: 1,
        gimbal_yaw_rotate_angle: actionData.value.yawAngle,
      }
      break;
    }
    case 9: {
      obj.action_actuator_func = '8'
      obj.action_actuator_func_params = {
        gimbal_pitch_rotate_enable: 1,
        gimbal_pitch_rotate_angle: actionData.value.pitchAngle,
      }
      break;
    }
    case 10: {
      obj.action_actuator_func = '9'
      obj.action_actuator_func_params = {
        file_suffix: actionData.value.photoName,
        payload_lens_index: actionData.value.isActive4 ? convert(photoSetting.value).join(',') : convert(actionData.value.photoSetting).join(','),
        use_global_payload_lens_index: Number(actionData.value.isActive4),
      }
      break;
    }
    case 11: {
      obj.action_actuator_func = '10'
      obj.action_actuator_func_params = {
        focal_length: actionData.value.cameraZoom,
      }
      break;
    }
    case 12: {
      obj.action_actuator_func = '11'
      obj.action_actuator_func_params = {
        directory_name: actionData.value.folderName,
      }
      break;
    }
    case 13: {
      obj.action_actuator_func = '12'
      obj.action_actuator_func_params = {
        payload_lens_index: actionData.value.isActive5 ? convert(photoSetting.value).join(',') : convert(actionData.value.panoramaSetting).join(','),
        use_global_payload_lens_index: Number(actionData.value.isActive5),
      }
      break;
    }
    default:
      break;
  }
  return obj
}

function deleteItem() {
  let arr = dataList.value[selectedItem.value].action_list
  dataList.value[selectedItem.value].action_list = arr.slice(0, selectedItemIndex.value).concat(arr.slice(selectedItemIndex.value + 1))
  operationtitle.value = {}
  selectedItem.value = null
  selectedItemIndex.value = null
}

const handleCurrentChange = (val: any) => {
  pagination.value.page = val;
  getWaylines();
};

const session = ref({});
const formRef = ref(null);
const Form = ref({ lineName: '' });
const Rules = {
  lineName: [
    { required: true, message: '请输入航线名称', trigger: 'blur' },
    { pattern: /^[a-zA-Z0-9\u4e00-\u9fa5]{3,12}$/, message: '长度为3-12位，请勿包含以下字符：< > : " / | ? * . _', trigger: 'blur' }
  ]
};

let showSetting = ref(false);
let textContent = ref('航线设置 ▲')
const showSet = () => {
  showSetting.value = !showSetting.value
  textContent.value = showSetting.value ? '航线设置 ▲' : '航线设置 ▼'
}

function parsePoint(pointStr: any) {
  let coords = pointStr.split(',');
  return [parseFloat(coords[0]), parseFloat(coords[1])];
}

let waylineId = ref('')
let takeoff_Point = ref('')

let timeData = ref({})
let saData = ref({})
const handlePoints = (val: any) => {
  // 如果是添加新点
  if (val.length > dataList.value.length) {
    let parts = val[val.length - 1].point.split(',');
    let lng = parts[0];
    let lat = parts[1];
    // let point = gcj02towgs84(lng, lat).join(',');
    let point = `${lng}, ${lat}`
    dataList.value.push({ point: point, height: parts[2] || 0, action_list: [] });
  }
  // 如果是更新现有点位（拖拽后）
  else if (val.length === dataList.value.length && val.length > 0) {
    // 更新所有点位坐标
    for (let i = 0; i < val.length; i++) {
      let parts = val[i].point.split(',');
      let lng = parts[0];
      let lat = parts[1];
      // let point = gcj02towgs84(lng, lat).join(',');
      let point = `${lng}, ${lat}`;
      dataList.value[i].point = point;
    }
  }

  if (val.length > 1) {
    let params = {
      fly_to_wayline_mode: verticalTilt.value, //垂直或倾斜爬升 verticalTilt.value
      take_off_security_height: takeOffSecurityHeight.value, //安全起飞高度垂直爬升倾斜爬升的高度
      height_mode: heightSetting.value, //绝对高度/相对起飞点高度/相对地形高度
      global_height: globalHeight.value, //全局航线高度（相对起飞点高度）
      auto_flight_speed: autoFlightSpeed.value, //全局航线飞行速度
      global_transitional_speed: globalTransitionalSpeed.value, //起飞速度
      global_waypoint_turn_mode: destinationType.value, //航点类型
      waypoint_heading_mode: yawAngleMode.value, //偏航角模式
      gimbal_pitch_mode: pitchAngleMode.value, //航点间云台俯仰角控制模式
      finish_action: completeTheAction.value, //完成动作
      placemark: dataList.value, //模版元素
      take_off_ref_point: takeoff_Point.value, //参考起飞点
      exit_on_rc_lost: isImplementRoute.value, //失控是否继续执行航线
      execute_rc_lost_action: haywireType.value, //失控动作类型
    }
    if (params.exit_on_rc_lost == 'goContinue') {
      delete params.execute_rc_lost_action
    }
    if (session.value.routeType === 'waypoint') {
      wayLineCount(params).then((res) => {
        timeData.value = res.data
      })
    }
  }
}

const watchSpeed = () => {
  if (dataList.value.length > 1) {
    let val = dataList.value
    let arr = val.map(item => {
      return {
        point: parsePoint(item.point).join(','),
        is_risky: item.is_risky,
        use_global_height: item.use_global_height,
        use_global_speed: item.use_global_speed,
        use_global_heading_param: item.use_global_heading_param,
        use_global_turn_param: item.use_global_turn_param,
        use_straight_line: item.use_straight_line,
        action_list: item.action_list,
      };
    });
    dataList.value = arr
    if (val.length > 1) {
      let params = {
        fly_to_wayline_mode: verticalTilt.value, //垂直或倾斜爬升 verticalTilt.value
        take_off_security_height: takeOffSecurityHeight.value, //安全起飞高度垂直爬升倾斜爬升的高度
        height_mode: heightSetting.value, //绝对高度/相对起飞点高度/相对地形高度
        global_height: globalHeight.value, //全局航线高度（相对起飞点高度）
        auto_flight_speed: autoFlightSpeed.value, //全局航线飞行速度
        global_transitional_speed: globalTransitionalSpeed.value, //起飞速度
        global_waypoint_turn_mode: destinationType.value, //航点类型
        waypoint_heading_mode: yawAngleMode.value, //偏航角模式
        gimbal_pitch_mode: pitchAngleMode.value, //航点间云台俯仰角控制模式
        finish_action: completeTheAction.value, //完成动作
        placemark: arr, //模版元素
        take_off_ref_point: takeoff_Point.value, //参考起飞点
        exit_on_rc_lost: isImplementRoute.value, //失控是否继续执行航线
        execute_rc_lost_action: haywireType.value, //失控动作类型
      }
      if (params.exit_on_rc_lost == 'goContinue') {
        delete params.execute_rc_lost_action
      }
      wayLineCount(params).then((res) => {
        timeData.value = res.data
      })
    }
  } else {
    timeData.value = {
      timeData: 0,
      workTime: 0,
      pointCount: 0,
    }
  }
}

const closePointParams = () => {
  pointSetting2.value = false
}

const handleTakeoff = (value: any) => {
  if (value.length == 0) {
    takeoff_Point.value = '';
  } else {
    // takeoff_Point.value = gcj02towgs84(value[1], value[0]).join(',') + ",15.676564";
    takeoff_Point.value = `${value[1]}, ${value[0]}, ${value[2]}`;

    // 如果有任何点位，更新测量数据
    if (dataList.value.length > 1) {
      let params = {
        fly_to_wayline_mode: verticalTilt.value,
        take_off_security_height: takeOffSecurityHeight.value,
        height_mode: heightSetting.value,
        global_height: globalHeight.value,
        auto_flight_speed: autoFlightSpeed.value,
        global_transitional_speed: globalTransitionalSpeed.value,
        global_waypoint_turn_mode: destinationType.value,
        waypoint_heading_mode: yawAngleMode.value,
        gimbal_pitch_mode: pitchAngleMode.value,
        finish_action: completeTheAction.value,
        placemark: dataList.value,
        take_off_ref_point: takeoff_Point.value,
        exit_on_rc_lost: isImplementRoute.value,
        execute_rc_lost_action: haywireType.value,
      }
      if (params.exit_on_rc_lost == 'goContinue') {
        delete params.execute_rc_lost_action
      }
      if (session.value.routeType === 'waypoint') {
        wayLineCount(params).then((res) => {
          timeData.value = res.data
        })
      }
    }
  }
}

const Polygon = ref('')
const pretreatment = (val, newVal, oldVal) => {
  if (session.value.routeType === 'waypoint') {
    return
  }

  console.log(val);
  if (!val || val?.length === 0) {
    Polygon.value = ''
    return
  }
  Polygon.value = val

  const pretreatmentParams = handleParams(Polygon.value, newVal, oldVal)
  formData.value.placemark = pretreatmentParams.placemark

  placemark.value = []
  preCreateMapping2dWayLine(pretreatmentParams).then((res) => {
    res.data.latLngs.forEach(item => {
      const point = item.map(item => [item.lon, item.lat, parseFloat(res.data.height)]);
      placemark.value.push(point);
    });
    saData.value = res.data.sa_data
    timeData.value = {
      PolygonArea: res.data.PolygonArea,
      time: res.data.time,
      pallcont: res.data.pallcont,
      distance: res.data.distance,
    }
    // 只在这里更新formData，避免触发watch导致循环
    isUpdating.value = true
    formData.value.GSD = res.data.gsd || 0
    formData.value.oblique_gsd = res.data.obliqueGsd || 0
    formData.value.inclined_gimbal_pitch = res.data.inclinedGimbalPitch || 0
    formData.value.global_height = Number(res.data.height) || 0
    // 给异步更新一些时间，确保UI更新后再释放锁定
    setTimeout(() => {
      isUpdating.value = false
    }, 500)
  }).catch(error => {
    console.error('预处理失败:', error)
    isUpdating.value = false
  })
}

const handleParams = (val, newVal, oldVal) => {
  // 处理基本参数
  const data = deepClone(formData.value)
  let params = {
    "device_mode": data.device_mode,
    "global_height": data.global_height,
    "auto_flight_speed": data.auto_flight_speed,
    "placemark": [
      {
        "elevation_optimize_enable": data.elevation_optimize_enable,
        "shoot_type": data.shoot_type,
        "direction": data.direction,
        "margin": data.margin,
        "overlap": data.overlap,
        "Polygon": val,
        "GSD": data.GSD,
        "inclined_gimbal_pitch": data.inclined_gimbal_pitch,
        "inclined_flight_speed": data.inclined_flight_speed,
        "oblique_gsd": data.oblique_gsd,
      },
    ],
    "template_type": data.template_type,
  }

  // 处理互斥关系
  if (newVal && oldVal) {
    const checks = [
      { idx: [1, 1], nullParam: () => params.placemark[0].GSD = null },
      { idx: [9, 9], nullParam: () => params.global_height = null },
      { idx: [11, 11], nullParam: () => params.placemark[0].inclined_gimbal_pitch = null },
      { idx: [13, 13], nullParam: () => params.placemark[0].oblique_gsd = null },
    ];

    for (const check of checks) {
      if (String(newVal[check.idx[0]]) !== String(oldVal[check.idx[1]])) {
        check.nullParam();
        return params;
      }
    }
  }

  return params;
}

const formatSeconds = (seconds: any) => {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const remainingSeconds = seconds % 60;

  if (hours > 0) {
    return `${hours}h ${minutes}m ${remainingSeconds}s`;
  } else {
    return `${minutes}m ${remainingSeconds}s`;
  }
}

const pointClick = (item: any, index: any) => {
  pointSetting2.value = true // 展示右下角定制参数
  moreOperate.value = true // 展示右侧航点动作列表
  selectedItem.value = index // 选中某个航点,拿到下标
  console.log("点位：" + index + "动作数量：" + dataList.value[index].action_list.length)
  if (dataList.value.length > index && !dataList.value[index].hasOwnProperty('action_list')) {
    dataList.value[index].action_list = []
  }
  echoConfig(item, index)
  console.log("点位：" + index + "动作数量：" + dataList.value[index].action_list.length)
}

const delLinePoint = (item: any) => {
  // let coords = parsePoint(item.point);
  // let wgs84Coords = gcj02towgs84(coords[0], coords[1]).join(',');
  // let wgs84Coords = `${coords[0]}, ${coords[1]}`;
  // item.point = wgs84Coords;
  dataList.value = dataList.value.filter(a => a.point != item.point);
  // const arr = dataList.value.map(item => {
  //   return item.point.split(',');
  // });
  // watchSpeed();
  // placemark.value = arr.map(item => {
  //   // return wgs84togcj02(item[0], item[1]);
  //   return item;
  // });
  placemark.value = [dataList.value.map(item => {
    const point = item.point.split(',');
    point.push(item.height);
    return point;
  })]

  pointSetting2.value = false
  pointSetting1.value = false
  moreOperate.value = false
  selectedItem.value = null
}

const waylinesData = reactive({
  data: [] as WaylineFile[],
});

const workspaceId = getWorkspaceId();
const deleteTip = ref(false);
const deleteWaylineId = ref<string>('');

function getWaylines() {
  let params = {
    page: pagination.value.page,
    page_size: pagination.value.page_size,
    order_by: 'update_time desc',
    payload_model_key: selectType.value.split(','),
    key: inputName.value,
  }
  if (selectType.value == '') delete params.payload_model_key
  if (inputName.value == '') delete params.key
  getWaylineFiles(workspaceId, params).then(res => {
    if (res.data.code !== 0) {
      return;
    }
    const data = res.data.data
    waylinesData.data = data.list
    pagination.value.total = data.pagination.total;
    pagination.value.page = data.pagination.page;
  })
}

function showWaylineTip(waylineId) {
  deleteWaylineId.value = waylineId;
  deleteTip.value = true;
}

// 删除
function deleteWayline() {
  deleteWaylineFile(workspaceId, deleteWaylineId.value).then(res => {
    if (res.code === 0) {
      message.success('删除成功');

      // 检查删除的航线是否是当前选中显示的航线
      if (waylinesData.data.length > 0 && activeIndex.value !== null) {
        const currentDisplayedWayline = waylinesData.data[activeIndex.value];
        if (currentDisplayedWayline && currentDisplayedWayline.id === deleteWaylineId.value) {
          // 清空图层相关数据
          placemark.value = [];
          take0ffPoint.value = [];
          dataList.value = [];
          saData.value = {};
          timeData.value = {};
          activeIndex.value = null;
        }
      }
    }
    deleteWaylineId.value = '';
    deleteTip.value = false;

    const isLastItem = waylinesData.data.length === 1;
    const isNotFirstPage = pagination.value.page > 1;

    if (isLastItem && isNotFirstPage) {
      pagination.value.page -= 1;
    }
    
    waylinesData.data = [];
    getWaylines();
  });
}

// 下载
function downloadWayline(waylineId: string, fileName: string) {
  loading.value = true;
  downloadWaylineFile(workspaceId, waylineId).then(res => {
    if (!res) {
      return;
    }
    const blob = new Blob([res], { type: 'application/octet-stream' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.setAttribute('download', fileName + '.kmz');
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  }).finally(() => {
    loading.value = false;
  });
}

let take0ffPoint = ref([])
let activeIndex = ref(null)

async function selectRoute(wayline: any, index: number) {
  activeIndex.value = index;
  const res = await getWayLineDetail(wayline.id);
  if (res.code !== 0) {
    return;
  } else {
    // 飞行航线转换经纬度
    const arr = [];
    let currentGroup = [];

    res.data.waylineData.placemark.forEach(item => {
      const point = item.point.split(',').map(Number);
      point.push(Number(item.height) || Number(item.execute_height));

      if (item.index == 0) {
        // 当遇到index为0时，如果currentGroup不为空，将其添加到arr中
        if (currentGroup.length > 0) {
          arr.push(currentGroup);
        }
        // 重置当前组并添加当前点
        currentGroup = [point];
      } else {
        currentGroup.push(point);
      }
    });

    // 确保最后一组也被添加到arr中
    if (currentGroup.length > 0) {
      arr.push(currentGroup);
    }

    placemark.value = arr
    console.log('placemark.value', placemark.value[0].length);

    // 有数据-回显地图起飞点
    if (res.data.waylineData.take_off_ref_point && res.data.waylineData.take_off_ref_point.substring(0, 3) != 'NaN') {
      const array = res.data.waylineData.take_off_ref_point.split(',')
      // 起飞点数据处理-取字符串前两个值，经纬度格式转为gcj02进行上图
      take0ffPoint.value = [parseFloat(array[1]), parseFloat(array[0]), parseFloat(array[2])]
    } else {
      take0ffPoint.value = []
    }
    /**航线库点位*/
    const {
      file_name, device_mode, template_type, image_format, take_off_ref_point,
      fly_to_wayline_mode, take_off_security_height, height_mode, global_height, auto_flight_speed,
      global_transitional_speed, global_waypoint_turn_mode, waypoint_heading_mode,
      gimbal_pitch_mode, finish_action, exit_on_rc_lost, execute_rc_lost_action,
    } = res.data.waylineData
    session.value = {
      name: file_name,
      model: device_mode,//无人机型号
      routeType: template_type,//航线类型,
      globalHeight: globalHeight
    }
    console.log('session.value', session.value.routeType);
    // 飞行航线数据统计
    if (template_type === 'waypoint') {
      timeData.value = res.data.wayLineCount
    } else {
      timeData.value = res.data['3ddata']
    }
    saData.value = res.data['3ddata']?.sa_data

    ruleForm.routeType = template_type
    if (template_type !== 'waypoint') {
      showSetting.value = true
      EchoFormData(res.data.waylineData)
    }
    // sessionStorage.removeItem('aerobatModel');
    sessionStorage.setItem('aerobatModel', JSON.stringify(session.value));
    let photoSet = []
    if (image_format) {
      photoSet = converts(image_format)
    }
    photoSetting.value = photoSet
    Form.value.lineName = file_name
    verticalTilt.value = fly_to_wayline_mode
    takeOffSecurityHeight.value = take_off_security_height
    heightSetting.value = height_mode
    globalHeight.value = global_height
    autoFlightSpeed.value = auto_flight_speed
    globalTransitionalSpeed.value = global_transitional_speed
    destinationType.value = global_waypoint_turn_mode
    yawAngleMode.value = waypoint_heading_mode
    pitchAngleMode.value = gimbal_pitch_mode
    completeTheAction.value = finish_action
    isImplementRoute.value = exit_on_rc_lost
    haywireType.value = execute_rc_lost_action
    takeoff_Point.value = take_off_ref_point ? take_off_ref_point : ''
    dataList.value = res.data.waylineData.placemark

    dataList.value.forEach((item) => {
      if (item.action_list && item.action_list.length > 0) {
        item.action_list.forEach((item1: any) => {
          if (item1) matching(item1)
        })
      }
    })
    if (session.value.routeType === 'waypoint') {
      photoSettingOption = photoTypeSelect(device_mode)
      // 以下四个型号具有全景拍照的航点动作
      const validValues = ['M30', 'M30T', 'M3D', 'M3TD', 'M4E', 'M4T', 'M4D', 'M4TD'];
      if (!validValues.includes(device_mode)) {
        operations.value = operations.value.filter(i => i.label !== '全景拍照'); // 移出全景拍照
        showPhotoSetting.value = false
      } else {
        operations.value = operations.value.filter(i => i.label !== '全景拍照');
        operations.value.push({ 'label': '全景拍照', 'value': 13 }); // 防止多个全景拍照
      }
    } else {
      photoSettingOption = cameraTypeSelect(device_mode)
      showPhotoSetting.value = true
    }
  }
}

const EchoFormData = (data: any) => {
  formData.value.file_name = data.file_name || '';
  formData.value.device_mode = data.device_mode || '';
  formData.value.image_format = converts(data.image_format) || ['可见光'];

  formData.value.height_mode = data.height_mode || 'aboveGroundLevel';
  formData.value.global_height = data.global_height || 100.0;
  formData.value.safe_takeoff_height = data.take_off_security_height || 5.0;
  formData.value.auto_flight_speed = data.auto_flight_speed || 5.0;
  formData.value.takeoff_speed = data.global_transitional_speed || 3.0;
  formData.value.direction = data.template_placemark?.direction || 0;
  formData.value.elevation_optimize_enable = data.template_placemark?.elevation_optimize_enable || 0;
  formData.value.completeTheAction = data.finish_action || 'goHome';
  formData.value.placemark = data.placemark || [];
  formData.value.margin = data.template_placemark?.margin || 5.0;
  formData.value.shoot_type = data.template_placemark?.shoot_type || 'time';
  formData.value.template_type = data.template_type || 'mapping2d';
  formData.value.inclined_gimbal_pitch = data.template_placemark?.inclined_gimbal_pitch || 0;
  formData.value.inclined_flight_speed = data.template_placemark?.inclined_flight_speed || 0;
  formData.value.oblique_gsd = data.template_placemark?.oblique_gsd || 0;
  // 处理重叠率设置，保持原结构
  if (data.template_placemark?.overlap) {
    formData.value.overlap = {
      ortho_camera_overlap_w: data.template_placemark.overlap.ortho_camera_overlap_w || 70.0,
      ortho_camera_overlap_h: data.template_placemark.overlap.ortho_camera_overlap_h || 80.0,
      inclined_camera_overlap_w: data.template_placemark.overlap.inclined_camera_overlap_w || 70.0,
      inclined_camera_overlap_h: data.template_placemark.overlap.inclined_camera_overlap_h || 80.0
    };
  }

  // 处理GSD值
  formData.value.GSD = data.template_placemark?.gsd || 3.0;

  Polygon.value = formData.value?.template_placemark?.polygon || data.template_placemark?.polygon;
}

let photoSettingOption = [] // 照片设置option
const photoSetting = ref([]) // 拍照设置
const destinationType = ref('coordinateTurn') // 航点类型
const yawAngleMode = ref('followWayline') // 偏航角模式
const pitchAngleMode = ref('manual') // 航点间云台俯仰角控制模式
const completeTheAction = ref('goHome') // 完成动作
const isImplementRoute = ref('executeLostAction') // 失控是否继续执行航线
const haywireType = ref('goBack') // 失控动作类型
const verticalTilt = ref('safely') // 垂直或倾斜爬升
const heightSetting = ref('EGM96') // 绝对高度/相对起飞点高度/相对地形高度
const takeOffSecurityHeight = ref(20) // 安全起飞高度垂直爬升倾斜爬升的高度
const globalHeight = ref(120) // 全局航线高度（相对起飞点高度）
const autoFlightSpeed = ref(10) // 全局航线飞行速度
const globalTransitionalSpeed = ref(15) // 起飞速度


watch(globalHeight, () => {
  if (session.value.routeType !== 'waypoint') {
    return
  }
  placemark.value[0] = dataList.value.map(item => {
    item.height = globalHeight.value
    const point = item.point.split(',').map(Number)
    return [point[0], point[1], globalHeight.value]
  })
}, { deep: true }
)

async function editWayline(item: any, index: any) {
  if (activeIndex.value !== index) {
    await selectRoute(item, index)
  }

  isChange.value = false // 关闭左侧航线列表
  changeSet.value = true // 设置编辑地图权限
  waylineId.value = item.id
  pointSetting.value = true // 展示右上角更多图标
}

const photoSettingChange = () => {
  // 如果没有选中的值，则默认选中第一个选项
  if (photoSetting.value.length === 0) {
    photoSetting.value = [photoSettingOption[0]]
  }
  if (actionData.value.videoSetting1.length === 0) {
    actionData.value.videoSetting1 = [photoSettingOption[0]]
  }
  if (actionData.value.videoSetting2.length === 0) {
    actionData.value.videoSetting2 = [photoSettingOption[0]]
  }
  if (actionData.value.videoSetting3.length === 0) {
    actionData.value.videoSetting3 = [photoSettingOption[0]]
  }
  if (actionData.value.photoSetting.length === 0) {
    actionData.value.photoSetting = [photoSettingOption[0]]
  }
  if (actionData.value.panoramaSetting.length === 0) {
    actionData.value.panoramaSetting = [photoSettingOption[0]]
  }
}

const save = () => {
  session.value = JSON.parse(sessionStorage.getItem('aerobatModel')) || {}
  if (session.value.routeType == 'mapping2d' || session.value.routeType == 'mapping3d') {
    saveMapping2d()
    return
  }
  console.log('photoSetting.value', photoSetting.value);

  let params = {
    image_format: convert(photoSetting.value).join(','), //拍照类型设置（多选）
    fly_to_wayline_mode: verticalTilt.value, //垂直或倾斜爬升
    take_off_security_height: takeOffSecurityHeight.value, //安全起飞高度垂直爬升倾斜爬升的高度
    height_mode: heightSetting.value, //绝对高度/相对起飞点高度/相对地形高度
    global_height: globalHeight.value, //全局航线高度（相对起飞点高度）
    auto_flight_speed: autoFlightSpeed.value, //全局航线飞行速度
    global_transitional_speed: globalTransitionalSpeed.value, //起飞速度
    global_waypoint_turn_mode: destinationType.value, //航点类型
    waypoint_heading_mode: yawAngleMode.value, //偏航角模式
    gimbal_pitch_mode: pitchAngleMode.value, //航点间云台俯仰角控制模式
    finish_action: completeTheAction.value, //完成动作
    file_name: Form.value.lineName, //航线名称
    device_mode: session.value.model, //无人机型号
    template_type: session.value.routeType, //航线类型
    placemark: dataList.value, //模版元素
    take_off_ref_point: takeoff_Point.value, //参考起飞点
    exit_on_rc_lost: isImplementRoute.value, //失控是否继续执行航线
    execute_rc_lost_action: haywireType.value, //失控动作类型
  }
  if (params.placemark.length <= 1) {
    message.error('请绘制航线！');
    return
  }
  if (params.exit_on_rc_lost == 'executeLostAction' && params.execute_rc_lost_action == '') {
    message.error('请选择失控动作类型！');
    return
  }
  if (params.image_format == '') {
    message.error('请选择拍照设置！');
    return
  }
  if (params.exit_on_rc_lost == 'goContinue') {
    delete params.execute_rc_lost_action
  }
  // console.log('params', params)
  // 正则校验航线名称格式
  formRef.value.validate((valid: any) => {
    if (valid) {
      if (waylineId.value) {
        rebuildWayLine(params, waylineId.value).then(res => {
          if (res.code !== 0) {
            message.error('修改航线失败！');
            return;
          } else {
            message.success('修改航线成功！');
            resetFormData(); // 重置数据
            close();
          }
        })
      } else {
        addWayline(params).then(res => {
          if (res.code !== 0) {
            message.error('新增航线失败！');
            return;
          } else {
            message.success('新增航线成功！');
            resetFormData(); // 重置数据
            close();
          }
        })
      }
    } else {
      message.error('航线名称格式不正确！');
      return false;
    }
  })
}

const saveMapping2d = () => {
  if (Polygon.value.length === 0) {
    message.error('请绘制航线！');
    return
  }
  const params = {
    "file_name": Form.value.lineName, /**文件名*/
    "device_mode": session.value.model, /**无人机型号，M350 RTK，M300 RTK，M30，M30T，M3E，M3T，M3M，M3D，M3TD*/
    "take_off_security_height": formData.value.safe_takeoff_height, /**安全起飞高度垂直爬升倾斜爬升的高度*/
    "image_format": convert(formData.value.image_format).join(','), /**相机模式，wide：存储广角镜头照片 zoom：存储变焦镜头照片 ir：存储红外镜头照片 narrow_band:存储窄带镜头拍摄照片 visable：可见光照片*/
    "global_transitional_speed": "5", /**起飞速度*/
    "auto_flight_speed": formData.value.auto_flight_speed, /**全局航线飞行速度*/
    "take_off_ref_point": takeoff_Point.value, /**参考起飞点*/
    "template_type": formData.value.template_type, /**预定义模板类型，waypoint：航点飞行 mapping2d：建图航拍 mapping3d：倾斜摄影 mappingStrip：航带飞行*/
    "waypoint_heading_mode": "followWayline", /**飞行器偏航角模式，followWayline：沿航线方向，manually：手动控制，fixed：锁定当前偏航角*/
    "height_mode": formData.value.height_mode, /**航线高度模式，EGM96：海拔高度，absoluteHeight：绝对高度，relativeToStartPoint：相对起飞点高度，aboveGroundLevel：相对地形高度*/
    "global_height": formData.value.global_height, /**全局航线高度（相对起飞点高度），高度模式下面的值*/
    "placemark": [ /**模版元素*/
      {
        "elevation_optimize_enable": formData.value.elevation_optimize_enable, /**高程优化使能*/
        "shoot_type": formData.value.shoot_type, /**拍照类型*/
        "direction": formData.value.direction, /**航线角度，水平为0 顺时针为负，逆时针为正，-180--180*/
        "margin": formData.value.margin, /**边距*/
        "overlap": formData.value.overlap,
        "Polygon": Polygon.value,
        "GSD": formData.value.GSD,
        "inclined_gimbal_pitch": formData.value.inclined_gimbal_pitch,
        "inclined_flight_speed": formData.value.inclined_flight_speed,
        "oblique_gsd": formData.value.oblique_gsd,
      }
    ]
  }

  if (waylineId.value) {
    reBuildWayLineMapping2d(params, waylineId.value).then(res => {
      if (res.code !== 0) {
        message.error('修改航线失败！');
        return;
      } else {
        message.success('修改航线成功！');
        resetFormData(); // 重置数据
        close();
      }
    })
  } else {
    createWayLineKmzMapping2(params).then(res => {
      if (res.code !== 0) {
        message.error('新增航线失败！');
        return;
      } else {
        message.success('新增航线成功！');
        resetFormData(); // 重置数据
        close();
      }
    })
  }
}

const close = () => {
  EventBus.emit('lineClose');
  placemark.value = []
  dataList.value = []
  operationtitle.value = {}
  timeData.value = {}
  saData.value = {}
  selectedItem.value = null
  selectedItemIndex.value = null
  activeIndex.value = null
  isChange.value = true
  changeSet.value = false
  pointSetting.value = false
  moreOperate.value = false
  pointSetting1.value = false
  pointSetting2.value = false
  waylineId.value = ''
  showSetting.value = false
  textContent.value = '航线设置 ▲'
  getWaylines()
  photoSetting.value = []
  destinationType.value = 'coordinateTurn'
  yawAngleMode.value = 'followWayline'
  pitchAngleMode.value = 'manual'
  completeTheAction.value = 'goHome'
  isImplementRoute.value = 'executeLostAction'
  haywireType.value = 'goBack'
  photoSettingOption = []
  verticalTilt.value = 'safely'
  heightSetting.value = 'EGM96'
  takeOffSecurityHeight.value = 20
  globalHeight.value = 120
  autoFlightSpeed.value = 10
  globalTransitionalSpeed.value = 15
  take0ffPoint.value = []
  Polygon.value = ''
}

const ruleFormRef = ref<FormInstance>()
const ruleForm = reactive({
  routeType: 'waypoint',
  aerocraft: '',
  model: '',
  name: '',
})

const rules = reactive({
  name: [
    { required: true, message: '请输入航线名称', trigger: 'blur' },
    { pattern: /^[a-zA-Z0-9\u4e00-\u9fa5]{3,12}$/, message: '长度为3-12位，请勿包含以下字符：< > : " / | ? * . _', trigger: 'blur' }
  ],
  routeType: [{ required: true, message: '请选择航线类型', trigger: 'change' }],
  aerocraft: [{ required: true, message: '请选择飞行器', trigger: 'change' }],
  model: [{ required: true, message: '请选择型号', trigger: 'change' }],
})
const routeTypeOptions = ref([])
const getRouteType = async () => {
  const res = await getRouteTypeOptions()
  console.log(res);
  routeTypeOptions.value = res.data.map((item:any)=>{
    return {
      label: item.dictValue,
      value: item.dictKey
    }
  })
}
getRouteType()

const aerocraftOptions = ref([
  { label: '经纬 M30 系列', value: '1' },
  { label: 'Mavic 3 行业系列', value: '2' },
  { label: 'Matrice 3D 系列', value: '3' },
  { label: 'Matrice 4 行业系列', value: '4' },
  { label: 'Matrice 4D 系列', value: '5' },
])
const modelOptions = ref([
  { label: '经纬 M30', value: '1' },
  { label: '经纬 M30 T', value: '2' },
])

const updateAerocraft = () => {
  switch (ruleForm.routeType) {
    case 'waypoint':
      aerocraftOptions.value = [
        { label: '经纬 M30 系列', value: '1' },
        { label: 'Mavic 3 行业系列', value: '2' },
        { label: 'Matrice 3D 系列', value: '3' },
        { label: 'Matrice 4 行业系列', value: '4' },
        { label: 'Matrice 4D 系列', value: '5' },
      ]
      break;
    case 'mapping2d':
      aerocraftOptions.value = [
        { label: '经纬 M30 系列', value: '1' },
        { label: 'Mavic 3 行业系列', value: '2' },
        { label: 'Matrice 3D 系列', value: '3' },
        { label: 'Matrice 4 行业系列', value: '4' },
        { label: 'Matrice 4D 系列', value: '5' },
      ]
      break;
    case 'mapping3d':
      aerocraftOptions.value = [
        { label: 'Mavic 3 行业系列', value: '2' },
        { label: 'Matrice 3D 系列', value: '3' },
        { label: 'Matrice 4 行业系列', value: '4' },
        { label: 'Matrice 4D 系列', value: '5' },
      ]
      break;
    case 'mappingStrip':
      aerocraftOptions.value = [
        { label: 'Mavic 3 行业系列', value: '2' },
        { label: 'Matrice 3D 系列', value: '3' },
        { label: 'Matrice 4 行业系列', value: '4' },
        { label: 'Matrice 4D 系列', value: '5' },
      ]
      break;
    default:
      aerocraftOptions.value = [];
  }
}

const updateModel = () => {
  switch (ruleForm.aerocraft) {
    case '1':
      modelOptions.value = [
        { label: '经纬 M30', value: 'M30' },
        { label: '经纬 M30 T', value: 'M30T' },
      ];
      break;
    case '2':
      modelOptions.value = [
        { label: 'Mavic 3E', value: 'M3E' },
        { label: 'Mavic 3T', value: 'M3T' },
      ];
      break;
    case '3':
      modelOptions.value = [
        { label: 'Matrice 3D', value: 'M3D' },
        { label: 'Matrice 3TD', value: 'M3TD' },
      ];
      break;
    case '4':
      modelOptions.value = [
        { label: 'Matrice 4E', value: 'M4E' },
        { label: 'Matrice 4T', value: 'M4T' },
      ];
      break;
    case '5':
      modelOptions.value = [
        { label: 'Matrice 4D', value: 'M4D' },
        { label: 'Matrice 4TD', value: 'M4TD' },
      ];
      break;
    default:
      modelOptions.value = [];
  }
}

const Add = () => {
  if (ruleFormRef.value) {
    ruleFormRef.value.resetFields(); // 清空表单字段
  }
  isAdd.value = true // 唤起创建新航线弹窗
  ruleForm.name = ''  // 航线名称
  ruleForm.model = ''  // 选择型号
  ruleForm.aerocraft = ''  // 选择飞行器
  ruleForm.routeType = 'waypoint' // 默认航线类型：航点航线

  // 重置formData到初始状态，防止使用selectRoute后的数据
  formData.value = {
    // 表单基本信息
    file_name: '',
    device_mode: '',
    // 镜头设置
    image_format: ['可见光'],

    // 采集方式
    template_type: 'mapping2d',

    // 图像分辨率
    GSD: 3.0,

    // 航线高度设置
    height_mode: 'aboveGroundLevel',
    global_height: 100.0,

    // 安全起飞高度
    safe_takeoff_height: 5.0,

    // 飞行速度设置
    auto_flight_speed: 5.0,
    takeoff_speed: 3.0,

    // 航线角度
    direction: 0,

    // 高程优化
    elevation_optimize_enable: 0,

    // 完成动作
    completeTheAction: 'goHome',
    placemark: [],

    // 重叠率设置
    overlap: {
      ortho_camera_overlap_w: 70.0,
      ortho_camera_overlap_h: 80.0,
      inclined_camera_overlap_w: 70.0,
      inclined_camera_overlap_h: 80.0
    },

    // 边距
    margin: 5.0,

    // 拍照设置
    shoot_type: 'time',

    // 相机角度
    customCameraAngle: false,

    // 航线速度(倾斜)
    inclined_flight_speed: 0.1
  };
}

const submitForm = async (formEl: FormInstance | undefined) => {
  if (!formEl) return
  await formEl.validate((valid) => {
    if (valid) {
      // sessionStorage.removeItem('aerobatModel'); // 清除session
      session.value = ruleForm
      ruleForm.globalHeight = 120
      window.sessionStorage.setItem('aerobatModel', JSON.stringify(ruleForm)); // 存储航线弹窗session
      Form.value.lineName = ruleForm.name; // 创建航线后赋值给航线名称表单
      formData.value.file_name = ruleForm.name
      formData.value.device_mode = ruleForm.model
      // 调用重置数据函数
      resetFormData()
      if (ruleForm.template_type === 'waypoint') {
        photoSettingOption = photoTypeSelect(ruleForm.model)
        const validValues = ['M30', 'M30T', 'M3D', 'M3TD', 'M4E', 'M4T', 'M4D', 'M4TD'];
        if (validValues.includes(ruleForm.model)) {
          photoSetting.value = [photoSettingOption[0]] // 设置默认第一个照片设置
          formData.image_format = [photoSettingOption[0]]
          showPhotoSetting.value = true
        } else {
          photoSetting.value = ["可见光"]
          showPhotoSetting.value = false // 不展示拍照设置
        }
      } else {
        photoSettingOption = cameraTypeSelect(ruleForm.model)
        photoSetting.value = [photoSettingOption[0]] // 设置默认第一个照片设置
        formData.image_format = [photoSettingOption[0]]
        showPhotoSetting.value = true
      }

      isAdd.value = false; // 新增航线弹窗
      isChange.value = false; // 切换左侧列表
      changeSet.value = true; // 设置编辑地图权限
      showSetting.value = true; // 默认收起航线设置
      pointSetting.value = true // 显示右侧动作面板
      dataList.value = []; // 飞行航线参数清空
      placemark.value = []; // 飞行航线数据清空
      timeData.value = {}; // 飞行航线统计信息清空
      saData.value = {};
      take0ffPoint.value = []; // 飞行航线起飞点清空
      Polygon.value = ''; // 飞行航线数据清空
    }
  })
}

// 添加一个防止循环调用的标志位
const isUpdating = ref(false)

// 重置表单数据的函数
const resetFormData = () => {
  photoSettingOption = [] // 照片设置option
  photoSetting.value = [] // 拍照设置
  destinationType.value = 'coordinateTurn' // 航点类型
  yawAngleMode.value = 'followWayline' // 偏航角模式
  pitchAngleMode.value = 'manual' // 航点间云台俯仰角控制模式
  completeTheAction.value = 'goHome' // 完成动作
  isImplementRoute.value = 'executeLostAction' // 失控是否继续执行航线
  haywireType.value = 'goBack' // 失控动作类型
  verticalTilt.value = 'safely' // 垂直或倾斜爬升
  heightSetting.value = 'EGM96' // 绝对高度/相对起飞点高度/相对地形高度
  takeOffSecurityHeight.value = 20 // 安全起飞高度垂直爬升倾斜爬升的高度
  globalHeight.value = 120 // 全局航线高度（相对起飞点高度）
  autoFlightSpeed.value = 10 // 全局航线飞行速度
  globalTransitionalSpeed.value = 15 // 起飞速度
}
</script>

<style lang="scss" scoped>
.project-app-wrapper {
  display: flex;
  transition: width 0.2s ease;
  height: 100%;
  width: 100%;
  overflow: hidden;

  .left {
    display: flex;

    .main-content {
      flex: 1;
      color: #fff;
      width: 285px;
    }
  }

  .right {
    flex-grow: 1;
    position: relative;

    .map-wrapper {
      width: 100%;
      height: 100%;
    }
  }
}

.wayline-panel {
  background: #1f2b38;
  margin-left: auto;
  margin-right: auto;
  margin-top: 9px;
  height: 90px;
  width: 95%;
  font-size: 13px;
  border-radius: 2px;
  cursor: pointer;
  padding-top: 5px;

  .title {
    display: flex;
    flex-direction: row;
    align-items: center;
    height: 30px;
    font-weight: bold;
    margin: 0 10px 0 10px;
  }
}

.g-map-wrapper {
  height: 100%;
  width: 100%;

  .g-action-panel {
    position: absolute;
    top: 16px;
    right: 16px;

    .g-action-item {
      width: 28px;
      height: 28px;
      background: white;
      color: rgb(81, 81, 81);
      border-radius: 2px;
      line-height: 28px;
      text-align: center;
      margin-bottom: 2px;
    }

    .g-action-item:hover {
      border: 1px solid rgb(81, 81, 81);
      border-radius: 2px;
    }
  }

  &:deep(.ant-btn) {
    &::after {
      display: none;
    }
  }
}

.uranus-scrollbar {
  overflow: auto;
  scrollbar-width: thin;
  scrollbar-color: #c5c8cc transparent;
}

.content {
  height: calc(100% - 90px);
  overflow-y: auto;
}

.pagination {
  position: absolute;
  bottom: 14px;
  left: 34px;
  width: 150px;
}

.hearder {
  position: absolute;
  top: 12px;
  left: 12px;
  right: 0;
  z-index: 1;
  width: 330px;
  color: #fff;

  .searchInput {
    background-color: #282626;
    color: white;
    margin-top: 4px;
  }
}

.updown {
  margin: 6px 0 4px 96px;
  width: 90px;
  height: 24px;
}

.close {
  position: absolute;
  top: 4px;
  right: 0;
  font-size: 16px;
  cursor: pointer;
}

ul {
  list-style-type: none;
  padding: 10px;
  margin: 4px;
}

.leftManage {
  position: fixed;
  bottom: 40px;
  left: 50%;
  transform: translate(-20%, 0);
  color: white;
  padding: 10px;
  z-index: 1;
  background: rgba(0, 0, 0, 0.4);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);

  .info-container {
    display: flex;
    flex-wrap: nowrap;
    justify-content: center;
  }

  .info-item {
    padding: 0 15px;
    display: flex;
    flex-direction: column;
    align-items: center;
    min-width: 100px;

    &:not(:last-child) {
      border-right: 1px solid rgba(255, 255, 255, 0.2);
    }
  }

  .info-title {
    font-size: 16px;
    white-space: nowrap;
    margin-bottom: 5px;
  }

  .info-value {
    font-size: 18px;
    text-align: center;
  }
}

.openOperate {
  position: fixed;
  width: 30px;
  height: 30px;
  top: 14px;
  z-index: 1;
}

.list-item {
  cursor: pointer;
  padding: 5px;
  border: 1px solid #ddd;
  margin: 5px 0;
}

.list-item.active {
  background-color: #3C3C3C;
}

.blue-border {
  border: 1px solid #2B85E4;
}

:deep(.ant-input-affix-wrapper > input.ant-input) {
  background-color: #282626;
  color: white;
}

:deep(.ant-select:not(.ant-select-customize-input) .ant-select-selector) {
  background-color: #282626;
  color: white;
}

.moreDiv {
  position: fixed;
  width: 150px;
  top: 0;
  color: #fff;
  z-index: 1;
  background: #a8a5a5;

  .moreDiv_item {
    width: 100%;
    height: 33px;
    margin-bottom: 6px;

    span {
      line-height: 33px;
      cursor: pointer;
    }

    div {
      float: right;
    }
  }
}

.tooltips-content {
  width: 200px;
  height: 200px;
  overflow: auto;

  .tooltips-container {
    width: 100%;
    display: flex;
  }
}


.container {
  width: 100%;
  display: flex;
  flex-wrap: wrap;

  .index {
    width: 10%;
    box-sizing: border-box;
  }

  .item_list {
    width: 80%;
    display: flex;
    flex-wrap: wrap;

    .item {
      width: 20%;
    }
  }

  .button {
    width: 10%;
    text-align: center;
    box-sizing: border-box;
  }
}

@for $n from 1 through 13 {
  .img_#{$n} {
    background-image: url('./img/#{$n}.jpg');
    background-size: 100% 100%;
    display: inline-block;
    width: 30px;
    height: 30px;
  }
}

.rightSet {
  width: 250px;
  height: 370px;
  background: black;
  padding: 0 4px;
  position: fixed;
  top: 0;
  right: 0;
  z-index: 1;

  .title {
    display: flex;
    justify-content: space-between;
    width: 100%;
    height: 30px;
    line-height: 30px;
    color: #fff;
  }

  .main {
    color: white;
  }
}

.pointParams {
  width: 250px;
  height: 410px;
  background: black;
  padding: 0 4px;
  position: fixed;
  bottom: 0;
  right: 0;
  z-index: 1;

  .title {
    width: 100%;
    text-align: center;
    height: 30px;
    line-height: 30px;
    color: white;
  }

  .closeIcon {
    position: absolute;
    font-size: 16px;
    top: 4px;
    right: -4px;
    color: rgb(255, 255, 255);
    cursor: pointer;
  }
}
</style>