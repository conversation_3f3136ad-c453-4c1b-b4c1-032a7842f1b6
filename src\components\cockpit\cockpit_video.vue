<template>
    <div>
        <div class="flex-column flex-justify-start flex-align-center">
            <div class="video-container">
                <div id="rtcPlayer">
                    <jessibucaPlayer ref="jessibuca" :visible.sync="true" :videoUrl="videoUrl" :hasAudio="true" fluent
                        autoplay live></jessibucaPlayer>
                    <div id="selectionBox" v-show="isDrawing" :style="selectionBoxStyle"></div>
                    <div v-for="rect in drawnRectangles" :key="rect.id" :style="rect.style" class="thermal-rect">
                        <span class="rect-label">测温区域</span>
                        <span class="rect-temp" v-if="temperature">{{ temperature }}℃</span>
                    </div>
                </div>
            </div>
            <!-- <div :class="['map-container', { 'map-container-expanded': isMapExpanded }]">
                <div id="mp-container" style="width: 100%;height:100%"></div>
            </div> -->
            <div class="flex-row flex-justify-center flex-align-center mt10">
                <div class="operate" v-permission>
                    <!-- 选择设备 -->
                    <a-popover title="选择设备" placement="left">
                        <template #content>
                            <a-radio-group v-model:value="droneSelected" @change="onDroneSelect">
                                <a-radio :style="radioStyle" v-for="item in droneList" :key="item.label"
                                    :value="item.value"> {{ item.label }}</a-radio>
                            </a-radio-group>
                        </template>
                        <VideoCameraAddOutlined class="font30" />
                    </a-popover>
                    <!-- 选择相机 -->
                    <a-popover title="选择相机" placement="left">
                        <template #content>
                            <a-radio-group v-model:value="cameraSelected" @change="onCameraSelect">
                                <a-radio :style="radioStyle" v-for="item in cameraList" :key="item.label"
                                    :value="item.value"> {{ item.label }}</a-radio>
                            </a-radio-group>
                        </template>
                        <CameraOutlined class="font30" />
                    </a-popover>
                    <!-- 选择画质 -->
                    <a-popover title="配置" placement="left">
                        <template #content>
                            <div>
                                <div>选择画质</div>
                                <a-radio-group v-model:value="claritySelected" @change="onClaritySelect">
                                    <a-radio :style="radioStyle" v-for="item in clarityList" :key="item.label"
                                        :value="item.value"> {{ item.label }}</a-radio>
                                </a-radio-group>
                            </div>
                            <div>
                                <div>镜头</div>
                                <a-radio-group v-model:value="lensSelected" @change="onSwitch">
                                    <a-radio :style="radioStyle" v-for="lens in lensList" :key="lens" :value="lens">
                                        {{ displayMode[lens] }}
                                    </a-radio>
                                </a-radio-group>
                            </div>
                        </template>
                        <ControlOutlined class="font30" />
                    </a-popover>
                </div>
                <template v-if="liveState && isDockLive">
                    <template v-if="lensList.length > 1">
                        <!-- <span class="mr10" style="color: white;">镜头:</span>
                        <a-radio-group v-model:value="lensSelected" button-style="solid">
                            <a-radio-button v-for="lens in lensList" :key="lens" :value="lens">
                                {{ displayMode[lens] }}
                            </a-radio-button>
                        </a-radio-group> -->
                    </template>
                </template>
                <template v-else>

                </template>
            </div>

            <!-- <div class="mt20">
                <p class="fz10" v-if="livetypeSelected == 2">
                    Please use VLC media player to play the RTSP livestream !!!
                </p>
                <p class="fz10" v-if="livetypeSelected == 2">RTSP Parameter:{{ rtspData }}</p>
            </div> -->

            <div class="btn-operate">
                <!-- <a-popover title="配置" placement="left">
                    <template #content>
                        <div>
                            <div>镜头</div>
                            <a-radio-group v-model:value="lensSelected" @change="onSwitch">
                                <a-radio :style="radioStyle" v-for="lens in lensList" :key="lens" :value="lens">
                                    {{ displayMode[lens] }}
                                </a-radio>
                            </a-radio-group>
                        </div>
                        <div>
                            <div>舱内/舱外</div>
                            <a-radio-group v-model:value="positionSelected" @change="checkPosition"
                                :disabled="!(liveState && isDockLive)">
                                <a-radio :style="radioStyle" :value="0">舱内</a-radio>
                                <a-radio :style="radioStyle" :value="1">舱外</a-radio>
                            </a-radio-group>
                        </div>
                    </template>
                    <ControlOutlined class="font30" />
                </a-popover> -->
                <!-- 播放 -->
                <a-tooltip title="播放">
                    <PlaySquareOutlined class="font30" v-if="!liveState" type="primary" large @click="onStart"
                        v-permission />
                </a-tooltip>
                <!-- 停止 -->
                <a-tooltip title="停止">
                    <PauseCircleOutlined class="font30" v-if="liveState" @click="onStop" v-permission />
                </a-tooltip>
                <!-- 刷新直播 -->
                <a-tooltip title="刷新直播">
                    <ReloadOutlined class="font30" v-if="!liveState" @click="() => onRefresh(false)" v-permission />
                </a-tooltip>
                <!-- 切换 -->
                <a-tooltip title="切换">
                    <ColumnWidthOutlined class="font30" @click="transIon" />
                </a-tooltip>
            </div>
            <!-- 变焦 -->
            <template v-if="liveState">
                <!-- 3种模式可以变焦 -->
                <div class="slider-operate" v-if="['ir', 'wide', 'zoom'].includes(lensSelected)">
                    <span class="colorFFF">变焦</span>
                    <!-- 红外是20，其余200 -->
                    <el-slider v-model="sliderParams.value" vertical height="200px" :min="2"
                        :max="lensSelected == 'ir' ? 20 : 200" :step="2" @change="setZoom" v-permission />
                </div>
                <div class="camera-operation">
                    <!-- 镜头中心 -->
                    <a-popover title="镜头中心" placement="left">
                        <template #content>
                            <div class="form-content">
                                <div>
                                    <span class="form-label">开启点选视频设置中心:</span>
                                    <a-switch v-model:checked="cameraAimPopoverData.showClick" v-permission />
                                </div>
                                <div>
                                    <span class="form-label">锁定:</span>
                                    <a-switch v-model:checked="cameraAimPopoverData.locked" v-permission />
                                </div>
                            </div>
                        </template>
                        <ExpandOutlined class="font30" />
                    </a-popover>
                </div>
            </template>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { message } from 'ant-design-vue';
import { ElMessage } from 'element-plus';
import jessibucaPlayer from "@/views/video/common/jessibuca.vue";
import { onMounted, ref, computed, reactive, watch, onBeforeUnmount } from 'vue';
import { onBeforeRouteLeave } from 'vue-router';
import { CURRENT_CONFIG as config } from '@/config/dijconfig';
import {
    changeLivestreamLens,
    getLiveCapacity,
    setLivestreamQuality,
    startLivestream,
    stopLivestream,
} from '@/api/manage';
import { cameraChange } from '@/api/livestream/index.js';
import store from '@/store/';
import {
    PlaySquareOutlined, PauseCircleOutlined, AimOutlined, BorderInnerOutlined,
    BorderOuterOutlined, ReloadOutlined,
    ColumnWidthOutlined, VideoCameraAddOutlined,
    CameraOutlined, ControlOutlined, ExpandOutlined, BorderOutlined
} from '@ant-design/icons-vue';

import EventBus from '@/event-bus';
import { usePayloadControl } from '@/components/g-map/use-payload-control';
import { CameraType } from '@/types/live-stream';
const { cameraExposure, cameraAim, changeCameraFocalLength } = usePayloadControl();
const props = defineProps({
    devicePlay: Boolean,
    cockpit_dock: Object,
    deviceInfoAttrs: Object
})
const radioStyle = reactive({
    display: 'block',
    height: '30px',
    lineHeight: '30px',
});

let videoUrl = computed(() => {
    return store.state.common.cockpitVideoUrl
})

watch(() => videoUrl, (newVal) => {
    setTimeout(() => {
        !newVal.value && jessibuca?.value?.destroy();
    }, 0)
}, { immediate: true, deep: true })
interface SelectOption {
    value: any;
    label: string;
    more?: any;
}
watch(() => props.devicePlay, (newVal, oldVal) => {
    // 负载默认值
    if (newVal == true) {
        console.log("自动播放")
        setTimeout(() => {
            onRefresh(true)
        }, 4000)
    }

    if (newVal == false) {
        console.log("自动停止")
        onStop()
    }
}, { immediate: true, deep: true })
const liveTypeList: SelectOption[] = [
    // {
    //   value: 1,
    //   label: 'RTMP',
    // },
    // {
    //   value: 2,
    //   label: 'RTSP',
    // },
    {
        value: 3,
        label: 'GB28181',
    },
    // {
    //   value: 4,
    //   label: 'WEBRTC',
    // },
];
/**画质枚举 */
const clarityList: SelectOption[] = [
    {
        value: 0,
        label: '自适应',
    },
    {
        value: 1,
        label: '流畅',
    },
    {
        value: 2,
        label: '标清',
    },
    {
        value: 3,
        label: '高清',
    },
    {
        value: 4,
        label: '超清',
    },
];

/**镜头类型枚举 */
const displayMode = {
    normal: '默认',
    wide: '广角',
    zoom: '变焦',
    ir: '红外',
};

const livestreamSource = ref();
/**设备下拉 */
const droneList = ref([]);
/**相机下拉 */
const cameraList = ref([]);
const videoList = ref([]);
/**设备默认值 */
const droneSelected = ref();
/**相机默认值 */
const cameraSelected = ref();
const videoSelected = ref();
/**画质默认值 */
const claritySelected = ref(0);
const videoId = ref();
/**视频播放ing */
const liveState = ref<boolean>(false);
const livetypeSelected = ref(3);
const rtspData = ref();
/**镜头list */
const lensList = ref<string[]>([]);
/**镜头默认值 */
const lensSelected = ref<string>();
const isDockLive = ref(false);
const nonSwitchable = 'normal';
// 地图轨迹
let isMapExpanded = ref(false);
let temperature = ref<string>('');
const emit = defineEmits(['change']);
const jessibuca = ref();
const transIon = () => {
    isMapExpanded.value = !isMapExpanded.value;
    emit('change', isMapExpanded.value);
    if (isMapExpanded.value) {
        jessibuca.value.setBigWidth()
    }
    // 在布局变化后延迟执行重新计算矩形位置
    setTimeout(() => {
        recalculateRectangles();
    }, 300);
};
var text = document.createElement('span');
// 视频点击标点图片
let imgList = ref([]);
// 测温点
let thermometric = ref(false);

// 框选绘制功能
const isDrawing = ref(false);
const startPoint = reactive({ x: 0, y: 0 });
const endPoint = reactive({ x: 0, y: 0 });
// 最小拖动阈值（像素）
const MIN_DRAG_THRESHOLD = 10;
// 保存已绘制的矩形区域
const drawnRectangles = ref<Array<{
    id: string,
    style: any,
    x: number,
    y: number,
    width: number,
    height: number,
    normalized: { x: number, y: number, width: number, height: number }
}>>([]);

// 拖动状态变量
const isDraggingRect = ref(false);
const draggedRectId = ref('');
const dragStartPos = reactive({ x: 0, y: 0 });
const rectStartPos = reactive({ x: 0, y: 0 });

// 重新计算并更新矩形位置和大小
const recalculateRectangles = () => {
    const player = document.getElementById('rtcPlayer');
    if (!player || drawnRectangles.value.length === 0) return;

    const playerWidth = player.clientWidth;
    const playerHeight = player.clientHeight;

    drawnRectangles.value = drawnRectangles.value.map(rect => {
        const newX = rect.normalized.x * playerWidth;
        const newY = rect.normalized.y * playerHeight;
        const newWidth = rect.normalized.width * playerWidth;
        const newHeight = rect.normalized.height * playerHeight;

        return {
            ...rect,
            x: newX,
            y: newY,
            width: newWidth,
            height: newHeight,
            style: {
                ...rect.style,
                left: newX + 'px',
                top: newY + 'px',
                width: newWidth + 'px',
                height: newHeight + 'px'
            }
        };
    });
};

const selectionBoxStyle = computed(() => {
    const left = Math.min(startPoint.x, endPoint.x);
    const top = Math.min(startPoint.y, endPoint.y);
    const width = Math.abs(endPoint.x - startPoint.x);
    const height = Math.abs(endPoint.y - startPoint.y);

    return {
        position: 'absolute' as const,
        left: left + 'px',
        top: top + 'px',
        width: width + 'px',
        height: height + 'px',
        border: '2px solid #00FFFF',
        backgroundColor: 'rgba(0, 255, 255, 0.1)',
        pointerEvents: 'none' as const
    };
});

// 简化后的鼠标事件处理函数
const handleMouseEvents = {
    down: (event) => {
        if (!drawRectEnabled.value) return;

        // 检查是否点击在已有矩形上
        const rect = drawnRectangles.value.find(r => 
            event.offsetX >= r.x && 
            event.offsetX <= r.x + r.width && 
            event.offsetY >= r.y && 
            event.offsetY <= r.y + r.height
        );
        
        if (rect) {
            // 开始拖动已有矩形
            isDraggingRect.value = true;
            draggedRectId.value = rect.id;
            dragStartPos.x = event.offsetX;
            dragStartPos.y = event.offsetY;
            rectStartPos.x = rect.x;
            rectStartPos.y = rect.y;
            return;
        }

        // 开始框选
        isDrawing.value = true;
        startPoint.x = endPoint.x = event.offsetX;
        startPoint.y = endPoint.y = event.offsetY;
    },
    
    move: (event) => {
        if (isDraggingRect.value) {
            // 移动已有矩形
            const deltaX = event.offsetX - dragStartPos.x;
            const deltaY = event.offsetY - dragStartPos.y;
            const player = document.getElementById('rtcPlayer');
            if (!player) return;

            const width = player.clientWidth;
            const height = player.clientHeight;
            const rectIndex = drawnRectangles.value.findIndex(rect => rect.id === draggedRectId.value);
            if (rectIndex === -1) return;

            const rect = drawnRectangles.value[rectIndex];
            let newX = Math.max(0, Math.min(width - rect.width, rectStartPos.x + deltaX));
            let newY = Math.max(0, Math.min(height - rect.height, rectStartPos.y + deltaY));
            
            // 更新矩形位置（只在UI上更新，不触发接口）
            const normalizedX = newX / width;
            const normalizedY = newY / height;
            
            const updatedRect = {
                ...rect,
                x: newX,
                y: newY,
                style: {
                    ...rect.style,
                    left: newX + 'px',
                    top: newY + 'px',
                },
                normalized: {
                    ...rect.normalized,
                    x: normalizedX,
                    y: normalizedY,
                }
            };

            drawnRectangles.value[rectIndex] = updatedRect;
            
            // 移除发送更新坐标的代码，仅在松开鼠标时发送
            return;
        }

        if (isDrawing.value) {
            endPoint.x = event.offsetX;
            endPoint.y = event.offsetY;
        }
    },
    
    up: (event) => {
        if (isDraggingRect.value) {
            // 拖动结束时，发送更新后的坐标
            const rectIndex = drawnRectangles.value.findIndex(rect => rect.id === draggedRectId.value);
            
            if (rectIndex !== -1) {
                const rect = drawnRectangles.value[rectIndex];
                // 在拖动结束时发送更新后的坐标
                EventBus.emit('rectangle_selection', {
                    x: rect.normalized.x,
                    y: rect.normalized.y,
                    width: rect.normalized.width,
                    height: rect.normalized.height
                });
            }
            
            isDraggingRect.value = false;
            draggedRectId.value = '';
            message.success('测温区域位置已更新');
            return;
        }

        if (!isDrawing.value) return;

        isDrawing.value = false;
        endPoint.x = event.offsetX;
        endPoint.y = event.offsetY;

        // 计算拖动距离
        const dragDistanceX = Math.abs(endPoint.x - startPoint.x);
        const dragDistanceY = Math.abs(endPoint.y - startPoint.y);

        // 如果拖动距离太小，不创建框选区域
        if (dragDistanceX < MIN_DRAG_THRESHOLD && dragDistanceY < MIN_DRAG_THRESHOLD) {
            console.log('拖动距离过小，取消框选');
            return;
        }

        // 创建新的测温区域
        createTemperatureArea();
    }
};

// 将创建测温区域逻辑提取为单独函数
const createTemperatureArea = () => {
    const player = document.getElementById('rtcPlayer');
    if (!player) return;
    
    const width = player.clientWidth;
    const height = player.clientHeight;

    // 计算绝对坐标
    const left = Math.min(startPoint.x, endPoint.x);
    const top = Math.min(startPoint.y, endPoint.y);
    const rectWidth = Math.abs(endPoint.x - startPoint.x);
    const rectHeight = Math.abs(endPoint.y - startPoint.y);

    // 归一化坐标（0-1范围）
    const normalizedX = left / width;
    const normalizedY = top / height;
    const normalizedWidth = rectWidth / width;
    const normalizedHeight = rectHeight / height;

    // 生成唯一ID
    const rectId = `rect-${Date.now()}`;

    // 创建归一化坐标对象，避免重复创建相同数据结构
    const normalized = {
        x: normalizedX,
        y: normalizedY,
        width: normalizedWidth,
        height: normalizedHeight
    };

    // 清除之前的矩形，只保留当前的
    drawnRectangles.value = [{
        id: rectId,
        style: {
            position: 'absolute',
            left: left + 'px',
            top: top + 'px',
            width: rectWidth + 'px',
            height: rectHeight + 'px',
            border: '2px solid #00FFFF',
            backgroundColor: 'rgba(0, 255, 255, 0.05)',
            pointerEvents: 'none'
        },
        x: left,
        y: top,
        width: rectWidth,
        height: rectHeight,
        normalized
    }];

    // 发送框选区域坐标到事件总线，直接使用normalized对象
    EventBus.emit('rectangle_selection', normalized);

    // 显示提示信息
    message.success(temperature.value 
        ? `已创建测温区域，温度: ${temperature.value}℃，可通过拖动调整位置`
        : '已创建测温区域，可通过拖动调整位置');
};

// 简化开启/关闭框选功能
const drawRectEnabled = ref(false);
const toggleDrawRectMode = (enable?: boolean) => {
    // 如果提供了明确的enable参数，则使用它，否则切换当前状态
    if (enable !== undefined) {
        drawRectEnabled.value = enable;
    } else {
        drawRectEnabled.value = !drawRectEnabled.value;
    }

    const player = document.getElementById('rtcPlayer');
    if (!player) return;
    
    if (drawRectEnabled.value) {
        message.info('已启用框选功能，请在视频上拖动鼠标进行区域选择，已有区域可直接拖动调整位置');
        player.classList.add('drawing-mode');
        toggleClickListener(false);
        
        // 添加框选事件
        player.addEventListener('mousedown', handleMouseEvents.down);
        player.addEventListener('mousemove', handleMouseEvents.move);
        player.addEventListener('mouseup', handleMouseEvents.up);
    } else {
        message.info('已关闭框选功能');
        player.classList.remove('drawing-mode');
        
        // 移除框选事件
        player.removeEventListener('mousedown', handleMouseEvents.down);
        player.removeEventListener('mousemove', handleMouseEvents.move);
        player.removeEventListener('mouseup', handleMouseEvents.up);
        
        toggleClickListener(true);
        drawnRectangles.value = [];
    }
};

// 特定用于EventBus的处理函数
function handleToggleDrawRectMode(payload: any) {
    toggleDrawRectMode(Boolean(payload));
}

// 声明点击事件处理函数（放在组件顶层）
let clickStartPosition = { x: 0, y: 0 };
let isClick = false;

const mouseDownForClick = (event) => {
    if (drawRectEnabled.value) return; // 框选模式下不处理点击

    isClick = true;
    clickStartPosition.x = event.offsetX;
    clickStartPosition.y = event.offsetY;
};

const mouseMoveForClick = (event) => {
    if (!isClick || drawRectEnabled.value) return;

    // 如果移动超过阈值，不再视为点击
    const moveX = Math.abs(event.offsetX - clickStartPosition.x);
    const moveY = Math.abs(event.offsetY - clickStartPosition.y);

    if (moveX > 5 || moveY > 5) {
        isClick = false;
    }
};

const mouseUpForClick = (event) => {
    if (!isClick || drawRectEnabled.value) return;

    isClick = false;
    const moveX = Math.abs(event.offsetX - clickStartPosition.x);
    const moveY = Math.abs(event.offsetY - clickStartPosition.y);

    // 如果移动很小，才算是点击
    if (moveX <= 5 && moveY <= 5) {
        clickHandler(event);
    }
};

const clickHandler = (event) => {
    const player = document.getElementById('rtcPlayer');
    const target = event.target;
    let x = event.offsetX / target.clientWidth;
    let y = event.offsetY / target.clientHeight;
    console.log('点击坐标: (x: ' + x + ', y: ' + y + ')');

    // 加载点选图片
    if (imgList.value.length != 0) {
        imgList.value.forEach((img) => {
            player.removeChild(img);
        });
        imgList.value = [];
    }
    if (temperature.value && thermometric.value) {
        const img = document.createElement('img');
        img.src = '/img/circle.png';
        img.style.position = 'absolute';
        img.style.width = '20px';
        img.style.height = '20px';
        img.style.left = `${event.offsetX - 10}px`;
        img.style.top = `${event.offsetY - 10}px`;
        img.style.zIndex = '1000';
        imgList.value.push(img);

        // 创建文本元素
        text.innerText = temperature.value + '℃';
        text.style.top = `${event.offsetY - 10}px`;
        text.style.left = `${event.offsetX - 10}px`;
        text.style.zIndex = '1000';
        if (player.contains(text)) {
            player.removeChild(text);
        }
        player.appendChild(text);
        player.appendChild(img);
    } else {
        if (player.contains(text)) {
            player.removeChild(text);
        }
    }

    if (thermometric.value) {
        console.log("x点" + x + "y点:" + y);
        EventBus.emit('cockpit_video_control', { x, y });
    } else {
        setCameraCenter(x, y);
    }
};

const toggleClickListener = (enable) => {
    const player = document.getElementById('rtcPlayer');
    if (!player) return;

    // 先清理所有已有的点击标记和文本
    if (imgList.value.length != 0) {
        imgList.value.forEach((img) => {
            if (player.contains(img)) {
                player.removeChild(img);
            }
        });
        imgList.value = [];
    }

    if (player.contains(text)) {
        player.removeChild(text);
    }

    // 移除已有的监听器
    player.removeEventListener('mousedown', mouseDownForClick);
    player.removeEventListener('mousemove', mouseMoveForClick);
    player.removeEventListener('mouseup', mouseUpForClick);

    // 如果需要启用，则添加监听器
    if (enable) {
        player.addEventListener('mousedown', mouseDownForClick);
        player.addEventListener('mousemove', mouseMoveForClick);
        player.addEventListener('mouseup', mouseUpForClick);
    }
};

/**
 * 摧毁前
 */
onBeforeUnmount(() => {
    EventBus.off('takeoffToPointPopoverData')
    EventBus.off('thermometricPopoverData')
    EventBus.off('temperatureSet')
    // 确保使用相同的处理函数引用移除事件监听
    EventBus.off('toggleDrawRectMode', handleToggleDrawRectMode)

    // 移除点击事件监听器和清理DOM
    toggleClickListener(false);

    // 移除窗口尺寸变化监听
    window.removeEventListener('resize', recalculateRectangles);

    // 移除全屏变化事件监听
    document.removeEventListener('fullscreenchange', recalculateRectangles);
    document.removeEventListener('webkitfullscreenchange', recalculateRectangles);
    document.removeEventListener('mozfullscreenchange', recalculateRectangles);
    document.removeEventListener('MSFullscreenChange', recalculateRectangles);
});
const onStart = async () => {
    const timestamp = new Date().getTime().toString()
    videoId.value =
        droneSelected.value + '/' + cameraSelected.value + '/' + (videoSelected.value || nonSwitchable + '-0')
    if (
        livetypeSelected.value == null ||
        droneSelected.value == null ||
        cameraSelected.value == null ||
        claritySelected.value == null
    ) {
        message.warn('请选择直播参数')
        return
    }
    let liveURL = ''
    switch (livetypeSelected.value) {
        case 1: {
            // RTMP
            liveURL = config.rtmpURL + timestamp
            break
        }
        case 2: {
            // RTSP
            liveURL = `userName=${config.rtspUserName}&password=${config.rtspPassword}&port=${config.rtspPort}`
            break
        }
        case 3: {
            liveURL = `serverIP=${config.gbServerIp}&serverPort=${config.gbServerPort}&serverID=${config.gbServerId}&agentID=${config.gbAgentId}&agentPassword=${config.gbPassword}&localPort=${config.gbAgentPort}&channel=${config.gbAgentChannel}`
            break
        }
        case 4: {
            break
        }
        default:
            break
    }
    await startLivestream({
        url: liveURL,
        video_id: videoId.value,
        url_type: livetypeSelected.value,
        video_quality: claritySelected.value
    }).then(res => {
        if (res.code !== 0) {
            return
        }
        if (livetypeSelected.value === 3) {
            let url = '';
            if (document.location.href.includes('https')) {
                // 正式
                url = res.data.wss_flv
            } else {
                // 本地
                url = res.data.ws_flv
            }
            store.commit('setCockpitUrl', url);
        }
        liveState.value = true
        // 视频开始播放后检查是否需要启用点击监听
        if (lensSelected.value === 'ir') {
            toggleClickListener(true);
        }
    }).catch(err => {
        console.error(err);
    })
};

const onStop = () => {
    if (!liveState.value) return;
    // 停止播放时移除点击监听
    toggleClickListener(false);
    store.commit('setCockpitUrl', '');
    videoId.value =
        droneSelected.value +
        '/' +
        cameraSelected.value +
        '/' +
        (videoSelected.value || nonSwitchable + '-0');
    if (
        livetypeSelected.value == null ||
        droneSelected.value == null ||
        cameraSelected.value == null ||
        claritySelected.value == null
    ) {
        return
    }
    // stopLivestream({
    //     video_id: videoId.value,
    // }).then(res => {
    // if (res.code === 0) {
    // message.success(res.message);
    liveState.value = false;
    lensSelected.value = undefined;
    setLensSelected();
    // }
    // });
};
/**
 * 设置镜头,告知负载界面
 */
const setLensSelected = () => {
    EventBus.emit('setLensSelected', lensSelected.value);
}

const onUpdateQuality = () => {
    if (!liveState.value) {
        message.info('请先开启直播.');
        return;
    }
    setLivestreamQuality({
        video_id: videoId.value,
        video_quality: claritySelected.value,
    }).then(res => {
        if (res.code === 0) {
            message.success('将清晰度设置为' + clarityList[claritySelected.value].label);
        }
    });
};

/**
 * 选择设备显示对应的镜头
 */
const onDroneSelect = () => {
    const val = droneList.value.find((ele: any) => ele.value === droneSelected.value);
    
    const temp: Array<SelectOption> = [];
    cameraList.value = [];
    cameraSelected.value = undefined;
    videoSelected.value = undefined;
    videoList.value = [];
    lensList.value = [];
    
    if (!val?.more) {
        return;
    }
    
    val.more.forEach((ele: any) => {
        temp.push({ label: ele.name, value: ele.index, more: ele.videos_list });
    });
    
    // 设备下的子集——相机 ,过滤label包含FPV的相机
    cameraList.value = temp.filter((ele: any) => !ele.label.includes('FPV'));
};
/**
 * 选择相机
 */
const onCameraSelect = () => {
    const val = cameraList.value.find((ele: any) => ele.value === cameraSelected.value);
    
    const result: Array<SelectOption> = [];
    videoSelected.value = undefined;
    videoList.value = [];
    lensList.value = [];
    
    if (!val?.more) {
        return;
    }
    
    val.more.forEach((ele: any) => {
        result.push({ label: ele.type, value: ele.index, more: ele.switch_video_types });
    });
    
    videoList.value = result;
    
    if (videoList.value.length === 0) {
        return;
    }
    
    const firstVideo: SelectOption = videoList.value[0];
    videoSelected.value = firstVideo.value;
    lensList.value = firstVideo.more;
    lensSelected.value = firstVideo.label;
    isDockLive.value = lensList.value?.length > 0;
    setLensSelected();
};

const onVideoSelect = (val: SelectOption) => {
    videoSelected.value = val.value;
    lensList.value = val.more;
    lensSelected.value = val.label;
    setLensSelected();
};
/**
 * 切换画质
 */
const onClaritySelect = () => {
    onUpdateQuality();
};

const onSwitch = () => {
    setLensSelected();
    if (!videoId.value) {
        return
    }

    // 先移除现有的点击监听
    toggleClickListener(false);

    // 检查是否需要启用点击监听
    if (liveState.value && lensSelected.value === 'ir') {
        setTimeout(() => {
            toggleClickListener(true);
        }, 500); // 延迟0.5秒等待切换完成
    }

    if (lensSelected.value === undefined || lensSelected.value === nonSwitchable) {
        message.info(nonSwitchable + ' 不可切换, 请选择要切换的镜头.', 8);
        return;
    }

    changeLivestreamLens({
        video_id: videoId.value,
        video_type: lensSelected.value,
    }).then(res => {
        if (res.code === 0) {
            message.success('成功切换直播摄像头.');
        }
    });
};

onBeforeRouteLeave(() => {
    // 路由即将改变前执行的操作
    if (liveState.value) {
        onStop();
    }
});

// 镜头中心
const cameraAimPopoverData = reactive({
    showClick: false,
    locked: false,
    loading: false
});
/**
 * 镜头中心
 */
const setCameraCenter = async (x, y) => {
    const { showClick, locked, loading } = cameraAimPopoverData
    if (showClick) {
        if (!loading) {
            cameraAimPopoverData.loading = true;
            try {
                //  相机的值,镜头的值,
                await cameraAim(props.cockpit_dock.gateway.sn, {
                    payload_index: cameraSelected.value,
                    camera_type: lensSelected.value as CameraType,
                    locked,
                    x,
                    y
                });
            } catch (error) {
                console.log(error);
            } finally {
                cameraAimPopoverData.loading = false;
            }
        } else {
            message.info('请先等待上次下发命令完成，完成之后在去选择镜头中心！');
        }
    } else {
        // message.info('请打开开启点选视频设置中心！');
    }
}
// 变焦
const sliderParams = reactive({
    value: 2,
    loading: false
});
/**
 * 变焦
 */
const setZoom = async (val) => {
    const { loading } = sliderParams;
    if (!loading) {
        sliderParams.loading = true;
        try {
            if (lensSelected.value == 'wide') {
                message.warn('广角模式下无法设置变焦！');
                sliderParams.value = 2
                return
            }
            
            // 检查是否为全景拍照模式（相机模式值为3）
            if (props.deviceInfoAttrs && props.deviceInfoAttrs.cameras && props.deviceInfoAttrs.cameras.length > 0) {
                const cameraMode = props.deviceInfoAttrs.cameras[0].camera_mode;
                if (cameraMode === 3) { // 全景拍照模式
                    message.warn('全景拍照模式下不能变焦！');
                    sliderParams.value = 2;
                    return;
                }
            }
            
            await changeCameraFocalLength(props.cockpit_dock.gateway.sn, {
                payload_index: cameraSelected.value,
                camera_type: lensSelected.value as CameraType,
                zoom_factor: val
            });
        } catch (err) {
        } finally {
            sliderParams.loading = false;
        }
    } else {
        message.info('请先等待上次下发命令完成，完成之后在去选择镜头中心！');
    }
}

defineExpose({
    onStop
});

onMounted(() => {
    text.style.position = 'absolute';
    text.style.color = 'white'; // 可选：设置文字颜色
    text.style.fontSize = '14px'; // 可选：设置文字大小
    text.style.zIndex = '1000';
    // setInitMap();
    onRefresh(true);
    // 一键起飞之后重新请求视频。自动播放
    // EventBus.on('takeoffToPointPopoverData', (data) => {
    //     onRefresh(true);
    // })
    EventBus.on('temperatureSet', (data) => {
        temperature.value = data
        text.innerText = temperature.value + '℃';
    })
    // 选择测温点
    EventBus.on('thermometricPopoverData', (data) => {
        thermometric.value = data
    })
    // 接收框选功能触发事件
    EventBus.on('toggleDrawRectMode', handleToggleDrawRectMode);

    // 初始化时启用点击监听
    toggleClickListener(true);

    // 监听窗口尺寸变化，重新计算矩形位置
    window.addEventListener('resize', recalculateRectangles);

    // 监听全屏变化事件
    document.addEventListener('fullscreenchange', recalculateRectangles);
    document.addEventListener('webkitfullscreenchange', recalculateRectangles);
    document.addEventListener('mozfullscreenchange', recalculateRectangles);
    document.addEventListener('MSFullscreenChange', recalculateRectangles);

    // 创建ResizeObserver监听播放器元素尺寸变化
    const resizeObserver = new ResizeObserver(() => {
        recalculateRectangles();
    });

    const player = document.getElementById('rtcPlayer');
    if (player) {
        resizeObserver.observe(player);
    }
});

const onRefresh = async (isPlay?: boolean) => {
    droneList.value = [];
    cameraList.value = [];
    videoList.value = [];
    droneSelected.value = null;
    cameraSelected.value = null;
    videoSelected.value = null;
    
    // 重置镜头中心相关的状态
    cameraAimPopoverData.showClick = false;
    cameraAimPopoverData.locked = false;
    cameraAimPopoverData.loading = false;
    
    // 获取设备列表
    await getLiveCapacity({}).then(res => {
        if (res.code === 0) {
            if (res.data === null) {
                return;
            }
            const resData: Array<[]> = res.data;
            livestreamSource.value = resData;
            const temp: Array<SelectOption> = [];
            if (livestreamSource.value) {
                livestreamSource.value.forEach((ele: any) => {
                    temp.push({ label: ele.name + '-' + ele.sn, value: ele.sn, more: ele.cameras_list });
                });
                //todo 待优化
                droneList.value = temp.filter(v => v.label.includes(props.cockpit_dock.sn));
                onSetFirstVide0(droneList.value, isPlay)
            }
        }
    }).catch(error => {
        message.error(error);
    });
};

// 默认播放第一个视频
const onSetFirstVide0 = (value, isPlay?: boolean) => {
    if (value.length === 0) {
        return
    }
    // 判断是否有飞行器
    const pilot = value.filter(v => v.label.includes('飞行器')).length !== 0 ? value.filter(v => v.label.includes('飞行器'))[0] : value[0]
    droneSelected.value = pilot.value;
    onDroneSelect();
    if (cameraList.value.length !== 0) {
        cameraSelected.value = cameraList.value[0].value;
        // TODO:发送到controlContainer 去告诉更新负载数据
        // EventBus.emit('pushCameraList', cameraList.value);
        onCameraSelect();
    }
    setTimeout(() => {
        isPlay && onStart()
    }, 0);
};

const positionSelected = ref();
const checkPosition = () => {
    if (
        livetypeSelected.value == null ||
        droneSelected.value == null ||
        cameraSelected.value == null ||
        claritySelected.value == null
    ) {
        message.warn('请选择直播参数!')
        return Promise.resolve();
    }
    const params = {
        video_id: videoId.value,
        camera_position: positionSelected.value,
    }
    return cameraChange(params).then(({ data }) => {
        if (data.code == 0) {
            setTimeout(() => {
                ElMessage({
                    type: 'success',
                    message: '操作成功！',
                })
            }, 1500);
        } else {
            setTimeout(() => {
                ElMessage({
                    type: 'error',
                    message: '操作失败！',
                })
            }, 1500);
        }
    });
};
</script>

<style lang="scss" scoped>
@import '@/styles/dij/index.scss';

.video-container {
    position: relative;
    width: 100%;
    height: 100%;
}

.video-container-big {
    position: absolute;
    left: 10px;
    top: -60px;
    width: 100%;
    height: 100%;
    z-index: 1;
}

.font30 {
    font-size: 25px;
    margin: 0 5px
}

.margin10 {
    margin: 10px;
}

// 地图
.map-container {
    position: absolute;
    bottom: 100px;
    left: 10px;
    width: 100px;
    height: 100px;
}

.map-container-expanded {
    position: absolute;
    /* top: 0; */
    bottom: 90px;
    left: 0;
    width: 100%;
    height: 300px;

    .jessibuca-container {
        width: 100px;
        height: 100px;
    }
}

// 右上角操作
.operate {
    position: absolute;
    left: 0;
    top: 0;
    display: grid;
    grid-gap: 10px;
    background: #000;
    color: #fff;
}

// 按钮操作
.btn-operate {
    position: absolute;
    left: 0;
    bottom: 43px;
    display: grid;
    grid-gap: 2px;
    background: #000;
    color: #fff;
}

/**镜头中心 */
.camera-operation {
    position: absolute;
    bottom: 40px;
    right: 5px;
}

.form-content div {
    display: flex;
    justify-content: space-between;
}

// 变焦
.slider-operate {
    position: absolute;
    right: 5px;
    top: 10%
}

.colorFFF {
    color: #fff;
}

// 为了确保rtcPlayer可以正确接收鼠标事件
#rtcPlayer {
    position: relative;
    width: 100%;
    height: 100%;
    object-fit: cover;
    cursor: pointer;
}

#rtcPlayer.drawing-mode {
    cursor: crosshair;
}

// 测温区域样式
.thermal-rect {
    z-index: 100;
    cursor: move;

    .rect-label {
        position: absolute;
        top: -20px;
        left: 0;
        color: #00FFFF;
        font-size: 12px;
        background-color: rgba(0, 0, 0, 0.5);
        padding: 2px 5px;
        border-radius: 2px;
    }

    .rect-temp {
        position: absolute;
        bottom: 0;
        right: 0;
        color: #FF9900;
        font-size: 14px;
        font-weight: bold;
        background-color: rgba(0, 0, 0, 0.5);
        padding: 2px 5px;
        border-radius: 2px;
    }
}

.video-player-container {
    position: relative;
    width: 100%;
    height: 100%;
    border-radius: 10px;
    overflow: hidden;
    background-color: #000;
}

.video-controls {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    padding: 8px;
    display: flex;
    justify-content: center;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 10;
}

.rect {
    position: absolute;
    border: 2px solid #ff0000;
    background-color: rgba(255, 0, 0, 0.2);
    pointer-events: none;
    z-index: 1000;
}
</style>