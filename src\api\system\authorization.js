import request from '@/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/hztech-system/userThirdAuth/page',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    },
  });
};

export const getDetail = id => {
  return request({
    url: '/hztech-system/userThirdAuth/detail',
    method: 'get',
    params: {
      id,
    },
  });
};

export const createCode = id => {
  return request({
    url: '/hztech-system/userThirdAuth/createCode',
    method: 'get',
    params: {
      id,
    },
  });
};

export const remove = ids => {
  return request({
    url: '/hztech-system/userThirdAuth/remove',
    method: 'post',
    params: {
      ids,
    },
  });
};

export const add = row => {
  return request({
    url: '/hztech-system/userThirdAuth/save',
    method: 'post',
    data: row,
  });
};

export const update = row => {
  return request({
    url: '/hztech-system/userThirdAuth/update',
    method: 'post',
    data: row,
  });
};
