<template>
  <div class="media-panel-wrapper">
    <a-form style="display: flex;background-color: #fff;padding-top: 10px;">
      <a-col :span="6">
        <a-form-item label="文件名称" style="padding-left: 10px;">
          <a-input v-model:value="body.file_name" placeholder="请输入文件名称" allow-clear style="width: 200px;"/>
        </a-form-item>
      </a-col>
      <a-col :span="6">
        <a-form-item label="飞行器类型">
          <a-input v-model:value="body.payload" placeholder="请输入飞行器类型" allow-clear style="width: 200px;"/>
        </a-form-item>
      </a-col>
      <a-col :span="6">
        <a-form-item label="创建时间">
          <a-date-picker v-model:value="body.create_time" :locale="zh_CN" valueFormat="YYYY-MM-DD" placeholder="请选择创建时间" style="width: 200px;"/>
        </a-form-item>
      </a-col>
      <a-col :span="6">
        <a-form-item>
          <a-button style="margin-left: 10px;" type="primary" @click="search">搜索</a-button>
          <a-button style="margin-left: 10px;" @click="reset">重置</a-button>
        </a-form-item>
      </a-col>
    </a-form>

    <a-config-provider :locale="zhCN">
      <a-table class="media-table" :scroll="{ x: 1500, y: height }" :columns="columns" @change="refreshData"
               :data-source="mediaData.data" :pagination="paginationProp">
        <template v-for="col in ['name', 'path']" #[col]="{ text }" :key="col">
          <a-tooltip :title="text">
            <a v-if="col === 'name'">{{ text }}</a>
            <span v-else>{{ text }}</span>
          </a-tooltip>
        </template>
        <template v-for="col in ['file_type']" #[col]="{ text }" :key="col">
          <span>{{ getStringAfter(text, '.') }}</span>
        </template>
        <template #original="{ text }">
          {{ text ? '是' : '否' }}
        </template>
        <template #action="{ record }">
          <a-tooltip title="下载">
            <div v-if="file_id === record.file_id" style="display: flex;">
              <el-progress :percentage="percentage" style="width: 140px;"/>
              <el-icon>
                <CloseBold @click="cancelDownload"/>
              </el-icon>
            </div>
            <a v-else class="fz14" style="color:#1890ff" @click="downloadMedia(record)">
              <DownloadOutlined style="color:#1890ff"/>
              下载
            </a>
          </a-tooltip>
        </template>
      </a-table>
    </a-config-provider>
  </div>
</template>

<script setup lang="ts">
import {ref, reactive, onMounted} from 'vue';
import {downloadMediaFile, getMediaFiles, getFileSize} from '@/api/media/index';
import {ELocalStorageKey} from '@/api/enum/index';
import {DownloadOutlined} from '@ant-design/icons-vue';
import zhCN from "ant-design-vue/es/locale/zh_CN";
import zh_CN from 'ant-design-vue/es/date-picker/locale/zh_CN';
import { getWorkspaceId } from '@/utils/storage'

let file_id = ref('');
let percentage = ref(0);
const columns = [
  {
    title: '文件名称',
    dataIndex: 'file_name',
    ellipsis: true,
    slots: {customRender: 'name'},
  },
  {
    title: '文件类型',
    dataIndex: 'file_name',
    ellipsis: true,
    slots: {customRender: 'file_type'},
  },
  {
    title: '文件路径',
    dataIndex: 'file_path',
    ellipsis: true,
    slots: {customRender: 'path'},
  },
  {
    title: '机场编号',
    dataIndex: 'drone',
    ellipsis: true,
    slots: {customRender: 'path'},
  },
  {
    title: '飞行器类型',
    dataIndex: 'payload',
    ellipsis: true,
    slots: {customRender: 'path'},
  },
  {
    title: '是否原图',
    dataIndex: 'is_original',
    slots: {customRender: 'original'},
  },
  {
    title: '创建时间',
    dataIndex: 'create_time',
    ellipsis: true,
    slots: {customRender: 'path'},
  },
  {
    title: '操作',
    slots: {customRender: 'action'},
  },
];
const body = reactive({
  page: 1,
  total: 0,
  page_size: 10,
  file_name: '',
  payload: '',
  create_time: '',
});
const paginationProp = reactive({
  pageSizeOptions: ['10', '20', '50', '100'],
  showQuickJumper: true,
  showSizeChanger: true,
  pageSize: 10,
  current: 1,
  total: 0,
});
const mediaData = reactive({
  data: [],
});
const height = ref(document.documentElement.clientHeight - 220 + 'px')

onMounted(() => {
  getFiles();
});

async function getFiles() {
  const res = await getMediaFiles(getWorkspaceId(), body)
  mediaData.data = res.data.data.list;
  paginationProp.total = res.data.data.pagination.total;
  paginationProp.current = res.data.data.pagination.page;
}

function search() {
  getFiles();
}

function reset() {
  body.page = 1
  body.total = 0
  body.page_size = 10
  body.file_name = ''
  body.payload = ''
  body.create_time = ''
  getFiles();
}

function getStringAfter(sourceString, searchString) {
  let parts = sourceString.split(searchString);
  if (parts.length > 1) {
    return parts.slice(1).join(''); // 返回searchString后面的部分
  }
  return ''; // 如果searchString不存在，返回空字符串
}

function refreshData(page) {
  body.page = page?.current!;
  body.page_size = page?.pageSize!;
  getFiles();
}

async function downloadMedia(media) {
  percentage.value = 0; // 清空进度条
  file_id.value = media.file_id; // 文件id
  try {
    const fileSize = await getFileSize(getWorkspaceId(), media.file_id);
    const chunkSize = 1024 * 1024;
    const chunks = calculateChunks(fileSize, chunkSize);
    let fileBlobs = []
    for (let i = 0; i < chunks.length; i++) {
      const blobs = await downloadChunk(chunks[i])
      fileBlobs.push(blobs)
      let start = i + 1
      let end = chunks.length
      let num = (start / end) * 100
      percentage.value = parseFloat(num.toFixed(2))
      if (num == 100) {
        file_id.value = ''
      }
    }
    // 下面的代码用于下载文件
    const blob = new Blob(fileBlobs, {type: 'application/octet-stream'});
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.setAttribute('download', media.file_name);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  } catch (error) {
    console.error('error:', error);
  }
}

// 定义中止下载的函数
function cancelDownload() {
  file_id.value = ''
}

function calculateChunks(fileSize, chunkSize) {
  const chunks = [];
  let start = 0;
  while (start < fileSize) {
    const end = Math.min(start + chunkSize - 1, fileSize - 1);
    chunks.push({start, end});
    start = end + 1;
  }
  return chunks;
}

async function downloadChunk(chunk) {
  return downloadMediaFile(getWorkspaceId(), file_id.value, chunk)
}
</script>

<style lang="scss" scoped>
.media-panel-wrapper {
  width: 100%;
  padding: 0 16px 16px 16px;

  .media-table {
    background: #fff;
  }

  .action-area {
    color: #333;
    cursor: pointer;
  }
}

.header {
  width: 100%;
  height: 60px;
  background: #fff;
  padding: 16px;
  font-size: 20px;
  font-weight: bold;
  text-align: start;
  color: #000;
}

.bor-b {
  height: 50px;
  line-height: 50px;
  border-bottom: 1px solid #4f4f4f;
  font-weight: 450;
}
</style>