<template>
  <div class="cockpitMap">
    <div id="cockpitMap" :class="['map-container', { 'map-container-small': isMapExpanded }]"></div>
    <div class="back-button" @click="goBack">
      <i class="el-icon-arrow-left"></i>
      <span>返回</span>
    </div>
    <div class="alarm-message-container">
      <alarmMessage :cockpit_dock="cockpit_dock"></alarmMessage>
    </div>
    <cockpitLayer></cockpitLayer>
    <div class="right-container">
      <airportSelection @changeUrl="changeUrl" @changeDomain="changeDomain"></airportSelection>
      <dashBoardBox :deviceInfoAttrs="deviceInfoAttrs"></dashBoardBox>
      <controlContainer v-if="cockpit_dock.is_dock" class="z_index1" :deviceInfoAttrs="deviceInfoAttrs"
        :cockpit_dock="cockpit_dock">
      </controlContainer>
    </div>
    <!-- <cokpitProcess :deviceInfoAttrs="deviceInfoAttrs" :dockInfoAttrs="dockInfoAttrs"></cokpitProcess> -->
    <cockpitVideo v-if="!algorithmUrl" ref="videoMap" :deviceInfoAttrs="deviceInfoAttrs" @change="videoMapChange"
      :class="isMapExpanded ? 'video-big' : 'video_box'" :devicePlay="devicePlay" :cockpit_dock="cockpit_dock">
    </cockpitVideo>
    <div v-else class="video-big">
      <player ref="player" :videoUrl="algorithmUrl" fluent autoplay @screenshot="shot" @destroy="destroy" />
    </div>
    <identifyImages :cockpit_dock="cockpit_dock"></identifyImages>
  </div>
</template>

<script lang="ts">
import dashBoardBox from './dashBoardBox.vue';
import controlContainer from './controlContainer.vue';
import cockpitLayer from './cockpitLayer.vue';
import cokpitProcess from './cockpitProcess.vue';
import alarmMessage from './alarmMessage.vue';
import { useConnectMqtt } from '@/components/g-map/use-connect-mqtt';
import cockpitVideo from './cockpit_video.vue';
import store from '@/store';
import { mapGetters, mapState } from 'vuex';
import { EDeviceTypeName } from '@/types';
import { gcj02towgs84, wgs84togcj02 } from '@/vendors/coordtransform';
import dockIcon from '@/assets/dock.png'
import rcIcon from '@/assets/rc.png'
import droneIcon from '@/assets/m30.png'
import airportSelection from './airportSelection.vue';

import { useGMapManage } from '@/hooks/use-c-map';
import { cockpitTsaUpdate } from '@/hooks/use-c-map-cockpit';
import { getApp, getRoot } from '@/root';
import { geNowWayLineJob, getWayLineDetail } from '@/api/wayline';
import EventBus from '@/event-bus';
import { testDroneTrackDisplay } from '@/hooks/use-c-map-cockpit';
import DrawTool from '@/components/djimap/DrawTool.js';
import identifyImages from './identifyImages.vue';
import player from '../video/common/jessibuca.vue';
import ReconnectingWebSocket from 'reconnecting-websocket'
import { getWebsocketUrl } from '@/websocket/util/config'
import { EBizCode, ERouterName } from '@/types'

export default {
  emits: ['closeCockpit'],
  components: {
    dashBoardBox,
    controlContainer,
    cockpitVideo,
    cockpitLayer,
    cokpitProcess,
    alarmMessage,
    identifyImages,
    airportSelection,
    player
  },
  data() {
    return {
      mapCo: '',
      // deviceInfo: store.state.dock.deviceState,
      cockpitTsaUpdateHook: cockpitTsaUpdate(),
      icons: new Map([
        [EDeviceTypeName.Aircraft, droneIcon],
        [EDeviceTypeName.Gateway, rcIcon],
        [EDeviceTypeName.Dock, dockIcon]
      ]),
      // markers: store.state.dock.markerInfo.coverMap,
      isMapExpanded: true,
      isCameraFollow: true, // 添加镜头跟随状态
      //
      points: [],
      /**起飞点数据*/
      startPoint: [],
      template_type: '',
      Polygon: '',
      /**起飞点图层*/
      overlay: [],
      polyline: null, // 存储折线数据
      startPolyline: null,
      overlayGroups: null, // 航点群体标识
      texts: null, // 航段中点距离标签
      drawTool: null, // 绘制工具,
      /*如果从4到1的话就退出航线，从1到4的话就应该重新获取航线*/
      modeCode: -1,
      // 设备状态
      deviceInfoAttrs: null,
      dockInfoAttrs: null,
      //直播在线
      devicePlay: null,
      // worker
      WRJWoker: null,
      algorithmUrl: null,
      osdVisible: {
        sn: '',
        gateway_sn: '',
        dock_sn: '',
        gateway_domain: '',
        dock_domain: '',
      },
      center: [120.2367, 30.3019],
      cockpit_dock: {
        is_dock: true,
      },
      webSocketInstance: null,
    };
  },
  // 在created中初始化非响应式属性
  created() {
    // 这些属性将被放在组件实例上，但不是响应式数据
    this.texts = []; // 航段中点距离标签
    this.overlay = null; // 起飞点图层
    this.polyline = null; // 存储折线数据
    this.overlayGroups = []; // 航点群体标识
    this.startPolyline = null; // 存储起飞点折线数据
    this.handler = null; // Cesium事件处理器
    this.dragHandler = null; // 拖拽事件处理器
    this.keyboardHandler = null; // 键盘事件处理器
    this.drawTool = null; // 绘制工具
    this.endMarker = null; // 控制点位的end图标

    this.cockpitTsaUpdateHook.removeAllMarker();
    // @ts-ignore
    this.WRJWoker = new Worker(new URL('./worker/cockpit_worker.js', import.meta.url));
  },
  computed: {
    deviceInfo() {
      return store.state.dock.deviceState.deviceInfo
    },
    dockInfo() {
      return store.state.dock.deviceState.dockInfo
    },
    gatewayInfo() {
      return store.state.dock.deviceState.gatewayInfo
    },

    isChange() {
      return store.state.dock.deviceState
    },
    online() {
      return store.state.dock.deviceStatusEvent.deviceOnline
    },
    offline() {
      return store.state.dock.deviceStatusEvent.deviceOffline
    }
  },
  watch: {
    'online': {
      handler(data, oldVal) {
        if (data.sn == this.cockpit_dock.sn) {
          this.devicePlay = true;
        }
      },
      deep: true,
    },
    'isChange': {
      handler(data, oldVal) {
        //无人机
        if (data.currentType === EDeviceTypeName.Aircraft && data.deviceInfo[data.currentSn]) {
          if (data.currentSn != this.cockpit_dock.sn) {
            return;
          }
          const { longitude, latitude, height } = data.deviceInfo[data.currentSn]
          if (longitude && latitude) {
            this.WRJWoker.postMessage({ deviceSn: data.currentSn, longitude: longitude || 0, latitude: latitude || 0 })
            // 监听数据变化
            this.WRJWoker.onmessage = ({ data: { deviceSn, coordinate } }) => {
              this.cockpitTsaUpdateHook.moveTo(deviceSn, longitude, latitude, height);
              // let point = [longitude, latitude];
              // this.mapMoveTo(point)
            };
          }
          this.deviceInfoAttrs = data.deviceInfo[this.cockpit_dock.sn];
        }
        // 机场
        if (data.currentType === EDeviceTypeName.Dock && data.dockInfo[data.currentSn]) {
          if (data.currentSn != this.cockpit_dock.gateway?.sn) {
            return;
          }

          const { longitude, latitude } = data.dockInfo[data.currentSn].basic_osd
          if (longitude && latitude) {
            // const [lng, lat] = gcj02towgs84(longitude, latitude)
            // this.cockpitTsaUpdateHook.addMarker(data.currentSn, lng, lat);
            this.cockpitTsaUpdateHook.addMarker(data.currentSn, longitude, latitude);
          }
          let mCode = data.dockInfo[data.currentSn].basic_osd.mode_code;
          if (this.modeCode != mCode) {
            this.clearWayLine();
            this.points = [];
            this.startPoint = [];
            this.getNowWayline();
            this.modeCode = mCode;
          }
          this.dockInfoAttrs = data.dockInfo[this.cockpit_dock.gateway.sn];
        }
        //遥控器
        if (data.currentType === EDeviceTypeName.Gateway && data.gatewayInfo[data.currentSn]) {
          if (data.currentSn != this.cockpit_dock.gateway.sn) {
            return;
          }
          const { longitude, latitude } = data.gatewayInfo[data.currentSn]
          if (longitude && latitude) {
            this.cockpitTsaUpdateHook.addMarker(data.currentSn, longitude, latitude);
          }
          this.dockInfoAttrs = this.deepClone(data.gatewayInfo[this.cockpit_dock.gateway.sn]);
          this.dockInfoAttrs.basic_osd = {};
          this.dockInfoAttrs.basic_osd.mode_code = 5;
        }

      },
      deep: true,
    },
    'offline': {
      handler(data, oldVal) {
        this.cockpitTsaUpdateHook.removeMarker(data.sn);
        if (data.sn == this.cockpit_dock.gateway.sn) {
          this.dockInfoAttrs = null;
        }
        if (data.sn == this.cockpit_dock.sn) {
          this.deviceInfoAttrs = null;
          this.devicePlay = false;
        }
      },
      deep: true,
    },
    // 'algorithmUrl': {
    //   handler(data, oldVal) {
    //     const player = this.$refs.player;
    //     if (player) {
    //       player.setBigWidth();
    //     }
    //   }
    // },
    center: {
      handler(data, oldVal) {
        console.log('center', data);
        this.mapMoveTo(data);
      },
      deep: true,
    }
  },
  mounted() {
    this.init();
    // this.getNowWayline()
    useConnectMqtt();

    // 初始化WebSocket连接
    this.initWebSocket();

    // 监听镜头跟随状态变化
    EventBus.on('toggle_camera_follow' as any, this.handleCameraFollowToggle);
    // setTimeout(() => {
    //   testDroneTrackDisplay(120.66261,27.982012);
    // }, 1000);
  },
  beforeUnmount() {
    this.WRJWoker.terminate();
    // this.dockWorker.terminate();
    EventBus.off('toggle_camera_follow' as any, this.handleCameraFollowToggle);

    // 关闭WebSocket连接
    if (this.webSocketInstance) {
      this.webSocketInstance.close();
    }
  },
  methods: {
    // 初始化WebSocket连接
    initWebSocket() {
      const url = getWebsocketUrl();
      if (!url) return;

      // 创建WebSocket连接实例
      this.webSocketInstance = new ReconnectingWebSocket(url, [], {
        maxReconnectionDelay: 20000,
        minReconnectionDelay: 5000,
        maxRetries: 5
      });

      // 添加事件监听
      this.webSocketInstance.addEventListener('open', this.onWebSocketOpen);
      this.webSocketInstance.addEventListener('close', this.onWebSocketClose);
      this.webSocketInstance.addEventListener('error', this.onWebSocketError);
      this.webSocketInstance.addEventListener('message', this.onWebSocketMessage);
    },

    // WebSocket连接成功
    onWebSocketOpen() {
      console.log('WebSocket连接成功');
    },

    // WebSocket连接关闭
    onWebSocketClose(event) {
      console.log('WebSocket连接已断开', JSON.stringify(event));
    },

    // WebSocket连接错误
    onWebSocketError(event) {
      console.log('WebSocket连接错误', JSON.stringify(event));
    },

    // 处理WebSocket消息
    onWebSocketMessage(msg) {
      try {
        const data = JSON.parse(msg.data);
        this.messageHandler(data);
      } catch (error) {
        console.error('WebSocket消息解析错误', error);
      }
    },

    messageHandler(payload) {
      if (!payload) {
        return
      }

      switch (payload.biz_code) {
        case EBizCode.GatewayOsd: {
          store.commit('SET_GATEWAY_INFO', payload.data)
          break
        }
        case EBizCode.DeviceOsd: {
          store.commit('SET_DEVICE_INFO', payload.data)
          break
        }
        case EBizCode.DockOsd: {
          store.commit('SET_DOCK_INFO', payload.data)
          break
        }
        case EBizCode.MapElementCreate: {
          store.commit('SET_MAP_ELEMENT_CREATE', payload.data)
          break
        }
        case EBizCode.MapElementUpdate: {
          store.commit('SET_MAP_ELEMENT_UPDATE', payload.data)
          break
        }
        case EBizCode.MapElementDelete: {
          store.commit('SET_MAP_ELEMENT_DELETE', payload.data)
          break
        }
        case EBizCode.DeviceOnline: {
          store.commit('SET_DEVICE_ONLINE', payload.data)
          break
        }
        case EBizCode.DeviceOffline: {
          store.commit('SET_DEVICE_OFFLINE', payload.data)
          break
        }
        case EBizCode.FlightTaskProgress:
        case EBizCode.FlightTaskMediaProgress:
        case EBizCode.FlightTaskMediaHighestPriority: {
          EventBus.emit('flightTaskWs', payload)
          break
        }
        case EBizCode.DeviceHms: {
          store.commit('SET_DEVICE_HMS_INFO', payload.data)
          break
        }
        case EBizCode.DeviceReboot:
        case EBizCode.DroneOpen:
        case EBizCode.DroneClose:
        case EBizCode.CoverOpen:
        case EBizCode.CoverClose:
        case EBizCode.PutterOpen:
        case EBizCode.PutterClose:
        case EBizCode.ChargeOpen:
        case EBizCode.ChargeClose:
        case EBizCode.DeviceFormat:
        case EBizCode.DroneFormat:
          {
            store.commit('SET_DEVICES_CMD_EXECUTE_INFO', {
              biz_code: payload.biz_code,
              timestamp: payload.timestamp,
              ...payload.data,
            })
            break
          }
        case EBizCode.ControlSourceChange:
        case EBizCode.FlyToPointProgress:
        case EBizCode.TakeoffToPointProgress:
        case EBizCode.JoystickInvalidNotify:
        case EBizCode.DrcStatusNotify:
          {
            EventBus.emit('droneControlWs', payload)
            break
          }
        case EBizCode.FlightAreasSyncProgress: {
          EventBus.emit('flightAreasSyncProgressWs', payload.data)
          break
        }
        case EBizCode.FlightAreasDroneLocation: {
          EventBus.emit('flightAreasDroneLocationWs', payload)
          break
        }
        case EBizCode.FlightAreasUpdate: {
          EventBus.emit('flightAreasUpdateWs', payload.data)
          break
        }
        // 红外测温变化
        case EBizCode.IrMeteringPoint:
        case EBizCode.IrMeteringArea:
          {
            console.log('irMeteringWs', payload.data);
            EventBus.emit('irMeteringWs' as any, payload.data)
            break
          }
        default:
          break
      }
    },
    // 处理镜头跟随状态变化
    handleCameraFollowToggle(value) {
      this.isCameraFollow = value;
      if (this.isCameraFollow) {
        this.cockpitTsaUpdateHook.enableEntityFollow(this.cockpit_dock.sn)
      } else {
        this.cockpitTsaUpdateHook.disableEntityFollow()
      }
    },
    // 返回上一级路由
    goBack() {
      this.$router.go(-1);
    },
    /**
     * 地图偏移
     * @param coordinate 
     */
    mapMoveTo(coordinate) {
      if (!this.isCameraFollow) return;
      setTimeout(() => {
        const root = getRoot();
        let viewer = root.$cockpitMap;
        if (viewer) {
          viewer.camera.flyTo({
            destination: Cesium.Cartesian3.fromDegrees(coordinate[0], coordinate[1], 500),
            orientation: {
              heading: Cesium.Math.toRadians(0.0),
              pitch: Cesium.Math.toRadians(-90),
              roll: 0.0
            }
          });
        }
      }, 0);
    },
    /**
     * 移除地图
     */
    removeMap() {
      const root = getRoot();
      let viewer = root.$cockpitMap;
      if (viewer) {
        viewer.entities.removeAll();
      }
    },
    // 判断数组arr1是否包含数组arr2的所有元素
    arrayContainsArray(arr1, arr2) {
      // 如果arr2的长度大于arr1，直接返回false
      if (arr2.length > arr1.length) {
        return false;
      }
      let arr1Index = 0;
      for (const item of arr2) {
        // 在arr1中从当前位置开始查找item
        while (arr1Index < arr1.length && arr1[arr1Index] !== item) {
          arr1Index++;
        }
        // 如果没有找到item，返回false
        if (arr1Index >= arr1.length) {
          return false;
        }
        // 移动到arr1的下一个位置（即使arr1中有重复的item，也继续向前）
        arr1Index++;
      }
      // 如果找到了arr2中的所有元素，返回true
      return true;
    },
    init() {
      const app = getApp();
      useGMapManage().globalPropertiesConfig(app, this.center, 'cockpitMap', 'Cesium');
    },
    videoMapChange(value) {
      this.isMapExpanded = value
    },
    async getNowWayline() {
      let gateWaySn = store.state.dock.osdVisible.gateway_sn;
      if (!gateWaySn) {
        return
      }
      const { data: res } = await geNowWayLineJob(gateWaySn);
      if (!res || res.code == -1) {
        return;
      }
      // const res = await getWayLineDetail('c48acb21-1a1f-4570-a88f-e37ea6c5fa81')
      // 有数据-回显地图起飞点
      if (!res.data.waylineData) {
        return;
      }
      const { take_off_ref_point, placemark, template_type } = res.data.waylineData;
      if (take_off_ref_point && take_off_ref_point.substring(0, 3) != 'NaN') {
        const array = take_off_ref_point.split(',');
        this.startPoint = [parseFloat(array[1]), parseFloat(array[0]), parseFloat(array[2])];
      } else {
        this.startPoint = [];
      }
      this.template_type = template_type
      this.points = placemark.map(({ point, height }) => {
        const [lat, lng] = point.split(',').map(Number);
        return [lat, lng, height];
      });
      //多边形数据
      this.Polygon = res.data?.waylineData?.template_placemark?.polygon
      this.updateMapline();
    },
    updateMapline() {
      const root = getRoot()
      let viewer = root.$cockpitMap

      //删除所有图层
      this.clearWayLine()

      //添加起飞点
      if (this.startPoint && this.startPoint.length > 0) {
        const startAltitude = this.startPoint.length > 2 ? this.startPoint[2] : 0;
        const position = Cesium.Cartesian3.fromDegrees(this.startPoint[0], this.startPoint[1], startAltitude);
        this.overlay = viewer.entities.add({
          position: position,
          billboard: {
            image: '/img/start.png', // 需要添加起飞点图标
            verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
            scale: 0.7,
            width: 38,
            disableDepthTestDistance: Number.POSITIVE_INFINITY,
            // clampToGround: true,
            // heightReference: Cesium.HeightReference.RELATIVE_TO_GROUND,
          },
          label: {
            text: '起飞点',
            font: '14px sans-serif',
            style: Cesium.LabelStyle.FILL_AND_OUTLINE,
            outlinewidth: 4,
            verticalOrigin: Cesium.VerticalOrigin.TOP,
            pixelOffset: new Cesium.Cartesian2(0, -10),
            disableDepthTestDistance: Number.POSITIVE_INFINITY,
            // clampToGround: true,
            // heightReference: Cesium.HeightReference.RELATIVE_TO_GROUND,
          }
        });
      }

      // 如果points数组为空，则不添加连接线和多边形，只保留起飞点
      if (!this.points || this.points.length === 0) {
        // 强制Cesium重新渲染
        if (viewer) {
          viewer.scene.requestRender();
        }
        return;
      }

      //添加起飞点距离第一个航线的连线
      this.createStartPolyline(viewer);

      if (this.template_type === 'waypoint') {
        //添加航线折线数据
        if (this.points.length > 1) {
          console.log(this.points);

          const positions = this.points.map(point =>
            Cesium.Cartesian3.fromDegrees(point[0], point[1], point[2] || 0)
          );

          this.polyline = viewer.entities.add({
            polyline: {
              positions: positions,
              width: 4,
              material: Cesium.Color.RED,
              clampToGround: false // 设置为false，确保折线不贴地
            }
          });
        }
        //添加航点标点
        this.createPointIcont(viewer)
      } else if (this.template_type === 'mapping2d' || this.template_type === 'mapping3d') {
        // 面状航线逻辑 - 无论drawTool是否存在都创建基于points的线条
        if (this.points.length > 0) {
          const positions = this.points.map(point =>
            Cesium.Cartesian3.fromDegrees(point[0], point[1], point[2] || 0)
          );

          this.polyline = viewer.entities.add({
            polyline: {
              positions: positions,
              width: 4,
              material: Cesium.Color.LIME,
              clampToGround: false // 设置为false，确保折线不贴地
            }
          });

          const point = this.points[0]
          const altitude = point.length > 2 ? point[2] : 0;
          const position = Cesium.Cartesian3.fromDegrees(point[0], point[1], altitude);

          // 不再使用高度参考系统，直接使用坐标中的高度值
          const entity = viewer.entities.add({
            position: position,
            billboard: {
              image: (() => {
                const canvas = document.createElement('canvas');
                canvas.width = 36;
                canvas.height = 36;
                const context = canvas.getContext('2d');

                context.beginPath();
                context.moveTo(18, 30);  // 底部中点
                context.lineTo(3, 6);    // 左上角
                context.lineTo(33, 6);   // 右上角
                context.closePath();

                context.fillStyle = '#2d8cf0';
                context.fill();

                context.lineWidth = 2;
                context.strokeStyle = '#ffffff';
                context.stroke();

                context.font = 'bold 14px sans-serif';
                context.fillStyle = '#ffffff';
                context.textAlign = 'center';
                context.textBaseline = 'middle';
                context.fillText('S', 18, 14);

                return canvas;
              })(),
              verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
              scale: 1.2,
              width: 36,
              height: 36,
              disableDepthTestDistance: Number.POSITIVE_INFINITY
            },
            altitude: altitude
          });

          this.overlayGroups.push(entity);
        }

        // 仅在DrawTool不存在时创建外部多边形
        if (!this.drawTool && this.Polygon && typeof this.Polygon === 'string' && this.Polygon.trim() !== '') {
          this.createOuterRing(viewer);
        }
      }
    },
    createOuterRing(viewer) {
      if (!this.Polygon || typeof this.Polygon !== 'string') return;

      // 解析多边形字符串
      const positionArray = this.Polygon.split('\n')
        .filter(line => line.trim() !== '')
        .map(line => {
          const parts = line.split(',');
          const lng = parseFloat(parts[0]);
          const lat = parseFloat(parts[1]);
          // 读取高度值，如果存在的话
          const height = parts.length > 2 ? parseFloat(parts[2]) : 0;
          return [lng, lat, height];
        });

      if (positionArray.length < 3) return; // 至少需要3个点才能构成多边形

      // 创建Cesium多边形顶点数组
      const positions = positionArray.map(point =>
        Cesium.Cartesian3.fromDegrees(point[0], point[1], point[2] || 0)
      );

      // 添加多边形实体
      const polygonEntity = viewer.entities.add({
        polygon: {
          hierarchy: new Cesium.PolygonHierarchy(positions),
          material: new Cesium.ColorMaterialProperty(
            Cesium.Color.fromCssColorString('#2d8cf0').withAlpha(0.3)
          ),
          outline: true,
          outlineColor: Cesium.Color.fromCssColorString('#2d8cf0'),
          outlinewidth: 4,
          heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
          extrudedHeight: 0, // 添加拉伸高度，默认为0
          clampToGround: true
        },
        polyline: {
          positions: positions,
          clampToGround: true,
          width: 4,
          material: new Cesium.ColorMaterialProperty(
            Cesium.Color.fromCssColorString('#2d8cf0')
          ),
          depthFailMaterial: new Cesium.ColorMaterialProperty(
            Cesium.Color.fromCssColorString('#2d8cf0')
          ),
          classificationType: Cesium.ClassificationType.BOTH,
          shadows: Cesium.ShadowMode.DISABLED,
          distanceDisplayCondition: new Cesium.DistanceDisplayCondition(0, 9999999)
        }
      });

      // 将多边形实体添加到覆盖组
      this.overlayGroups.push(polygonEntity);
    },
    // 添加起飞点到第一个航点的连线
    createStartPolyline(viewer) {
      if (this.startPoint.length > 0 && this.points.length > 0) {
        // 提取起飞点和第一个航点的高度值
        const startAltitude = this.startPoint.length > 2 ? this.startPoint[2] : 0;
        const endAltitude = this.points[0].length > 2 ? this.points[0][2] : 0;

        // 检查当前是否为2D模式
        const is2DMode = viewer.scene.mode === 2;

        // 根据模式创建不同的路径
        let positions;

        if (is2DMode) {
          // 2D模式：直接连接起飞点和第一个航点
          positions = [
            // 起飞点
            Cesium.Cartesian3.fromDegrees(this.startPoint[0], this.startPoint[1], startAltitude),
            // 第一个航点
            Cesium.Cartesian3.fromDegrees(this.points[0][0], this.points[0][1], endAltitude)
          ];
        } else {
          // 3D模式：使用L形路径（先垂直上升再水平移动）
          positions = [
            // 起飞点
            Cesium.Cartesian3.fromDegrees(this.startPoint[0], this.startPoint[1], startAltitude),
            // 垂直上升点（与起飞点经纬度相同，但高度为第一个航点的高度）
            Cesium.Cartesian3.fromDegrees(this.startPoint[0], this.startPoint[1], endAltitude),
            // 第一个航点
            Cesium.Cartesian3.fromDegrees(this.points[0][0], this.points[0][1], endAltitude)
          ];
        }

        this.startPolyline = viewer.entities.add({
          polyline: {
            positions: positions,
            width: 4,
            material: Cesium.Color.LIME,
            clampToGround: false, // 设置为false，确保连线不贴地,
            horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
            verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
          }
        });
      }
    },
    // 添加航点标记
    createPointIcont(viewer) {
      if (this.points.length > 0) {
        this.points.forEach((point, index) => {
          const altitude = point.length > 2 ? Number(point[2]) : 0;
          const position = Cesium.Cartesian3.fromDegrees(point[0], point[1], altitude);

          const entity = viewer.entities.add({
            position: position,
            billboard: {
              image: '/img/mid.png', // 需要添加航点图标
              verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
              scale: 0.7,
              width: 38,
              height: 60,
              disableDepthTestDistance: Number.POSITIVE_INFINITY
            },
            label: {
              text: (index + 1).toString(),
              font: '24px sans-serif',
              style: Cesium.LabelStyle.FILL_AND_OUTLINE,
              outlinewidth: 4,
              verticalOrigin: Cesium.VerticalOrigin.TOP,
              pixelOffset: new Cesium.Cartesian2(0, -10),
              disableDepthTestDistance: Number.POSITIVE_INFINITY
            },
            pointIndex: index, // 添加点位索引标识
            // 添加高度属性以便于后续操作
            altitude: altitude
          });

          this.overlayGroups.push(entity);
        });
      }
    },
    clearWayLine() {
      const root = getRoot();
      let viewer = root.$cockpitMap;

      if (this.overlay) {
        viewer.entities.remove(this.overlay);
        this.overlay = null;
      }

      // 删除航点标识
      if (this.overlayGroups && this.overlayGroups.length > 0) {
        this.overlayGroups.forEach(entity => {
          viewer.entities.remove(entity);
        });
        this.overlayGroups = [];
      }

      // 删除航线
      if (this.polyline) {
        viewer.entities.remove(this.polyline);
        this.polyline = null;
      }

      // 删除起飞点到第一条线
      if (this.startPolyline) {
        viewer.entities.remove(this.startPolyline);
        this.startPolyline = null;
      }

      // 删除距离文字
      if (this.texts && this.texts.length > 0) {
        this.texts.forEach(entity => {
          viewer.entities.remove(entity);
        });
        this.texts = [];  // 修改为空数组而不是null
      }
    },
    // 计算航线两点之间中心点
    getMidPoints(pointA, pointB) {
      // 点A和点B的经纬度坐标
      let lat1 = pointA[1];
      let lon1 = pointA[0];
      let lat2 = pointB[1];
      let lon2 = pointB[0];
      // 将经纬度从十进制转换为弧度
      lat1 = lat1 * Math.PI / 180;
      lon1 = lon1 * Math.PI / 180;
      lat2 = lat2 * Math.PI / 180;
      lon2 = lon2 * Math.PI / 180;
      // 计算中心点的经纬度（弧度）
      let midLat = (lat1 + lat2) / 2;
      let midLon = (lon1 + lon2) / 2;
      // 将结果从弧度转换回十进制
      midLat = midLat * 180 / Math.PI;
      midLon = midLon * 180 / Math.PI;
      return [midLon, midLat];
    },
    handleCloseCockpit() {
      this.$refs.videoMap.onStop();
      this.$emit('closeCockpit', false);
    },
    changeUrl(url) {
      this.algorithmUrl = url;
      console.log(this.algorithmUrl);
    },
    changeDomain(domain) {
      console.log('openCockpit', domain);
      this.algorithmUrl = ''
      this.cockpit_dock = {
        ...domain,
        is_dock: true
      }
      if (this.center.length == 0 && domain.longitude && domain.latitude) {
        this.center = [domain.longitude, domain.latitude];
      }

      if (this.deviceInfo[domain.sn] && this.deviceInfo[domain.sn].longitude && this.deviceInfo[domain.sn].latitude) {
        this.center = [this.deviceInfo[domain.sn].longitude, this.deviceInfo[domain.sn].latitude]
      } else if (this.dockInfo[domain.gateway.sn] && this.dockInfo[domain.gateway.sn].basic_osd.longitude && this.dockInfo[domain.gateway.sn].basic_osd.latitude) {
        this.center = [this.dockInfo[domain.gateway.sn].basic_osd.longitude, this.dockInfo[domain.gateway.sn].basic_osd.latitude]
      } else if (this.gatewayInfo[domain.gateway.sn]) {
        this.center = [this.gatewayInfo[domain.gateway.sn].longitude, this.gatewayInfo[domain.gateway.sn].latitude]
      } else {
        //默认位置
        this.center = [120.23670203353453, 30.301917939866275]
      }

      this.osdVisible.sn = domain.sn
      this.osdVisible.callsign = domain.callsign
      this.osdVisible.model = domain.model
      this.osdVisible.visible = true
      this.osdVisible.gateway_sn = domain.gateway.sn
      this.osdVisible.is_dock = true
      this.osdVisible.gateway_callsign = domain.gateway.callsign
      this.osdVisible.payloads = domain.payload
      store.commit('SET_OSD_VISIBLE_INFO', this.osdVisible)
      store.commit("COCKPIT_SET", true)
      // }else{
      // 	message.error('无人机未在线')
      // }
    },
    shot(e) {
      // console.log(e)
      // send({code:'image',data:e})
      var base64ToBlob = function (code) {
        let parts = code.split(';base64,');
        let contentType = parts[0].split(':')[1];
        let raw = window.atob(parts[1]);
        let rawLength = raw.length;
        let uInt8Array = new Uint8Array(rawLength);
        for (let i = 0; i < rawLength; ++i) {
          uInt8Array[i] = raw.charCodeAt(i);
        }
        return new Blob([uInt8Array], {
          type: contentType,
        });
      };
      let aLink = document.createElement('a');
      let blob = base64ToBlob(e); //new Blob([content]);
      let evt = document.createEvent('HTMLEvents');
      evt.initEvent('click', true, true); //initEvent 不加后两个参数在FF下会报错  事件类型，是否冒泡，是否阻止浏览器的默认行为
      aLink.download = '截图';
      aLink.href = URL.createObjectURL(blob);
      aLink.click();
    },
    destroy(idx) {
      console.log(idx);
      this.clear(idx.substring(idx.length - 1));
    },
  }
}
</script>
<style lang="scss" scoped>
.cockpitMap {
  position: relative;
  width: 100%;
  height: 100%;
}

.back-button {
  position: absolute;
  top: 5px;
  left: 20px;
  background: rgba(0, 0, 0, 0.6);
  color: #fff;
  padding: 8px 15px;
  border-radius: 4px;
  cursor: pointer;
  z-index: 10;
  display: flex;
  align-items: center;
  font-size: 14px;

  &:hover {
    background: rgba(0, 0, 0, 0.8);
  }

  i {
    margin-right: 5px;
  }
}

.map-container {
  width: 100%;
  height: 100%
}

.map-container-small {
  position: absolute;
  width: 25vw;
  height: 320px;
  left: 0;
  bottom: 0;
  z-index: 1;
}

.video_box {
  background: rgba(60, 60, 60, 0.38);
  position: absolute;
  bottom: -6vh;
  left: 7vw;
  transform: translateX(-50%);
  border-radius: 0.25rem;
  color: #fff;
  scale: 0.7;
  width: 35vw;
}

.z_index1 {
  z-index: 1;
}

.video-big {
  background: rgba(60, 60, 60, 0.38);
  position: absolute;
  /* bottom: 32vh; */
  /* left: 80vw; */
  /* transform: translateX(-50%); */
  border-radius: 0.25rem;
  color: #fff;
  scale: 1;
  width: 50vw;
  height: 70vh;
  // top: 42px;
  left: 25vw;
}

::v-deep(.amap-marker .amap-ranging-label) {
  background: #15b8ff;
}

:deep(.cesium-viewer-bottom) {
  display: none;
}

.alarm-message-container {
  width: 25vw;
  height: 60vh;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
}

.right-container {
  position: absolute;
  top: 0;
  right: 0;
  width: 25vw;
  height: 72vh;
  z-index: 1;
  background-color: #28323e;
}
</style>