{"asset": {"gltfUpAxis": "Z", "version": "0.0"}, "geometricError": 0.9124380350112915, "root": {"boundingVolume": {"box": [390.11468505859375, -106.03623962402344, -149.36587524414062, 109.71501159667969, 0.0, 0.0, 0.0, 168.96083068847656, 0.0, 0.0, 0.0, 23.69415283203125]}, "children": [{"boundingVolume": {"box": [380.9738464355469, -161.7972869873047, -150.23704528808594, 100.57418823242188, 0.0, 0.0, 0.0, 113.19978332519531, 0.0, 0.0, 0.0, 22.822978973388672]}, "children": [{"boundingVolume": {"box": [380.9738464355469, -160.11961364746094, -150.23704528808594, 100.57418823242188, 0.0, 0.0, 0.0, 111.52210998535156, 0.0, 0.0, 0.0, 22.822978973388672]}, "children": [{"boundingVolume": {"box": [379.6558532714844, -206.587158203125, -149.15530395507812, 99.25619506835938, 0.0, 0.0, 0.0, 65.0545654296875, 0.0, 0.0, 0.0, 18.702919006347656]}, "children": [{"boundingVolume": {"box": [313.48504638671875, -206.587158203125, -148.83811950683594, 33.08540344238281, 0.0, 0.0, 0.0, 65.0545654296875, 0.0, 0.0, 0.0, 18.3857421875]}, "content": {"uri": "Block_L20_41.b3dm"}, "geometricError": 0.06472840160131454, "refine": "REPLACE"}, {"boundingVolume": {"box": [412.74127197265625, -199.00575256347656, -160.322509765625, 66.17079162597656, 0.0, 0.0, 0.0, 57.47315979003906, 0.0, 0.0, 0.0, 7.53570556640625]}, "content": {"uri": "Block_L20_40.b3dm"}, "geometricError": 0.07228078693151474, "refine": "REPLACE"}], "content": {"uri": "Block_L19_30.b3dm"}, "geometricError": 0.1360929310321808, "refine": "REPLACE"}, {"boundingVolume": {"box": [380.9738464355469, -95.06504821777344, -150.23704528808594, 100.57418823242188, 0.0, 0.0, 0.0, 46.46754455566406, 0.0, 0.0, 0.0, 22.822978973388672]}, "children": [{"boundingVolume": {"box": [322.3055725097656, -95.06504821777344, -147.61419677734375, 41.905914306640625, 0.0, 0.0, 0.0, 46.46754455566406, 0.0, 0.0, 0.0, 20.200130462646484]}, "content": {"uri": "Block_L20_39.b3dm"}, "geometricError": 0.057203277945518494, "refine": "REPLACE"}, {"boundingVolume": {"box": [422.8797607421875, -95.06504821777344, -162.67665100097656, 58.66827392578125, 0.0, 0.0, 0.0, 46.46754455566406, 0.0, 0.0, 0.0, 10.383377075195312]}, "content": {"uri": "Block_L20_38.b3dm"}, "geometricError": 0.06831886619329453, "refine": "REPLACE"}], "content": {"uri": "Block_L19_29.b3dm"}, "geometricError": 0.12472052127122879, "refine": "REPLACE"}], "content": {"uri": "Block_L18_16.b3dm"}, "geometricError": 0.2603737711906433, "refine": "REPLACE"}], "content": {"uri": "Block_L17_10.b3dm"}, "geometricError": 0.5236411094665527, "refine": "REPLACE"}, {"boundingVolume": {"box": [390.11468505859375, 7.163543701171875, -148.9951171875, 109.71501159667969, 0.0, 0.0, 0.0, 55.76104736328125, 0.0, 0.0, 0.0, 23.323402404785156]}, "children": [{"boundingVolume": {"box": [316.9713134765625, 7.163543701171875, -145.86495971679688, 36.57167053222656, 0.0, 0.0, 0.0, 55.76104736328125, 0.0, 0.0, 0.0, 20.19322967529297]}, "children": [{"boundingVolume": {"box": [316.9713134765625, -20.71697998046875, -145.878662109375, 36.57167053222656, 0.0, 0.0, 0.0, 27.880523681640625, 0.0, 0.0, 0.0, 20.17951202392578]}, "children": [{"boundingVolume": {"box": [295.6378479003906, -20.71697998046875, -145.79269409179688, 15.238189697265625, 0.0, 0.0, 0.0, 27.880523681640625, 0.0, 0.0, 0.0, 20.080856323242188]}, "content": {"uri": "Block_L20_37.b3dm"}, "geometricError": 0.0458780862390995, "refine": "REPLACE"}, {"boundingVolume": {"box": [332.20953369140625, -20.71697998046875, -146.25392150878906, 21.333480834960938, 0.0, 0.0, 0.0, 27.880523681640625, 0.0, 0.0, 0.0, 19.804256439208984]}, "content": {"uri": "Block_L20_36.b3dm"}, "geometricError": 0.04711023345589638, "refine": "REPLACE"}], "content": {"uri": "Block_L19_28.b3dm"}, "geometricError": 0.09301676601171494, "refine": "REPLACE"}, {"boundingVolume": {"box": [316.9713134765625, 35.0440673828125, -149.11343383789062, 36.57167053222656, 0.0, 0.0, 0.0, 27.880523681640625, 0.0, 0.0, 0.0, 16.333511352539062]}, "children": [{"boundingVolume": {"box": [295.6378479003906, 35.0440673828125, -149.03125, 15.238189697265625, 0.0, 0.0, 0.0, 27.880523681640625, 0.0, 0.0, 0.0, 15.998023986816406]}, "content": {"uri": "Block_L20_35.b3dm"}, "geometricError": 0.04707767814397812, "refine": "REPLACE"}, {"boundingVolume": {"box": [332.20953369140625, 35.0440673828125, -149.1073455810547, 21.333480834960938, 0.0, 0.0, 0.0, 27.880523681640625, 0.0, 0.0, 0.0, 16.327423095703125]}, "content": {"uri": "Block_L20_34.b3dm"}, "geometricError": 0.04751085489988327, "refine": "REPLACE"}], "content": {"uri": "Block_L19_27.b3dm"}, "geometricError": 0.09473934769630432, "refine": "REPLACE"}], "content": {"uri": "Block_L18_15.b3dm"}, "geometricError": 0.18865378201007843, "refine": "REPLACE"}, {"boundingVolume": {"box": [426.68634033203125, 7.163543701171875, -162.32577514648438, 73.14334106445312, 0.0, 0.0, 0.0, 55.76104736328125, 0.0, 0.0, 0.0, 9.992752075195312]}, "children": [{"boundingVolume": {"box": [426.68634033203125, 7.163543701171875, -162.3367919921875, 73.14334106445312, 0.0, 0.0, 0.0, 55.76104736328125, 0.0, 0.0, 0.0, 9.981727600097656]}, "children": [{"boundingVolume": {"box": [377.92413330078125, -20.71697998046875, -161.29258728027344, 24.381118774414062, 0.0, 0.0, 0.0, 27.880523681640625, 0.0, 0.0, 0.0, 5.8206634521484375]}, "content": {"uri": "Block_L20_33.b3dm"}, "geometricError": 0.05717030167579651, "refine": "REPLACE"}, {"boundingVolume": {"box": [377.92413330078125, 35.0440673828125, -159.02154541015625, 24.381118774414062, 0.0, 0.0, 0.0, 27.880523681640625, 0.0, 0.0, 0.0, 6.362174987792969]}, "content": {"uri": "Block_L20_32.b3dm"}, "geometricError": 0.053233370184898376, "refine": "REPLACE"}, {"boundingVolume": {"box": [451.06744384765625, 7.163543701171875, -162.3367919921875, 48.76222229003906, 0.0, 0.0, 0.0, 55.76104736328125, 0.0, 0.0, 0.0, 9.981727600097656]}, "content": {"uri": "Block_L20_31.b3dm"}, "geometricError": 0.06634367257356644, "refine": "REPLACE"}], "content": {"uri": "Block_L19_26.b3dm"}, "geometricError": 0.11896196007728577, "refine": "REPLACE"}], "content": {"uri": "Block_L18_14.b3dm"}, "geometricError": 0.2392326444387436, "refine": "REPLACE"}], "content": {"uri": "Block_L17_9.b3dm"}, "geometricError": 0.4127097725868225, "refine": "REPLACE"}], "content": {"uri": "Block_L16_4.b3dm"}, "geometricError": 0.9124380350112915, "refine": "REPLACE"}}