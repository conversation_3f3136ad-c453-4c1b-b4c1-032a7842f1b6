import request from '@/axios';
import {ELocalStorageKey} from '@/api/enum/index';
import { getWorkspaceId } from '@/utils/storage'

export const devicesList = (params) => {
  return request({
    url: '/hztech-flight-core/manage/api/v1/devices/' + getWorkspaceId() + '/devices/bound',
    method: 'get',
    params: {
      domain: params.domain,
      page: params.currentPage,
      page_size: params.pageSize,
    }
  })
}

//删除设备
export const deleteDevice = (deviceSn) => {
  return request({
    url: '/hztech-flight-core/manage/api/v1/devices/' + deviceSn + '/unbinding',
    method: 'DELETE',
  })
}

//修改设备
export const updateDevice = (deviceSn, params) => {
  return request({
    url: '/hztech-flight-core/manage/api/v1/devices/' + getWorkspaceId() + '/devices/' + deviceSn + '',
    method: 'put',
    data: params
  })
}

//获取实时更新进度
export const updateStatus = (sn) => {
  return request({
    url: '/hztech-flight-core/manage/api/v1/devices/' + getWorkspaceId() + '/devices/updateStatus',
    method: 'get',
    params: {
      deviceSn: sn
    }
  })
}