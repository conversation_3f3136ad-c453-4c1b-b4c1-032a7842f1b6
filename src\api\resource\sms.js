import request from '@/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/hztech-resource/sms/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    },
  });
};

export const getDetail = id => {
  return request({
    url: '/hztech-resource/sms/detail',
    method: 'get',
    params: {
      id,
    },
  });
};

export const remove = ids => {
  return request({
    url: '/hztech-resource/sms/remove',
    method: 'post',
    params: {
      ids,
    },
  });
};

export const add = row => {
  return request({
    url: '/hztech-resource/sms/submit',
    method: 'post',
    data: row,
  });
};

export const update = row => {
  return request({
    url: '/hztech-resource/sms/submit',
    method: 'post',
    data: row,
  });
};

export const enable = id => {
  return request({
    url: '/hztech-resource/sms/enable',
    method: 'post',
    params: {
      id,
    },
  });
};

export const send = (code, phones, params) => {
  return request({
    url: '/hztech-resource/sms/endpoint/send-message',
    method: 'post',
    params: {
      code,
      phones,
      params,
    },
  });
};
