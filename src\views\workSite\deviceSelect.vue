<template>
    <div class="device-select">
        <div class="item-group">
            算法:<el-select v-model="algoId" placeholder="请选择算法类型">
                <el-option v-for="item in algorithmList" :key="item.algoId" :value="item.algoId"
                    :label="item.algoName"></el-option>
            </el-select>
        </div>
        <div class="item-group">
            设备:<el-select v-model="cameraId" placeholder="请选择设备">
                <el-option v-for="item in deviceList" :key="item.cameraId" :value="item.cameraId"
                    :label="item.name"></el-option>
            </el-select>
        </div>
    </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue';
import { getHostDeviceList, getAlgorithmList, getAlgoHosts,getPlayAlgoList } from '@/api/workSite/index.js';

const algoId = ref('')
const algorithmList = ref([])
const cameraId = ref('')
const deviceList = ref([])
const algoHosts = ref([])
const hostId = ref('')

// 获取设备列表函数
// const fetchDeviceList = async () => {
//     if (!hostId.value) return

//     const res = await getHostDeviceList(1, 100, { hostId: hostId.value })
//     deviceList.value = res.data.data.records
//     if (deviceList.value.length > 0) {
//         cameraId.value = deviceList.value[0].cameraId
//     } else {
//         cameraId.value = ''
//     }
// }
const fetchDeviceList = async () => {
    if (!hostId.value) return

    const res = await getPlayAlgoList(1, 100, { algoId: algoId.value })
    deviceList.value = res.data.data.records
    if (deviceList.value.length > 0) {
        cameraId.value = deviceList.value[0].cameraId
    } else {
        cameraId.value = ''
    }
}

const fetchAlgoHosts = async (id) => {
    const res = await getAlgoHosts(1, 100, { algoId: id })
    algoHosts.value = res.data.data.hosts
    console.log(algoHosts.value)
    hostId.value = algoHosts.value[0].hostId
    console.log(hostId.value)
}

onMounted(async () => {
    const res = await getAlgorithmList(1, 100, { algoType: 5 })
    algorithmList.value = res.data.data.records
    if (algorithmList.value.length > 0) {
        algoId.value = algorithmList.value[0].algoId
    }
})

// 监听算法ID变化，重新获取设备列表
watch(algoId, async (newAlgoId) => {
    if (newAlgoId) {
        await fetchAlgoHosts(newAlgoId)
        await fetchDeviceList()
    }
})

</script>

<style lang="scss" scoped>
.device-select {
    display: flex;
    align-items: center;
    flex-direction: row;
    flex-wrap: nowrap;
    gap: 20px;
    margin: 10px;
    background-color: #454f5d;
    color: white;
    padding: 10px;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);

    .item-group {
        display: flex;
        align-items: center;
        white-space: nowrap;
    }

    :deep(.el-select) {
        margin-left: 8px;
        width: 140px;
    }

    :deep(.el-input__wrapper) {
        background-color: rgba(255, 255, 255, 0.1);
        box-shadow: 0 0 0 1px rgba(255, 255, 255, 0.2) inset;
    }

    :deep(.el-input__inner) {
        color: white;
    }

    :deep(.el-select__popper) {
        background-color: #454f5d;
        border-color: rgba(255, 255, 255, 0.2);
    }
}
</style>