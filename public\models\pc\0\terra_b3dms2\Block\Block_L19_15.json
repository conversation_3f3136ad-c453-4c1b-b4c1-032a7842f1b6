{"asset": {"gltfUpAxis": "Z", "version": "0.0"}, "geometricError": 0.15985989570617676, "root": {"boundingVolume": {"box": [41.73459243774414, 22.74590492248535, -38.383365631103516, 7.193416595458984, 0.0, 0.0, 0.0, 13.618413925170898, 0.0, 0.0, 0.0, 6.912041664123535]}, "children": [{"boundingVolume": {"box": [38.13788604736328, 15.936697959899902, -35.777523040771484, 3.596708297729492, 0.0, 0.0, 0.0, 6.809206962585449, 0.0, 0.0, 0.0, 4.306200981140137]}, "children": [{"boundingVolume": {"box": [38.13788604736328, 12.532094955444336, -35.06785202026367, 3.596708297729492, 0.0, 0.0, 0.0, 3.4046034812927246, 0.0, 0.0, 0.0, 3.579440116882324]}, "children": [{"boundingVolume": {"box": [38.13788604736328, 12.532094955444336, -35.06592559814453, 3.596708297729492, 0.0, 0.0, 0.0, 3.4046034812927246, 0.0, 0.0, 0.0, 3.577511787414551]}, "children": [{"boundingVolume": {"box": [36.33953094482422, 12.532094955444336, -34.95271301269531, 1.7983551025390625, 0.0, 0.0, 0.0, 3.4046034812927246, 0.0, 0.0, 0.0, 3.464299201965332]}, "content": {"uri": "Block_L23_24.b3dm"}, "geometricError": 0.009051756002008915, "refine": "REPLACE"}, {"boundingVolume": {"box": [39.936241149902344, 12.532094955444336, -35.084259033203125, 1.7983531951904297, 0.0, 0.0, 0.0, 3.4046034812927246, 0.0, 0.0, 0.0, 3.5579328536987305]}, "content": {"uri": "Block_L23_23.b3dm"}, "geometricError": 0.00883525237441063, "refine": "REPLACE"}], "content": {"uri": "Block_L22_76.b3dm"}, "geometricError": 0.01803533174097538, "refine": "REPLACE"}], "content": {"uri": "Block_L21_58.b3dm"}, "geometricError": 0.03609583154320717, "refine": "REPLACE"}, {"boundingVolume": {"box": [38.13788604736328, 19.34130096435547, -35.777523040771484, 3.596708297729492, 0.0, 0.0, 0.0, 3.4046034812927246, 0.0, 0.0, 0.0, 4.306200981140137]}, "children": [{"boundingVolume": {"box": [38.13788604736328, 19.34130096435547, -35.777523040771484, 3.596708297729492, 0.0, 0.0, 0.0, 3.4046034812927246, 0.0, 0.0, 0.0, 4.306200981140137]}, "children": [{"boundingVolume": {"box": [38.13788604736328, 19.34130096435547, -38.28947448730469, 3.596708297729492, 0.0, 0.0, 0.0, 3.4046034812927246, 0.0, 0.0, 0.0, 1.79425048828125]}, "content": {"uri": "Block_L23_22.b3dm"}, "geometricError": 0.009931263513863087, "refine": "REPLACE"}, {"boundingVolume": {"box": [38.13788604736328, 19.34130096435547, -33.983272552490234, 3.596708297729492, 0.0, 0.0, 0.0, 3.4046034812927246, 0.0, 0.0, 0.0, 2.5119504928588867]}, "content": {"uri": "Block_L23_21.b3dm"}, "geometricError": 0.009232205338776112, "refine": "REPLACE"}], "content": {"uri": "Block_L22_75.b3dm"}, "geometricError": 0.019295020028948784, "refine": "REPLACE"}], "content": {"uri": "Block_L21_57.b3dm"}, "geometricError": 0.038565099239349365, "refine": "REPLACE"}], "content": {"uri": "Block_L20_30.b3dm"}, "geometricError": 0.07478414475917816, "refine": "REPLACE"}, {"boundingVolume": {"box": [45.331298828125, 15.936697959899902, -36.109779357910156, 3.596708297729492, 0.0, 0.0, 0.0, 6.809206962585449, 0.0, 0.0, 0.0, 4.23798942565918]}, "children": [{"boundingVolume": {"box": [45.331298828125, 15.936697959899902, -36.104820251464844, 3.596708297729492, 0.0, 0.0, 0.0, 6.809206962585449, 0.0, 0.0, 0.0, 4.231869697570801]}, "children": [{"boundingVolume": {"box": [45.331298828125, 12.532094955444336, -35.48283386230469, 3.596708297729492, 0.0, 0.0, 0.0, 3.4046034812927246, 0.0, 0.0, 0.0, 3.578807830810547]}, "children": [{"boundingVolume": {"box": [43.832672119140625, 12.532094955444336, -35.395050048828125, 2.0980796813964844, 0.0, 0.0, 0.0, 3.4046034812927246, 0.0, 0.0, 0.0, 3.4766550064086914]}, "content": {"uri": "Block_L23_20.b3dm"}, "geometricError": 0.009275841526687145, "refine": "REPLACE"}, {"boundingVolume": {"box": [47.42938232421875, 12.532094955444336, -35.73090362548828, 1.4986286163330078, 0.0, 0.0, 0.0, 3.4046034812927246, 0.0, 0.0, 0.0, 3.330738067626953]}, "content": {"uri": "Block_L23_19.b3dm"}, "geometricError": 0.009601601399481297, "refine": "REPLACE"}], "content": {"uri": "Block_L22_74.b3dm"}, "geometricError": 0.018909092992544174, "refine": "REPLACE"}, {"boundingVolume": {"box": [45.331298828125, 19.34130096435547, -37.78834915161133, 3.596708297729492, 0.0, 0.0, 0.0, 3.4046034812927246, 0.0, 0.0, 0.0, 2.5474510192871094]}, "children": [{"boundingVolume": {"box": [43.23322296142578, 19.34130096435547, -37.72338104248047, 1.4986286163330078, 0.0, 0.0, 0.0, 3.4046034812927246, 0.0, 0.0, 0.0, 2.482484817504883]}, "content": {"uri": "Block_L23_18.b3dm"}, "geometricError": 0.009745153598487377, "refine": "REPLACE"}, {"boundingVolume": {"box": [46.82992935180664, 19.34130096435547, -37.80982208251953, 2.0980796813964844, 0.0, 0.0, 0.0, 3.4046034812927246, 0.0, 0.0, 0.0, 2.5259780883789062]}, "content": {"uri": "Block_L23_17.b3dm"}, "geometricError": 0.010057206265628338, "refine": "REPLACE"}], "content": {"uri": "Block_L22_73.b3dm"}, "geometricError": 0.019802924245595932, "refine": "REPLACE"}], "content": {"uri": "Block_L21_56.b3dm"}, "geometricError": 0.03864471986889839, "refine": "REPLACE"}], "content": {"uri": "Block_L20_29.b3dm"}, "geometricError": 0.07728742063045502, "refine": "REPLACE"}, {"boundingVolume": {"box": [41.73459243774414, 29.555110931396484, -40.182945251464844, 7.193416595458984, 0.0, 0.0, 0.0, 6.809206962585449, 0.0, 0.0, 0.0, 5.110902786254883]}, "children": [{"boundingVolume": {"box": [38.13788604736328, 29.555110931396484, -40.187965393066406, 3.596708297729492, 0.0, 0.0, 0.0, 6.809206962585449, 0.0, 0.0, 0.0, 5.103567123413086]}, "children": [{"boundingVolume": {"box": [38.13788604736328, 25.58307456970215, -39.108375549316406, 3.596708297729492, 0.0, 0.0, 0.0, 2.837169647216797, 0.0, 0.0, 0.0, 4.020715713500977]}, "children": [{"boundingVolume": {"box": [38.13788604736328, 25.58307456970215, -39.05146789550781, 3.596708297729492, 0.0, 0.0, 0.0, 2.837169647216797, 0.0, 0.0, 0.0, 3.960163116455078]}, "content": {"uri": "Block_L23_16.b3dm"}, "geometricError": 0.010651469230651855, "refine": "REPLACE"}], "content": {"uri": "Block_L22_72.b3dm"}, "geometricError": 0.02129940502345562, "refine": "REPLACE"}, {"boundingVolume": {"box": [38.13788604736328, 32.39228057861328, -43.3189582824707, 3.596708297729492, 0.0, 0.0, 0.0, 3.9720373153686523, 0.0, 0.0, 0.0, 1.9725723266601562]}, "children": [{"boundingVolume": {"box": [38.13788604736328, 32.39228057861328, -43.3189582824707, 3.596708297729492, 0.0, 0.0, 0.0, 3.9720373153686523, 0.0, 0.0, 0.0, 1.9725723266601562]}, "content": {"uri": "Block_L22_20.b3dm"}, "geometricError": 0.011410999111831188, "refine": "REPLACE"}], "content": {"uri": "Block_L21_15.b3dm"}, "geometricError": 0.022813545539975166, "refine": "REPLACE"}], "content": {"uri": "Block_L21_55.b3dm"}, "geometricError": 0.044009722769260406, "refine": "REPLACE"}, {"boundingVolume": {"box": [45.331298828125, 29.555110931396484, -41.359764099121094, 3.596708297729492, 0.0, 0.0, 0.0, 6.809206962585449, 0.0, 0.0, 0.0, 3.685445785522461]}, "children": [{"boundingVolume": {"box": [45.331298828125, 25.58307456970215, -39.74309539794922, 3.596708297729492, 0.0, 0.0, 0.0, 2.837169647216797, 0.0, 0.0, 0.0, 2.068777084350586]}, "children": [{"boundingVolume": {"box": [45.331298828125, 25.58307456970215, -39.70018005371094, 3.596708297729492, 0.0, 0.0, 0.0, 2.837169647216797, 0.0, 0.0, 0.0, 2.0258617401123047]}, "content": {"uri": "Block_L23_15.b3dm"}, "geometricError": 0.010432534851133823, "refine": "REPLACE"}], "content": {"uri": "Block_L22_71.b3dm"}, "geometricError": 0.020871929824352264, "refine": "REPLACE"}, {"boundingVolume": {"box": [45.331298828125, 32.39228057861328, -42.31898498535156, 3.596708297729492, 0.0, 0.0, 0.0, 3.9720373153686523, 0.0, 0.0, 0.0, 2.726224899291992]}, "children": [{"boundingVolume": {"box": [45.331298828125, 32.39228057861328, -42.31898498535156, 3.596708297729492, 0.0, 0.0, 0.0, 3.9720373153686523, 0.0, 0.0, 0.0, 2.726224899291992]}, "content": {"uri": "Block_L23_14.b3dm"}, "geometricError": 0.010923991911113262, "refine": "REPLACE"}], "content": {"uri": "Block_L22_70.b3dm"}, "geometricError": 0.021838368847966194, "refine": "REPLACE"}], "content": {"uri": "Block_L21_54.b3dm"}, "geometricError": 0.04269761964678764, "refine": "REPLACE"}], "content": {"uri": "Block_L20_28.b3dm"}, "geometricError": 0.08672367036342621, "refine": "REPLACE"}], "content": {"uri": "Block_L19_15.b3dm"}, "geometricError": 0.15985989570617676, "refine": "REPLACE"}}