{"asset": {"gltfUpAxis": "Z", "version": "0.0"}, "geometricError": 10000000000.0, "root": {"boundingVolume": {"box": [20.154348373413086, 6.200321197509766, -37.91358184814453, 73.14938354492188, 0.0, 0.0, 0.0, 70.09600830078125, 0.0, 0.0, 0.0, 17.47840690612793]}, "children": [{"boundingVolume": {"box": [-13.778310775756836, -28.847686767578125, -31.792964935302734, 33.932655334472656, 0.0, 0.0, 0.0, 35.048004150390625, 0.0, 0.0, 0.0, 11.357791900634766]}, "children": [{"boundingVolume": {"box": [-13.778310775756836, -28.847686767578125, -31.742969512939453, 33.932655334472656, 0.0, 0.0, 0.0, 35.048004150390625, 0.0, 0.0, 0.0, 11.307796478271484]}, "children": [{"boundingVolume": {"box": [-7.610729217529297, -43.45102310180664, -34.68629455566406, 27.76507568359375, 0.0, 0.0, 0.0, 20.44466781616211, 0.0, 0.0, 0.0, 8.139058113098145]}, "children": [{"boundingVolume": {"box": [-6.60688591003418, -43.45102310180664, -34.64681625366211, 26.761232376098633, 0.0, 0.0, 0.0, 20.44466781616211, 0.0, 0.0, 0.0, 8.099579811096191]}, "content": {"uri": "Block_L18_16.json"}, "geometricError": 10000000000.0, "refine": "REPLACE"}], "content": {"uri": "Block_L17_8.b3dm"}, "geometricError": 0.6279066801071167, "refine": "REPLACE"}, {"boundingVolume": {"box": [-13.778310775756836, -8.403018951416016, -29.20407485961914, 33.932655334472656, 0.0, 0.0, 0.0, 14.603336334228516, 0.0, 0.0, 0.0, 8.768901824951172]}, "children": [{"boundingVolume": {"box": [-30.726146697998047, -8.403018951416016, -29.173179626464844, 16.984821319580078, 0.0, 0.0, 0.0, 14.603336334228516, 0.0, 0.0, 0.0, 8.738006591796875]}, "children": [{"boundingVolume": {"box": [-37.78774642944336, -8.403018951416016, -33.27339553833008, 9.923221588134766, 0.0, 0.0, 0.0, 14.603336334228516, 0.0, 0.0, 0.0, 4.616057395935059]}, "content": {"uri": "Block_L19_33.json"}, "geometricError": 10000000000.0, "refine": "REPLACE"}, {"boundingVolume": {"box": [-20.80292510986328, -15.704687118530273, -27.860851287841797, 7.0615997314453125, 0.0, 0.0, 0.0, 7.301668167114258, 0.0, 0.0, 0.0, 7.425678253173828]}, "content": {"uri": "Block_L19_32.json"}, "geometricError": 10000000000.0, "refine": "REPLACE"}, {"boundingVolume": {"box": [-20.80292510986328, -1.1013507843017578, -35.009681701660156, 7.0615997314453125, 0.0, 0.0, 0.0, 7.301668167114258, 0.0, 0.0, 0.0, 2.893239974975586]}, "content": {"uri": "Block_L19_31.json"}, "geometricError": 10000000000.0, "refine": "REPLACE"}], "content": {"uri": "Block_L18_15.b3dm"}, "geometricError": 0.2802487015724182, "refine": "REPLACE"}, {"boundingVolume": {"box": [-6.679727077484131, -8.403018951416016, -29.164997100830078, 7.061598300933838, 0.0, 0.0, 0.0, 14.603336334228516, 0.0, 0.0, 0.0, 8.72982406616211]}, "children": [{"boundingVolume": {"box": [-6.679727077484131, -16.921630859375, -29.140417098999023, 7.061598300933838, 0.0, 0.0, 0.0, 6.084723472595215, 0.0, 0.0, 0.0, 8.705244064331055]}, "content": {"uri": "Block_L19_30.json"}, "geometricError": 10000000000.0, "refine": "REPLACE"}, {"boundingVolume": {"box": [-6.679727077484131, -2.318295478820801, -33.494773864746094, 7.061598300933838, 0.0, 0.0, 0.0, 8.5186128616333, 0.0, 0.0, 0.0, 4.379637718200684]}, "content": {"uri": "Block_L19_29.json"}, "geometricError": 10000000000.0, "refine": "REPLACE"}], "content": {"uri": "Block_L18_14.b3dm"}, "geometricError": 0.27263250946998596, "refine": "REPLACE"}, {"boundingVolume": {"box": [10.268108367919922, -8.403018951416016, -35.09156799316406, 9.886238098144531, 0.0, 0.0, 0.0, 14.603336334228516, 0.0, 0.0, 0.0, 2.88140869140625]}, "children": [{"boundingVolume": {"box": [10.268108367919922, -14.48774242401123, -35.20203399658203, 9.886238098144531, 0.0, 0.0, 0.0, 8.5186128616333, 0.0, 0.0, 0.0, 2.765687942504883]}, "content": {"uri": "Block_L19_28.json"}, "geometricError": 10000000000.0, "refine": "REPLACE"}, {"boundingVolume": {"box": [10.268108367919922, 0.11559391021728516, -34.85173034667969, 9.886238098144531, 0.0, 0.0, 0.0, 6.084723472595215, 0.0, 0.0, 0.0, 2.641569137573242]}, "content": {"uri": "Block_L19_27.json"}, "geometricError": 10000000000.0, "refine": "REPLACE"}], "content": {"uri": "Block_L18_13.b3dm"}, "geometricError": 0.29846835136413574, "refine": "REPLACE"}], "content": {"uri": "Block_L17_7.b3dm"}, "geometricError": 0.5623301267623901, "refine": "REPLACE"}], "content": {"uri": "Block_L16_5.b3dm"}, "geometricError": 1.174730896949768, "refine": "REPLACE"}], "content": {"uri": "Block_L15_4.b3dm"}, "geometricError": 2.3527281284332275, "refine": "REPLACE"}, {"boundingVolume": {"box": [-16.420345306396484, 41.24832534790039, -38.805015563964844, 36.57469177246094, 0.0, 0.0, 0.0, 35.04800796508789, 0.0, 0.0, 0.0, 10.76684856414795]}, "children": [{"boundingVolume": {"box": [-16.420345306396484, 41.24832534790039, -38.805015563964844, 36.57469177246094, 0.0, 0.0, 0.0, 35.04800796508789, 0.0, 0.0, 0.0, 10.76684856414795]}, "children": [{"boundingVolume": {"box": [-16.420345306396484, 41.24832534790039, -38.805015563964844, 36.57469177246094, 0.0, 0.0, 0.0, 35.04800796508789, 0.0, 0.0, 0.0, 10.76684856414795]}, "children": [{"boundingVolume": {"box": [-31.659801483154297, 39.7519645690918, -40.698081970214844, 21.335235595703125, 0.0, 0.0, 0.0, 33.5516471862793, 0.0, 0.0, 0.0, 6.864229202270508]}, "children": [{"boundingVolume": {"box": [-40.54948425292969, 20.1435604095459, -38.568817138671875, 12.44555377960205, 0.0, 0.0, 0.0, 13.943243026733398, 0.0, 0.0, 0.0, 4.124416351318359]}, "content": {"uri": "Block_L19_26.json"}, "geometricError": 10000000000.0, "refine": "REPLACE"}, {"boundingVolume": {"box": [-19.214248657226562, 20.1435604095459, -39.58454895019531, 8.889681816101074, 0.0, 0.0, 0.0, 13.943243026733398, 0.0, 0.0, 0.0, 3.108682632446289]}, "content": {"uri": "Block_L19_25.json"}, "geometricError": 10000000000.0, "refine": "REPLACE"}, {"boundingVolume": {"box": [-27.33197593688965, 53.60734558105469, -40.698081970214844, 17.007410049438477, 0.0, 0.0, 0.0, 19.520540237426758, 0.0, 0.0, 0.0, 6.864229202270508]}, "content": {"uri": "Block_L19_24.json"}, "geometricError": 10000000000.0, "refine": "REPLACE"}], "content": {"uri": "Block_L18_12.b3dm"}, "geometricError": 0.3314044177532196, "refine": "REPLACE"}, {"boundingVolume": {"box": [4.914890289306641, 20.803653717041016, -35.10567855834961, 15.239456176757812, 0.0, 0.0, 0.0, 14.603336334228516, 0.0, 0.0, 0.0, 7.067511558532715]}, "children": [{"boundingVolume": {"box": [-1.4348831176757812, 20.803653717041016, -35.34796142578125, 8.88968276977539, 0.0, 0.0, 0.0, 14.603336334228516, 0.0, 0.0, 0.0, 6.824167251586914]}, "content": {"uri": "Block_L19_23.json"}, "geometricError": 10000000000.0, "refine": "REPLACE"}, {"boundingVolume": {"box": [13.804573059082031, 20.803653717041016, -34.602046966552734, 6.349773406982422, 0.0, 0.0, 0.0, 14.603336334228516, 0.0, 0.0, 0.0, 6.563878059387207]}, "content": {"uri": "Block_L19_22.json"}, "geometricError": 10000000000.0, "refine": "REPLACE"}], "content": {"uri": "Block_L18_11.b3dm"}, "geometricError": 0.2989073395729065, "refine": "REPLACE"}, {"boundingVolume": {"box": [4.914890289306641, 55.851661682128906, -43.409706115722656, 15.239456176757812, 0.0, 0.0, 0.0, 20.444671630859375, 0.0, 0.0, 0.0, 6.16215705871582]}, "children": [{"boundingVolume": {"box": [4.914890289306641, 43.925601959228516, -43.37860870361328, 15.239456176757812, 0.0, 0.0, 0.0, 8.518611907958984, 0.0, 0.0, 0.0, 6.13105583190918]}, "content": {"uri": "Block_L18_6.json"}, "geometricError": 10000000000.0, "refine": "REPLACE"}, {"boundingVolume": {"box": [4.914890289306641, 64.37026977539062, -45.086334228515625, 15.239456176757812, 0.0, 0.0, 0.0, 11.92605972290039, 0.0, 0.0, 0.0, 4.485532760620117]}, "content": {"uri": "Block_L18_5.json"}, "geometricError": 10000000000.0, "refine": "REPLACE"}], "content": {"uri": "Block_L17_3.b3dm"}, "geometricError": 0.3656761348247528, "refine": "REPLACE"}], "content": {"uri": "Block_L17_6.b3dm"}, "geometricError": 0.6368345618247986, "refine": "REPLACE"}], "content": {"uri": "Block_L16_4.b3dm"}, "geometricError": 1.2501829862594604, "refine": "REPLACE"}], "content": {"uri": "Block_L15_3.b3dm"}, "geometricError": 2.4689784049987793, "refine": "REPLACE"}, {"boundingVolume": {"box": [56.729042053222656, -23.562335968017578, -42.04466247558594, 36.57469177246094, 0.0, 0.0, 0.0, 32.68982696533203, 0.0, 0.0, 0.0, 13.347326278686523]}, "children": [{"boundingVolume": {"box": [56.729042053222656, -23.562335968017578, -42.04466247558594, 36.57469177246094, 0.0, 0.0, 0.0, 32.68982696533203, 0.0, 0.0, 0.0, 13.347326278686523]}, "children": [{"boundingVolume": {"box": [35.393798828125, -23.562335968017578, -38.03055953979492, 15.23945426940918, 0.0, 0.0, 0.0, 32.68982696533203, 0.0, 0.0, 0.0, 9.33322525024414]}, "children": [{"boundingVolume": {"box": [35.393798828125, -37.180747985839844, -38.03055953979492, 15.23945426940918, 0.0, 0.0, 0.0, 19.071413040161133, 0.0, 0.0, 0.0, 9.33322525024414]}, "content": {"uri": "Block_L18_10.json"}, "geometricError": 10000000000.0, "refine": "REPLACE"}, {"boundingVolume": {"box": [35.393798828125, -4.490922927856445, -38.46162414550781, 15.23945426940918, 0.0, 0.0, 0.0, 13.618413925170898, 0.0, 0.0, 0.0, 7.409605026245117]}, "children": [{"boundingVolume": {"box": [27.774072647094727, -4.490922927856445, -35.72770690917969, 7.619726181030273, 0.0, 0.0, 0.0, 13.618413925170898, 0.0, 0.0, 0.0, 4.638055801391602]}, "content": {"uri": "Block_L19_19.json"}, "geometricError": 10000000000.0, "refine": "REPLACE"}, {"boundingVolume": {"box": [43.013526916503906, -4.490922927856445, -38.68471145629883, 7.619728088378906, 0.0, 0.0, 0.0, 13.618413925170898, 0.0, 0.0, 0.0, 7.186518669128418]}, "content": {"uri": "Block_L19_18.json"}, "geometricError": 10000000000.0, "refine": "REPLACE"}], "content": {"uri": "Block_L18_9.b3dm"}, "geometricError": 0.3174688220024109, "refine": "REPLACE"}], "content": {"uri": "Block_L17_5.b3dm"}, "geometricError": 0.6299423575401306, "refine": "REPLACE"}, {"boundingVolume": {"box": [71.96849060058594, -19.11035919189453, -43.72035598754883, 21.33523941040039, 0.0, 0.0, 0.0, 28.237850189208984, 0.0, 0.0, 0.0, 11.671630859375]}, "children": [{"boundingVolume": {"box": [71.96849060058594, -19.11035919189453, -43.72035598754883, 21.33523941040039, 0.0, 0.0, 0.0, 28.237850189208984, 0.0, 0.0, 0.0, 11.671630859375]}, "children": [{"boundingVolume": {"box": [70.5115966796875, -30.865262985229492, -44.204078674316406, 19.878341674804688, 0.0, 0.0, 0.0, 16.482946395874023, 0.0, 0.0, 0.0, 11.187906265258789]}, "content": {"uri": "Block_L18_3.json"}, "geometricError": 10000000000.0, "refine": "REPLACE"}, {"boundingVolume": {"box": [71.96849060058594, -2.627412796020508, -40.11936950683594, 21.33523941040039, 0.0, 0.0, 0.0, 11.754903793334961, 0.0, 0.0, 0.0, 8.070646286010742]}, "content": {"uri": "Block_L19_17.json"}, "geometricError": 10000000000.0, "refine": "REPLACE"}], "content": {"uri": "Block_L17_2.b3dm"}, "geometricError": 0.3655857443809509, "refine": "REPLACE"}], "content": {"uri": "Block_L16_1.b3dm"}, "geometricError": 0.7249791026115417, "refine": "REPLACE"}], "content": {"uri": "Block_L16_3.b3dm"}, "geometricError": 1.3147464990615845, "refine": "REPLACE"}], "content": {"uri": "Block_L15_2.b3dm"}, "geometricError": 2.6350152492523193, "refine": "REPLACE"}, {"boundingVolume": {"box": [54.68439483642578, 41.891822814941406, -43.406517028808594, 34.53004455566406, 0.0, 0.0, 0.0, 32.76433563232422, 0.0, 0.0, 0.0, 11.935193061828613]}, "children": [{"boundingVolume": {"box": [54.68439483642578, 41.816566467285156, -43.406517028808594, 34.53004455566406, 0.0, 0.0, 0.0, 32.68907165527344, 0.0, 0.0, 0.0, 11.935193061828613]}, "children": [{"boundingVolume": {"box": [54.68439483642578, 41.816566467285156, -43.406517028808594, 34.53004455566406, 0.0, 0.0, 0.0, 32.68907165527344, 0.0, 0.0, 0.0, 11.935193061828613]}, "content": {"uri": "Block_L17_4.json"}, "geometricError": 10000000000.0, "refine": "REPLACE"}], "content": {"uri": "Block_L16_2.b3dm"}, "geometricError": 1.3562798500061035, "refine": "REPLACE"}], "content": {"uri": "Block_L15_1.b3dm"}, "geometricError": 2.6880545616149902, "refine": "REPLACE"}], "geometricError": 10000000000.0, "refine": "REPLACE"}}