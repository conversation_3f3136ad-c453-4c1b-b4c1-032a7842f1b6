{"odmVersion": "3.5.5", "memory": {"total": 13972, "available": 10496}, "cpus": 32, "images": 104, "options": {"3d_tiles": false, "align": null, "auto_boundary": true, "auto_boundary_distance": 0, "bg_removal": false, "boundary": {}, "build_overviews": false, "camera_lens": "auto", "cameras": {}, "cog": true, "copy_to": null, "crop": 3, "dem_decimation": 1, "dem_euclidean_map": false, "dem_gapfill_steps": 3, "dem_resolution": 5, "dsm": false, "dtm": false, "end_with": "odm_postprocess", "fast_orthophoto": false, "feature_quality": "high", "feature_type": "dspsift", "force_gps": false, "gcp": null, "geo": null, "gltf": true, "gps_accuracy": 3, "ignore_gsd": false, "matcher_neighbors": 0, "matcher_order": 0, "matcher_type": "flann", "max_concurrency": 32, "merge": "all", "mesh_octree_depth": 12, "mesh_size": 300000, "min_num_features": 10000, "name": "782885c2-b560-4bde-9689-8f08139ca019", "no_gpu": false, "optimize_disk_space": false, "orthophoto_compression": "DEFLATE", "orthophoto_cutline": false, "orthophoto_kmz": false, "orthophoto_no_tiled": false, "orthophoto_png": false, "orthophoto_resolution": 5, "pc_classify": false, "pc_copc": false, "pc_csv": false, "pc_ept": true, "pc_filter": 5, "pc_las": false, "pc_quality": "high", "pc_rectify": false, "pc_sample": 0, "pc_skip_geometric": false, "primary_band": "auto", "project_path": "/var/www/data/782885c2-b560-4bde-9689-8f08139ca019", "radiometric_calibration": "none", "rerun": null, "rerun_all": false, "rerun_from": ["odm_postprocess"], "rolling_shutter": false, "rolling_shutter_readout": 0, "sfm_algorithm": "incremental", "sfm_no_partial": false, "skip_3dmodel": false, "skip_band_alignment": false, "skip_orthophoto": false, "skip_report": false, "sky_removal": false, "sm_cluster": null, "sm_no_align": false, "smrf_scalar": 1.25, "smrf_slope": 0.15, "smrf_threshold": 0.5, "smrf_window": 18.0, "split": 999999, "split_image_groups": null, "split_overlap": 150, "texturing_keep_unseen_faces": false, "texturing_single_material": false, "texturing_skip_global_seam_leveling": false, "tiles": false, "use_3dmesh": true, "use_exif": false, "use_fixed_camera_params": false, "use_hybrid_bundle_adjustment": false, "video_limit": 500, "video_resolution": 4000}, "startTime": "2025-05-30T02:07:15.517430", "stages": [{"name": "dataset", "startTime": "2025-05-30T02:07:16.498393", "messages": [{"message": "Running dataset stage", "type": "info"}, {"message": "Loading dataset from: /var/www/data/782885c2-b560-4bde-9689-8f08139ca019/images", "type": "info"}, {"message": "Loading images database: /var/www/data/782885c2-b560-4bde-9689-8f08139ca019/images.json", "type": "info"}, {"message": "Found 104 usable images", "type": "info"}, {"message": "Coordinates file already exist: /var/www/data/782885c2-b560-4bde-9689-8f08139ca019/odm_georeferencing/coords.txt", "type": "info"}, {"message": "Model geo file already exist: /var/www/data/782885c2-b560-4bde-9689-8f08139ca019/odm_georeferencing/odm_georeferencing_model_geo.txt", "type": "info"}, {"message": "Finished dataset stage", "type": "info"}]}, {"name": "split", "startTime": "2025-05-30T02:07:16.500997", "messages": [{"message": "Running split stage", "type": "info"}, {"message": "Normal dataset, will process all at once.", "type": "info"}, {"message": "Finished split stage", "type": "info"}]}, {"name": "merge", "startTime": "2025-05-30T02:07:16.501086", "messages": [{"message": "Running merge stage", "type": "info"}, {"message": "Normal dataset, nothing to merge.", "type": "info"}, {"message": "Finished merge stage", "type": "info"}]}, {"name": "opensfm", "startTime": "2025-05-30T02:07:16.501114", "messages": [{"message": "Running opensfm stage", "type": "info"}, {"message": "/var/www/data/782885c2-b560-4bde-9689-8f08139ca019/opensfm/image_list.txt already exists, not rerunning OpenSfM setup", "type": "warning"}, {"message": "/var/www/data/782885c2-b560-4bde-9689-8f08139ca019/opensfm/exif already exists, not rerunning photo to metadata", "type": "warning"}, {"message": "Detect features already done: /var/www/data/782885c2-b560-4bde-9689-8f08139ca019/opensfm/features exists", "type": "warning"}, {"message": "Match features already done: /var/www/data/782885c2-b560-4bde-9689-8f08139ca019/opensfm/matches exists", "type": "warning"}, {"message": "Found a valid OpenSfM tracks file in: /var/www/data/782885c2-b560-4bde-9689-8f08139ca019/opensfm/tracks.csv", "type": "warning"}, {"message": "Found a valid OpenSfM reconstruction file in: /var/www/data/782885c2-b560-4bde-9689-8f08139ca019/opensfm/reconstruction.json", "type": "warning"}, {"message": "Already extracted cameras", "type": "info"}, {"message": "Export reconstruction stats", "type": "info"}, {"message": "Found existing reconstruction stats /var/www/data/782885c2-b560-4bde-9689-8f08139ca019/opensfm/stats/stats.json", "type": "warning"}, {"message": "Will skip exporting /var/www/data/782885c2-b560-4bde-9689-8f08139ca019/opensfm/reconstruction.geocoords.json", "type": "warning"}, {"message": "Undistorting /var/www/data/782885c2-b560-4bde-9689-8f08139ca019/opensfm ...", "type": "info"}, {"message": "Already undistorted (nominal)", "type": "warning"}, {"message": "Found a valid OpenSfM NVM reconstruction file in: /var/www/data/782885c2-b560-4bde-9689-8f08139ca019/opensfm/undistorted/reconstruction.nvm", "type": "warning"}, {"message": "Finished opensfm stage", "type": "info"}]}, {"name": "openmvs", "startTime": "2025-05-30T02:07:16.989217", "messages": [{"message": "Running openmvs stage", "type": "info"}, {"message": "Found a valid OpenMVS reconstruction file in: /var/www/data/782885c2-b560-4bde-9689-8f08139ca019/opensfm/undistorted/openmvs/scene_dense_dense_filtered.ply", "type": "warning"}, {"message": "Finished openmvs stage", "type": "info"}]}, {"name": "odm_filterpoints", "startTime": "2025-05-30T02:07:16.989284", "messages": [{"message": "Running odm_filterpoints stage", "type": "info"}, {"message": "Found a valid point cloud file in: /var/www/data/782885c2-b560-4bde-9689-8f08139ca019/odm_filterpoints/point_cloud.ply", "type": "warning"}, {"message": "Finished odm_filterpoints stage", "type": "info"}]}, {"name": "odm_meshing", "startTime": "2025-05-30T02:07:16.989322", "messages": [{"message": "Running odm_meshing stage", "type": "info"}, {"message": "Found a valid ODM Mesh file in: /var/www/data/782885c2-b560-4bde-9689-8f08139ca019/odm_meshing/odm_mesh.ply", "type": "warning"}, {"message": "Finished odm_meshing stage", "type": "info"}]}, {"name": "mvs_texturing", "startTime": "2025-05-30T02:07:16.989406", "messages": [{"message": "Running mvs_texturing stage", "type": "info"}, {"message": "Writing MVS Textured file in: /var/www/data/782885c2-b560-4bde-9689-8f08139ca019/odm_texturing/odm_textured_model_geo.obj", "type": "info"}, {"message": "Removing old tmp directory /var/www/data/782885c2-b560-4bde-9689-8f08139ca019/odm_texturing/tmp", "type": "info"}, {"message": "running \"/code/SuperBuild/install/bin/texrecon\" \"/var/www/data/782885c2-b560-4bde-9689-8f08139ca019/opensfm/undistorted/reconstruction.nvm\" \"/var/www/data/782885c2-b560-4bde-9689-8f08139ca019/odm_meshing/odm_mesh.ply\" \"/var/www/data/782885c2-b560-4bde-9689-8f08139ca019/odm_texturing/odm_textured_model_geo\" -d gmi -o gauss_clamping -t none --no_intermediate_results     --max_texture_size=8192 ", "type": "info"}, {"message": "Generating glTF Binary", "type": "info"}, {"message": "Converting /var/www/data/782885c2-b560-4bde-9689-8f08139ca019/odm_texturing/odm_textured_model_geo.obj --> /var/www/data/782885c2-b560-4bde-9689-8f08139ca019/odm_texturing/odm_textured_model_geo.glb", "type": "info"}, {"message": "Loading /var/www/data/782885c2-b560-4bde-9689-8f08139ca019/odm_texturing/odm_textured_model_geo.obj", "type": "info"}, {"message": "Loading odm_textured_model_geo_material0000_map_Kd.png", "type": "info"}, {"message": "Loading odm_textured_model_geo_material0001_map_Kd.png", "type": "info"}, {"message": "Loading odm_textured_model_geo_material0002_map_Kd.png", "type": "info"}, {"message": "Loading odm_textured_model_geo_material0003_map_Kd.png", "type": "info"}, {"message": "Loading odm_textured_model_geo_material0004_map_Kd.png", "type": "info"}, {"message": "Loading odm_textured_model_geo_material0005_map_Kd.png", "type": "info"}, {"message": "Loading odm_textured_model_geo_material0006_map_Kd.png", "type": "info"}, {"message": "Loading odm_textured_model_geo_material0007_map_Kd.png", "type": "info"}, {"message": "Loading odm_textured_model_geo_material0008_map_Kd.png", "type": "info"}, {"message": "Loading odm_textured_model_geo_material0009_map_Kd.png", "type": "info"}, {"message": "Loading odm_textured_model_geo_material0010_map_Kd.png", "type": "info"}, {"message": "Loading odm_textured_model_geo_material0011_map_Kd.png", "type": "info"}, {"message": "Loading odm_textured_model_geo_material0012_map_Kd.png", "type": "info"}, {"message": "Loading odm_textured_model_geo_material0013_map_Kd.png", "type": "info"}, {"message": "Loading odm_textured_model_geo_material0014_map_Kd.png", "type": "info"}, {"message": "Loading odm_textured_model_geo_material0015_map_Kd.png", "type": "info"}, {"message": "Loading odm_textured_model_geo_material0016_map_Kd.png", "type": "info"}, {"message": "Loading odm_textured_model_geo_material0017_map_Kd.png", "type": "info"}, {"message": "Loading odm_textured_model_geo_material0018_map_Kd.png", "type": "info"}, {"message": "Loading odm_textured_model_geo_material0019_map_Kd.png", "type": "info"}, {"message": "Loading odm_textured_model_geo_material0020_map_Kd.png", "type": "info"}, {"message": "Loading odm_textured_model_geo_material0021_map_Kd.png", "type": "info"}, {"message": "Loading odm_textured_model_geo_material0022_map_Kd.png", "type": "info"}, {"message": "Loading odm_textured_model_geo_material0023_map_Kd.png", "type": "info"}, {"message": "Loading odm_textured_model_geo_material0024_map_Kd.png", "type": "info"}, {"message": "Loading odm_textured_model_geo_material0025_map_Kd.png", "type": "info"}, {"message": "Loading odm_textured_model_geo_material0026_map_Kd.png", "type": "info"}, {"message": "Loading odm_textured_model_geo_material0027_map_Kd.png", "type": "info"}, {"message": "Loading odm_textured_model_geo_material0028_map_Kd.png", "type": "info"}, {"message": "Loading odm_textured_model_geo_material0029_map_Kd.png", "type": "info"}, {"message": "Loading odm_textured_model_geo_material0030_map_Kd.png", "type": "info"}, {"message": "Loading odm_textured_model_geo_material0031_map_Kd.png", "type": "info"}, {"message": "Loading odm_textured_model_geo_material0032_map_Kd.png", "type": "info"}, {"message": "Loading odm_textured_model_geo_material0033_map_Kd.png", "type": "info"}, {"message": "Loading odm_textured_model_geo_material0034_map_Kd.png", "type": "info"}, {"message": "Loading odm_textured_model_geo_material0035_map_Kd.png", "type": "info"}, {"message": "Loading odm_textured_model_geo_material0036_map_Kd.png", "type": "info"}, {"message": "Loading odm_textured_model_geo_material0037_map_Kd.png", "type": "info"}, {"message": "Loading odm_textured_model_geo_material0038_map_Kd.png", "type": "info"}, {"message": "Loading odm_textured_model_geo_material0039_map_Kd.png", "type": "info"}, {"message": "Loading odm_textured_model_geo_material0040_map_Kd.png", "type": "info"}, {"message": "Loading odm_textured_model_geo_material0041_map_Kd.png", "type": "info"}, {"message": "Loading odm_textured_model_geo_material0042_map_Kd.png", "type": "info"}, {"message": "Loading odm_textured_model_geo_material0043_map_Kd.png", "type": "info"}, {"message": "Loading odm_textured_model_geo_material0044_map_Kd.png", "type": "info"}, {"message": "Loading odm_textured_model_geo_material0045_map_Kd.png", "type": "info"}, {"message": "Loading odm_textured_model_geo_material0046_map_Kd.png", "type": "info"}, {"message": "Loading odm_textured_model_geo_material0047_map_Kd.png", "type": "info"}, {"message": "Loading odm_textured_model_geo_material0048_map_Kd.png", "type": "info"}, {"message": "Loading odm_textured_model_geo_material0049_map_Kd.png", "type": "info"}, {"message": "Loading odm_textured_model_geo_material0050_map_Kd.png", "type": "info"}, {"message": "Loading odm_textured_model_geo_material0051_map_Kd.png", "type": "info"}, {"message": "Loading odm_textured_model_geo_material0052_map_Kd.png", "type": "info"}, {"message": "Loading odm_textured_model_geo_material0053_map_Kd.png", "type": "info"}, {"message": "Loading odm_textured_model_geo_material0054_map_Kd.png", "type": "info"}, {"message": "Loading odm_textured_model_geo_material0055_map_Kd.png", "type": "info"}, {"message": "Loading odm_textured_model_geo_material0056_map_Kd.png", "type": "info"}, {"message": "Loading odm_textured_model_geo_material0057_map_Kd.png", "type": "info"}, {"message": "Loading odm_textured_model_geo_material0058_map_Kd.png", "type": "info"}, {"message": "Loading odm_textured_model_geo_material0059_map_Kd.png", "type": "info"}, {"message": "Loading odm_textured_model_geo_material0060_map_Kd.png", "type": "info"}, {"message": "Loading odm_textured_model_geo_material0061_map_Kd.png", "type": "info"}, {"message": "Loading odm_textured_model_geo_material0062_map_Kd.png", "type": "info"}, {"message": "Loading odm_textured_model_geo_material0063_map_Kd.png", "type": "info"}, {"message": "Loading odm_textured_model_geo_material0064_map_Kd.png", "type": "info"}, {"message": "Loading odm_textured_model_geo_material0065_map_Kd.png", "type": "info"}, {"message": "Loading odm_textured_model_geo_material0066_map_Kd.png", "type": "info"}, {"message": "Loading odm_textured_model_geo_material0067_map_Kd.png", "type": "info"}, {"message": "Loading odm_textured_model_geo_material0068_map_Kd.png", "type": "info"}, {"message": "Loading odm_textured_model_geo_material0069_map_Kd.png", "type": "info"}, {"message": "Loading odm_textured_model_geo_material0070_map_Kd.png", "type": "info"}, {"message": "Loading odm_textured_model_geo_material0071_map_Kd.png", "type": "info"}, {"message": "Loading odm_textured_model_geo_material0072_map_Kd.png", "type": "info"}, {"message": "Loading odm_textured_model_geo_material0073_map_Kd.png", "type": "info"}, {"message": "Loading odm_textured_model_geo_material0074_map_Kd.png", "type": "info"}, {"message": "Writing...", "type": "info"}, {"message": "Wrote /var/www/data/782885c2-b560-4bde-9689-8f08139ca019/odm_texturing/odm_textured_model_geo.glb", "type": "info"}, {"message": "Compressing with draco", "type": "info"}, {"message": "running draco_transcoder -i \"/var/www/data/782885c2-b560-4bde-9689-8f08139ca019/odm_texturing/odm_textured_model_geo.glb\" -o \"/var/www/data/782885c2-b560-4bde-9689-8f08139ca019/odm_texturing/odm_textured_model_geo_compressed.glb\" -qt 16 -qp 16", "type": "info"}, {"message": "Finished mvs_texturing stage", "type": "info"}]}, {"name": "odm_georeferencing", "startTime": "2025-05-30T02:11:09.317605", "messages": [{"message": "Running odm_georeferencing stage", "type": "info"}, {"message": "Georeferencing point cloud", "type": "info"}, {"message": "las scale calculated as the minimum of 1/10 estimated spacing or 0.001, which ever is less.", "type": "info"}, {"message": "running pdal translate -i \"/var/www/data/782885c2-b560-4bde-9689-8f08139ca019/odm_filterpoints/point_cloud.ply\" -o \"/var/www/data/782885c2-b560-4bde-9689-8f08139ca019/odm_georeferencing/odm_georeferenced_model.laz\" ferry transformation --filters.ferry.dimensions=\"views => UserData\" --filters.transformation.matrix=\"1 0 0 269825.0 0 1 0 3097756.0 0 0 1 0 0 0 0 1\" --writers.las.offset_x=269825.0 --writers.las.offset_y=3097756.0 --writers.las.scale_x=0.001 --writers.las.scale_y=0.001 --writers.las.scale_z=0.001 --writers.las.offset_z=0 --writers.las.a_srs=\"+proj=utm +zone=51 +datum=WGS84 +units=m +no_defs +type=crs\"", "type": "info"}, {"message": "Calculating cropping area and generating bounds shapefile from point cloud", "type": "info"}, {"message": "running pdal translate -i \"/var/www/data/782885c2-b560-4bde-9689-8f08139ca019/odm_georeferencing/odm_georeferenced_model.laz\" -o \"/var/www/data/782885c2-b560-4bde-9689-8f08139ca019/odm_georeferencing/odm_georeferenced_model.decimated.las\" decimation --filters.decimation.step=40 ", "type": "info"}, {"message": "running pdal info --boundary --filters.hexbin.edge_size=1 --filters.hexbin.threshold=0 \"/var/www/data/782885c2-b560-4bde-9689-8f08139ca019/odm_georeferencing/odm_georeferenced_model.decimated.las\" > \"/var/www/data/782885c2-b560-4bde-9689-8f08139ca019/odm_georeferencing/odm_georeferenced_model.boundary.json\"", "type": "info"}, {"message": "running pdal info --summary \"/var/www/data/782885c2-b560-4bde-9689-8f08139ca019/odm_georeferencing/odm_georeferenced_model.laz\" > \"/var/www/data/782885c2-b560-4bde-9689-8f08139ca019/odm_georeferencing/odm_georeferenced_model.summary.json\"", "type": "info"}, {"message": "running ogr2ogr -overwrite -f GPKG -a_srs \"+proj=utm +zone=51 +datum=WGS84 +units=m +no_defs\" /var/www/data/782885c2-b560-4bde-9689-8f08139ca019/odm_georeferencing/odm_georeferenced_model.bounds.gpkg /var/www/data/782885c2-b560-4bde-9689-8f08139ca019/odm_georeferencing/odm_georeferenced_model.bounds.geojson", "type": "info"}, {"message": "Creating Entwine Point Tile output", "type": "info"}, {"message": "running entwine build --threads 32 --tmp \"/var/www/data/782885c2-b560-4bde-9689-8f08139ca019/entwine_pointcloud-tmp\" -i /var/www/data/782885c2-b560-4bde-9689-8f08139ca019/odm_georeferencing/odm_georeferenced_model.laz -o \"/var/www/data/782885c2-b560-4bde-9689-8f08139ca019/entwine_pointcloud\" ", "type": "info"}, {"message": "Finished odm_georeferencing stage", "type": "info"}]}, {"name": "odm_dem", "startTime": "2025-05-30T02:11:45.006385", "messages": [{"message": "Running odm_dem stage", "type": "info"}, {"message": "Maximum resolution set to 1.0 * (GSD - 10.0%) (5.04 cm / pixel, requested resolution was 5.00 cm / pixel)", "type": "warning"}, {"message": "Create DSM: <PERSON><PERSON><PERSON>", "type": "info"}, {"message": "Create DTM: <PERSON><PERSON><PERSON>", "type": "info"}, {"message": "DEM input file /var/www/data/782885c2-b560-4bde-9689-8f08139ca019/odm_georeferencing/odm_georeferenced_model.laz found: True", "type": "info"}, {"message": "DEM will not be generated", "type": "warning"}, {"message": "Finished odm_dem stage", "type": "info"}]}, {"name": "odm_orthophoto", "startTime": "2025-05-30T02:11:45.007791", "messages": [{"message": "Running odm_orthophoto stage", "type": "info"}, {"message": "Maximum resolution set to 1.0 * (GSD - 10.0%) (5.04 cm / pixel, requested resolution was 5.00 cm / pixel)", "type": "warning"}, {"message": "Creating GeoTIFF", "type": "info"}, {"message": "running \"/code/SuperBuild/install/bin/odm_orthophoto\" -inputFiles /var/www/data/782885c2-b560-4bde-9689-8f08139ca019/odm_texturing/odm_textured_model_geo.obj -logFile \"/var/www/data/782885c2-b560-4bde-9689-8f08139ca019/odm_orthophoto/odm_orthophoto_log.txt\" -outputFile \"/var/www/data/782885c2-b560-4bde-9689-8f08139ca019/odm_orthophoto/odm_orthophoto.tif\" -resolution 19.83629470202584 -verbose -outputCornerFile \"/var/www/data/782885c2-b560-4bde-9689-8f08139ca019/odm_orthophoto/odm_orthophoto_corners.txt\"   -inpaintThreshold 1.0 -utm_north_offset 3097756.0 -utm_east_offset 269825.0 -a_srs \"+proj=utm +zone=51 +datum=WGS84 +units=m +no_defs +type=crs\" -co TILED=YES -co COMPRESS=DEFLATE -co PREDICTOR=2 -co BIGTIFF=IF_SAFER -co BLOCKXSIZE=512 -co BLOCKYSIZE=512 -co NUM_THREADS=32 --config GDAL_CACHEMAX 5582589952.0 ", "type": "info"}, {"message": "Cropping /var/www/data/782885c2-b560-4bde-9689-8f08139ca019/odm_orthophoto/odm_orthophoto.tif", "type": "info"}, {"message": "running gdalwarp -cutline /var/www/data/782885c2-b560-4bde-9689-8f08139ca019/odm_georeferencing/odm_georeferenced_model.bounds.gpkg -crop_to_cutline -co TILED=YES -co COMPRESS=DEFLATE -co PREDICTOR=2 -co BIGTIFF=IF_SAFER -co BLOCKXSIZE=512 -co BLOCKYSIZE=512 -co NUM_THREADS=32 -dstalpha /var/www/data/782885c2-b560-4bde-9689-8f08139ca019/odm_orthophoto/odm_orthophoto.original.tif /var/www/data/782885c2-b560-4bde-9689-8f08139ca019/odm_orthophoto/odm_orthophoto.tif --config GDAL_CACHEMAX 37.95%", "type": "info"}, {"message": "Optimizing /var/www/data/782885c2-b560-4bde-9689-8f08139ca019/odm_orthophoto/odm_orthophoto.tif as Cloud Optimized GeoTIFF", "type": "info"}, {"message": "running gdal_translate -of COG -co NUM_THREADS=32 -co BLOCKSIZE=256 -co COMPRESS=DEFLATE -co PREDICTOR=2 -co BIGTIFF=IF_SAFER -co RESAMPLING=NEAREST --config GDAL_CACHEMAX 37.8% --config GDAL_NUM_THREADS 32 \"/var/www/data/782885c2-b560-4bde-9689-8f08139ca019/odm_orthophoto/odm_orthophoto.tif\" \"/var/www/data/782885c2-b560-4bde-9689-8f08139ca019/odm_orthophoto/odm_orthophoto_cogeo.tif\" ", "type": "info"}, {"message": "Wrote /var/www/data/782885c2-b560-4bde-9689-8f08139ca019/odm_orthophoto/odm_orthophoto_extent.dxf", "type": "info"}, {"message": "Wrote /var/www/data/782885c2-b560-4bde-9689-8f08139ca019/odm_orthophoto/odm_orthophoto.tfw", "type": "info"}, {"message": "Finished odm_orthophoto stage", "type": "info"}]}, {"name": "odm_report", "startTime": "2025-05-30T02:12:44.969965", "messages": [{"message": "Running odm_report stage", "type": "info"}, {"message": "Exporting shots.g<PERSON><PERSON><PERSON>", "type": "info"}, {"message": "Wrote /var/www/data/782885c2-b560-4bde-9689-8f08139ca019/odm_report/shots.geojson", "type": "info"}, {"message": "Copied /var/www/data/782885c2-b560-4bde-9689-8f08139ca019/opensfm/camera_mappings.npz --> /var/www/data/782885c2-b560-4bde-9689-8f08139ca019/odm_report/camera_mappings.npz", "type": "info"}, {"message": "running pdal info --dimensions \"X,Y,Z\" \"/var/www/data/782885c2-b560-4bde-9689-8f08139ca019/odm_georeferencing/odm_georeferenced_model.laz\" > \"/var/www/data/782885c2-b560-4bde-9689-8f08139ca019/odm_georeferencing/odm_georeferenced_model.info.json\"", "type": "info"}, {"message": "running pdal translate -i \"/var/www/data/782885c2-b560-4bde-9689-8f08139ca019/odm_georeferencing/odm_georeferenced_model.laz\" -o \"/var/www/data/782885c2-b560-4bde-9689-8f08139ca019/opensfm/stats/overlap.tif\" --writer gdal --writers.gdal.resolution=0.7890492857142817 --writers.gdal.data_type=uint8_t --writers.gdal.dimension=UserData --writers.gdal.output_type=max --writers.gdal.radius=1.1158842012379404 ", "type": "info"}, {"message": "Cropping /var/www/data/782885c2-b560-4bde-9689-8f08139ca019/opensfm/stats/overlap.tif", "type": "info"}, {"message": "running gdalwarp -cutline /var/www/data/782885c2-b560-4bde-9689-8f08139ca019/odm_georeferencing/odm_georeferenced_model.bounds.gpkg -crop_to_cutline -co TILED=YES -co COMPRESS=DEFLATE -co PREDICTOR=2 -co BIGTIFF=IF_SAFER -co BLOCKXSIZE=512 -co BLOCKYSIZE=512 -co NUM_THREADS=32  /var/www/data/782885c2-b560-4bde-9689-8f08139ca019/opensfm/stats/overlap.original.tif /var/www/data/782885c2-b560-4bde-9689-8f08139ca019/opensfm/stats/overlap.tif --config GDAL_CACHEMAX 37.55%", "type": "info"}, {"message": "running gdaldem color-relief \"/var/www/data/782885c2-b560-4bde-9689-8f08139ca019/opensfm/stats/overlap.tif\" \"/code/opendm/report/overlap_color_map.txt\" \"/var/www/data/782885c2-b560-4bde-9689-8f08139ca019/opensfm/stats/overlap.png\" -of PNG -alpha", "type": "info"}, {"message": "running gdal_translate -of png \"/var/www/data/782885c2-b560-4bde-9689-8f08139ca019/odm_orthophoto/odm_orthophoto.tif\" \"/var/www/data/782885c2-b560-4bde-9689-8f08139ca019/opensfm/stats/ortho.png\" -b 1 -b 2 -b 3 -b 4 -outsize 1400 0 -co WORLDFILE=YES --config GDAL_CACHEMAX 37.55% ", "type": "info"}, {"message": "Exporting report to /var/www/data/782885c2-b560-4bde-9689-8f08139ca019/odm_report/report.pdf", "type": "info"}, {"message": "Finished odm_report stage", "type": "info"}]}, {"name": "odm_postprocess", "startTime": "2025-05-30T02:13:05.195688", "messages": [{"message": "Running odm_postprocess stage", "type": "info"}, {"message": "Post Processing", "type": "info"}, {"message": "Adding TIFFTAGs to /var/www/data/782885c2-b560-4bde-9689-8f08139ca019/odm_orthophoto/odm_orthophoto.tif", "type": "info"}, {"message": "Finished odm_postprocess stage", "type": "info"}, {"message": "No more stages to run", "type": "info"}], "endTime": "2025-05-30T02:13:05.201677", "totalTime": 0.01}], "processes": [{"command": "\"/code/SuperBuild/install/bin/texrecon\" \"/var/www/data/782885c2-b560-4bde-9689-8f08139ca019/opensfm/undistorted/reconstruction.nvm\" \"/var/www/data/782885c2-b560-4bde-9689-8f08139ca019/odm_meshing/odm_mesh.ply\" \"/var/www/data/782885c2-b560-4bde-9689-8f08139ca019/odm_texturing/odm_textured_model_geo\" -d gmi -o gauss_clamping -t none --no_intermediate_results     --max_texture_size=8192 ", "exitCode": 0, "output": ["", "Blending texture patches 100%... done. (<PERSON><PERSON> 67.125s)", "Generating texture atlases:", "Sorting texture patches... done.", "", "Working on atlas 75 100%... done.", "Finalizing texture atlases... done. (Took: 0s)", "Building objmodel:", "Saving model... done.", "Whole texturing procedure took: 190.705s"]}, {"command": "draco_transcoder -i \"/var/www/data/782885c2-b560-4bde-9689-8f08139ca019/odm_texturing/odm_textured_model_geo.glb\" -o \"/var/www/data/782885c2-b560-4bde-9689-8f08139ca019/odm_texturing/odm_textured_model_geo_compressed.glb\" -qt 16 -qp 16", "exitCode": 0, "output": ["Transcode\t/var/www/data/782885c2-b560-4bde-9689-8f08139ca019/odm_texturing/odm_textured_model_geo.glb\t4096"]}, {"command": "pdal translate -i \"/var/www/data/782885c2-b560-4bde-9689-8f08139ca019/odm_filterpoints/point_cloud.ply\" -o \"/var/www/data/782885c2-b560-4bde-9689-8f08139ca019/odm_georeferencing/odm_georeferenced_model.laz\" ferry transformation --filters.ferry.dimensions=\"views => UserData\" --filters.transformation.matrix=\"1 0 0 269825.0 0 1 0 3097756.0 0 0 1 0 0 0 0 1\" --writers.las.offset_x=269825.0 --writers.las.offset_y=3097756.0 --writers.las.scale_x=0.001 --writers.las.scale_y=0.001 --writers.las.scale_z=0.001 --writers.las.offset_z=0 --writers.las.a_srs=\"+proj=utm +zone=51 +datum=WGS84 +units=m +no_defs +type=crs\"", "exitCode": 0}, {"command": "pdal translate -i \"/var/www/data/782885c2-b560-4bde-9689-8f08139ca019/odm_georeferencing/odm_georeferenced_model.laz\" -o \"/var/www/data/782885c2-b560-4bde-9689-8f08139ca019/odm_georeferencing/odm_georeferenced_model.decimated.las\" decimation --filters.decimation.step=40 ", "exitCode": 0}, {"command": "pdal info --boundary --filters.hexbin.edge_size=1 --filters.hexbin.threshold=0 \"/var/www/data/782885c2-b560-4bde-9689-8f08139ca019/odm_georeferencing/odm_georeferenced_model.decimated.las\" > \"/var/www/data/782885c2-b560-4bde-9689-8f08139ca019/odm_georeferencing/odm_georeferenced_model.boundary.json\"", "exitCode": 0}, {"command": "pdal info --summary \"/var/www/data/782885c2-b560-4bde-9689-8f08139ca019/odm_georeferencing/odm_georeferenced_model.laz\" > \"/var/www/data/782885c2-b560-4bde-9689-8f08139ca019/odm_georeferencing/odm_georeferenced_model.summary.json\"", "exitCode": 0}, {"command": "ogr2ogr -overwrite -f GPKG -a_srs \"+proj=utm +zone=51 +datum=WGS84 +units=m +no_defs\" /var/www/data/782885c2-b560-4bde-9689-8f08139ca019/odm_georeferencing/odm_georeferenced_model.bounds.gpkg /var/www/data/782885c2-b560-4bde-9689-8f08139ca019/odm_georeferencing/odm_georeferenced_model.bounds.geojson", "exitCode": 0}, {"command": "entwine build --threads 32 --tmp \"/var/www/data/782885c2-b560-4bde-9689-8f08139ca019/entwine_pointcloud-tmp\" -i /var/www/data/782885c2-b560-4bde-9689-8f08139ca019/odm_georeferencing/odm_georeferenced_model.laz -o \"/var/www/data/782885c2-b560-4bde-9689-8f08139ca019/entwine_pointcloud\" ", "exitCode": 0, "output": ["Bounds: [(269159, 3097277, -27), (270265, 3098368, 109)]", "Scale: 0.001", "SRS: EPSG:32651", "", "Adding 0 - /var/www/data/782885c2-b560-4bde-9689-8f08139ca019/odm_georeferencing/odm_georeferenced_model.laz", "Joining", "00:10 - 57% - 8,364,032 - 3,011 (3,011) M/h - 0W - 0R - 186A", "Done 0", "Saving", "<PERSON>rote 14,710,854 points."]}, {"command": "\"/code/SuperBuild/install/bin/odm_orthophoto\" -inputFiles /var/www/data/782885c2-b560-4bde-9689-8f08139ca019/odm_texturing/odm_textured_model_geo.obj -logFile \"/var/www/data/782885c2-b560-4bde-9689-8f08139ca019/odm_orthophoto/odm_orthophoto_log.txt\" -outputFile \"/var/www/data/782885c2-b560-4bde-9689-8f08139ca019/odm_orthophoto/odm_orthophoto.tif\" -resolution 19.83629470202584 -verbose -outputCornerFile \"/var/www/data/782885c2-b560-4bde-9689-8f08139ca019/odm_orthophoto/odm_orthophoto_corners.txt\"   -inpaintThreshold 1.0 -utm_north_offset 3097756.0 -utm_east_offset 269825.0 -a_srs \"+proj=utm +zone=51 +datum=WGS84 +units=m +no_defs +type=crs\" -co TILED=YES -co COMPRESS=DEFLATE -co PREDICTOR=2 -co BIGTIFF=IF_SAFER -co BLOCKXSIZE=512 -co BLOCKYSIZE=512 -co NUM_THREADS=32 --config GDAL_CACHEMAX 5582589952.0 ", "exitCode": 0, "output": ["Set GDAL_CACHEMAX to 5582589952", "Set TILED=YES", "Set COMPRESS=DEFLATE", "Set PREDICTOR=2", "Set BIGTIFF=IF_SAFER", "Set BLOCKXSIZE=512", "Set BLOCKYSIZE=512", "Set NUM_THREADS=32", "Writing corner coordinates to /var/www/data/782885c2-b560-4bde-9689-8f08139ca019/odm_orthophoto/odm_orthophoto_corners.txt", "Orthophoto generation done."]}, {"command": "gdalwarp -cutline /var/www/data/782885c2-b560-4bde-9689-8f08139ca019/odm_georeferencing/odm_georeferenced_model.bounds.gpkg -crop_to_cutline -co TILED=YES -co COMPRESS=DEFLATE -co PREDICTOR=2 -co BIGTIFF=IF_SAFER -co BLOCKXSIZE=512 -co BLOCKYSIZE=512 -co NUM_THREADS=32 -dstalpha /var/www/data/782885c2-b560-4bde-9689-8f08139ca019/odm_orthophoto/odm_orthophoto.original.tif /var/www/data/782885c2-b560-4bde-9689-8f08139ca019/odm_orthophoto/odm_orthophoto.tif --config GDAL_CACHEMAX 37.95%", "exitCode": 0, "output": ["Using band 4 of source image as alpha.", "Creating output file that is 20554P x 17967L.", "Processing /var/www/data/782885c2-b560-4bde-9689-8f08139ca019/odm_orthophoto/odm_orthophoto.original.tif [1/1] : 0...10...20...30...40...50...60...70...80...90...100 - done."]}, {"command": "gdal_translate -of COG -co NUM_THREADS=32 -co BLOCKSIZE=256 -co COMPRESS=DEFLATE -co PREDICTOR=2 -co BIGTIFF=IF_SAFER -co RESAMPLING=NEAREST --config GDAL_CACHEMAX 37.8% --config GDAL_NUM_THREADS 32 \"/var/www/data/782885c2-b560-4bde-9689-8f08139ca019/odm_orthophoto/odm_orthophoto.tif\" \"/var/www/data/782885c2-b560-4bde-9689-8f08139ca019/odm_orthophoto/odm_orthophoto_cogeo.tif\" ", "exitCode": 0, "output": ["Input file size is 20554, 17967", "0...10...20...30...40...50...60...70...80...90...100 - done."]}, {"command": "pdal info --dimensions \"X,Y,Z\" \"/var/www/data/782885c2-b560-4bde-9689-8f08139ca019/odm_georeferencing/odm_georeferenced_model.laz\" > \"/var/www/data/782885c2-b560-4bde-9689-8f08139ca019/odm_georeferencing/odm_georeferenced_model.info.json\"", "exitCode": 0}, {"command": "pdal translate -i \"/var/www/data/782885c2-b560-4bde-9689-8f08139ca019/odm_georeferencing/odm_georeferenced_model.laz\" -o \"/var/www/data/782885c2-b560-4bde-9689-8f08139ca019/opensfm/stats/overlap.tif\" --writer gdal --writers.gdal.resolution=0.7890492857142817 --writers.gdal.data_type=uint8_t --writers.gdal.dimension=UserData --writers.gdal.output_type=max --writers.gdal.radius=1.1158842012379404 ", "exitCode": 0}, {"command": "gdalwarp -cutline /var/www/data/782885c2-b560-4bde-9689-8f08139ca019/odm_georeferencing/odm_georeferenced_model.bounds.gpkg -crop_to_cutline -co TILED=YES -co COMPRESS=DEFLATE -co PREDICTOR=2 -co BIGTIFF=IF_SAFER -co BLOCKXSIZE=512 -co BLOCKYSIZE=512 -co NUM_THREADS=32  /var/www/data/782885c2-b560-4bde-9689-8f08139ca019/opensfm/stats/overlap.original.tif /var/www/data/782885c2-b560-4bde-9689-8f08139ca019/opensfm/stats/overlap.tif --config GDAL_CACHEMAX 37.55%", "exitCode": 0, "output": ["Creating output file that is 1312P x 1147L.", "Processing /var/www/data/782885c2-b560-4bde-9689-8f08139ca019/opensfm/stats/overlap.original.tif [1/1] : 0Using internal nodata values (e.g. 255) for image /var/www/data/782885c2-b560-4bde-9689-8f08139ca019/opensfm/stats/overlap.original.tif.", "Copying nodata values from source /var/www/data/782885c2-b560-4bde-9689-8f08139ca019/opensfm/stats/overlap.original.tif to destination /var/www/data/782885c2-b560-4bde-9689-8f08139ca019/opensfm/stats/overlap.tif.", "...10...20...30...40...50...60...70...80...90...100 - done."]}, {"command": "gdaldem color-relief \"/var/www/data/782885c2-b560-4bde-9689-8f08139ca019/opensfm/stats/overlap.tif\" \"/code/opendm/report/overlap_color_map.txt\" \"/var/www/data/782885c2-b560-4bde-9689-8f08139ca019/opensfm/stats/overlap.png\" -of PNG -alpha", "exitCode": 0, "output": ["0...10...20...30...40...50...60...70...80...90...100 - done."]}, {"command": "gdal_translate -of png \"/var/www/data/782885c2-b560-4bde-9689-8f08139ca019/odm_orthophoto/odm_orthophoto.tif\" \"/var/www/data/782885c2-b560-4bde-9689-8f08139ca019/opensfm/stats/ortho.png\" -b 1 -b 2 -b 3 -b 4 -outsize 1400 0 -co WORLDFILE=YES --config GDAL_CACHEMAX 37.55% ", "exitCode": 0, "output": ["Input file size is 20554, 17967", "0...10...20...30...40...50...60...70...80...90...100 - done."]}], "success": true, "endTime": "2025-05-30T02:13:05.201677", "totalTime": 349.68}