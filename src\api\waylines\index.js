import Qs from 'qs';
import request from '@/utils/http/axios';
import {ELocalStorageKey} from '@/api/enum/index';

const HTTP_PREFIX = '/hztech-flight-core/wayline/api/v1';

// 查询航线
export const getWaylineFiles = (wid, params) => {
  return request({
    url: `${HTTP_PREFIX}/workspaces/${wid}/waylines`,
    method: 'get',
    paramsSerializer: function (params) {
      return Qs.stringify(params, {arrayFormat: 'repeat'})
    },
    params: {
      ...params
    },
  })
}

// 新增航线
export const addWayline = (params) => {
  return request({
    url: `${HTTP_PREFIX}/WaylineDraw/createWayLineKmz`,
    method: 'post',
    data: params,
  }).data
}

// 修改航线
export const rebuildWayLine = (params, id) => {
  return request({
    url: `${HTTP_PREFIX}/WaylineDraw/reBuildWayLine?id=${id}`,
    method: 'post',
    data: params,
  }).data
}

// 删除航线
export function deleteWaylineFile(workspaceId, waylineId) {
  return request({
    url: `${HTTP_PREFIX}/workspaces/${workspaceId}/waylines/${waylineId}`,
    method: 'delete',
  }).data
}

// 航线导入
export const importFirmareFile = (params) => {
  return request({
    url: `${HTTP_PREFIX}/workspaces/${workspaceId}/waylines/file/upload`,
    method: 'post',
    data: params,
  }).data
}

// 下载航线文件
export const downloadWaylineFile = (workspaceId, waylineId) => {
  return request({
    url: `/hztech-flight-core/media/api/v1/files/workspaces/${workspaceId}/waylines/${waylineId}/getInputStream`,
    method: 'get',
    responseType: 'blob',
    headers: {
      'content-type': 'application/octet-stream',
    },
  }).data
}

// 航线详情预览
export const getWayLineDetail = (id) => {
  return request({
    url: `${HTTP_PREFIX}/WaylineDraw/wayLineDetail?id=${id}`,
    method: 'get',
  })
}

// 航线距离时间计算接口
export const wayLineCount = (params) => {
  return request({
    url: `${HTTP_PREFIX}/WaylineDraw/wayLineCount`,
    method: 'post',
    data: params,
  }).data
}